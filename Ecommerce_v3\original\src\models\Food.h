#ifndef FOOD_H
#define FOOD_H

#include "Product.h"

class Food : public Product
{
public:
    Food(int id, const QString& name, double price, int stock,
         const QString& owner, double discount);

    Category getCategory() const override { return FoodCategory; }

    double getDiscountedPrice() const override
    {
        return m_price * m_discount;
    }

    QString getRemark() const override
    {
        // 食物目前无额外属性，备注留空
        return QString("");
    }

    QString toCsvString() const override;
};

#endif // FOOD_H
