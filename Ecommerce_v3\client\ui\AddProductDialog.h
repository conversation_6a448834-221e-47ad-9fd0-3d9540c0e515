#ifndef ADDPRODUCTDIALOG_H
#define ADDPRODUCTDIALOG_H

#include <QDialog>

class ClientManager;

namespace Ui {
class AddProductDialog;
}

class AddProductDialog : public QDialog
{
    Q_OBJECT

public:
    explicit AddProductDialog(ClientManager* clientMgr, QWidget *parent = nullptr);
    ~AddProductDialog();

private slots:
    void on_categoryCombo_currentIndexChanged(int index);
    void on_addButton_clicked();
    void on_cancelButton_clicked();
    void onSelectImageClicked();
    void onClearImageClicked();

private:
    void setupUI();
    void updateExtraFields();
    void updateImagePreview();

private:
    Ui::AddProductDialog *ui;
    ClientManager* m_clientMgr;
    QString m_selectedImagePath;
};

#endif // ADDPRODUCTDIALOG_H
