Ecommerce_v3/
├── common/                    # 共享模块
│   ├── Protocol.h
│   ├── Message.h
│   ├── Message.cpp
│   └── NetworkUtils.h
├── server/                    # 服务器端
│   ├── main.cpp
│   ├── network/
│   │   ├── Server.h
│   │   ├── Server.cpp
│   │   ├── ClientHandler.h
│   │   └── ClientHandler.cpp
│   ├── dispatcher/
│   │   ├── MessageDispatcher.h
│   │   └── MessageDispatcher.cpp
│   ├── managers/              # 复用现有管理器
│   │   ├── UserManager.h
│   │   ├── UserManager.cpp
│   │   ├── ProductManager.h
│   │   └── ProductManager.cpp
│   └── models/                # 复用现有模型
└── client/                    # 客户端
    ├── main.cpp
    ├── network/
    │   ├── Client.h
    │   ├── Client.cpp
    │   ├── ClientManager.h
    │   └── ClientManager.cpp
    ├── ui/                    # 复用现有UI
    │   ├── LoginDialog.h
    │   ├── LoginDialog.cpp
    │   ├── MainWindow.h
    │   ├── MainWindow.cpp
    │   └── ...
    └── models/                # 复用现有模型