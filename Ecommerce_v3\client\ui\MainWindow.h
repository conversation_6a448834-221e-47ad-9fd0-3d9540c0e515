#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QVector>
#include "../network/ClientManager.h"

namespace Ui {
class MainWindow;
}

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit MainWindow(ClientManager* clientMgr, QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    // 主页 Tab 的槽函数
    void on_refreshHomeButton_clicked();
    void on_searchHomeButton_clicked();
    void on_addToCartButton_clicked();
    
    // 购物车 Tab 的槽函数
    void on_generateOrderButton_clicked();
    void on_removeCartButton_clicked();
    void on_refreshCartButton_clicked();

    // 商品管理 Tab 的槽函数（仅商家可见）
    void on_addProductButton_clicked();
    void on_refreshProductButton_clicked();
    void on_updateProductButton_clicked();
    void on_batchDiscountButton_clicked();

    // 我的账户 Tab 的槽函数
    void on_refreshProfileButton_clicked();
    void on_rechargeButton_clicked();
    void on_changePwdButton_clicked();
    void on_profileButton_clicked();
    void on_logoutButton_clicked();
    
    // 客户端管理器信号槽
    void onLoginStatusChanged(bool loggedIn);
    void onBalanceChanged(double newBalance);
    void onDisconnected();

    // 购物车相关槽函数
    void onCartQuantityChanged(int value);

private:
    void setupUI();
    void setupButtonIcons();
    void applyStyles();
    void setupTabsForUserType();
    void loadHomeProducts();
    void updateHomeProductsTable();
    void loadCartProducts();
    void loadProductProducts();
    void loadProfileInfo();
    void updateBalanceDisplay();
    void updateAvatarDisplay();

private:
    Ui::MainWindow *ui;
    ClientManager* m_clientMgr;
    QVector<ClientProduct> m_currentProducts;
    QVector<CartItem> m_currentCart;
    QWidget* m_productTab; // 保存商品管理标签页的引用
};

#endif // MAINWINDOW_H
