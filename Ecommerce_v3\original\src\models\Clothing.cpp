#include "Clothing.h"
#include <QStringList>

Clothing::Clothing(int id, const QString& name, double price, int stock,
                   const QString& owner, double discount,
                   const QString& size, const QString& color)
    : Product(id, name, price, stock, owner, discount),
    m_size(size), m_color(color)
{
}

QString Clothing::toCsvString() const
{
    // 基类：id,name,price,stock,category,owner,discount
    QString base = Product::toCsvString();
    // 额外 append size, color
    return base + "," + m_size + "," + m_color;
}
