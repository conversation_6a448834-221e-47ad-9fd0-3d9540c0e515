#ifndef PROTOCOL_H
#define PROTOCOL_H

#include <QString>

namespace Protocol {
    // 请求类型常量
    namespace Request {
        const QString LOGIN = "login";
        const QString REGISTER = "register";
        const QString LOGOUT = "logout";
        const QString CHANGE_PASSWORD = "change_password";
        const QString RECHARGE_BALANCE = "recharge_balance";
        const QString UPDATE_PROFILE = "update_profile";
        const QString GET_USER_INFO = "get_user_info";
        
        const QString GET_ALL_PRODUCTS = "get_all_products";
        const QString SEARCH_PRODUCTS = "search_products";
        const QString FILTER_PRODUCTS = "filter_products";
        const QString ADD_PRODUCT = "add_product";
        const QString UPDATE_PRODUCT = "update_product";
        const QString UPDATE_PRODUCT_STOCK = "update_product_stock";
        const QString UPDATE_PRODUCT_PRICE = "update_product_price";
        const QString UPDATE_PRODUCT_DISCOUNT = "update_product_discount";
        const QString BATCH_UPDATE_DISCOUNT = "batch_update_discount";
        
        const QString ADD_TO_CART = "add_to_cart";
        const QString REMOVE_FROM_CART = "remove_from_cart";
        const QString GET_CART = "get_cart";
        const QString CLEAR_CART = "clear_cart";
        
        const QString RESERVE_ORDER = "reserve_order";  // 预订单（立即扣减库存）
        const QString CREATE_ORDER = "create_order";   // 确认订单（执行支付）
        const QString CANCEL_ORDER = "cancel_order";   // 取消订单（回滚库存）
        const QString GET_ORDERS = "get_orders";
        const QString PAY_ORDER = "pay_order";
    }
    
    // 响应状态码
    namespace Status {
        const int SUCCESS = 200;
        const int BAD_REQUEST = 400;
        const int UNAUTHORIZED = 401;
        const int NOT_FOUND = 404;
        const int INTERNAL_ERROR = 500;
    }
    
    // 错误消息
    namespace ErrorMessage {
        const QString INVALID_REQUEST = "无效的请求格式";
        const QString INVALID_CREDENTIALS = "用户名或密码错误";
        const QString USER_EXISTS = "用户名已存在";
        const QString USER_NOT_FOUND = "用户不存在";
        const QString INSUFFICIENT_BALANCE = "余额不足";
        const QString PRODUCT_NOT_FOUND = "商品不存在";
        const QString INSUFFICIENT_STOCK = "库存不足";
        const QString PERMISSION_DENIED = "权限不足";
        const QString SERVER_ERROR = "服务器内部错误";
    }
    
    // 成功消息
    namespace SuccessMessage {
        const QString LOGIN_SUCCESS = "登录成功";
        const QString REGISTER_SUCCESS = "注册成功";
        const QString LOGOUT_SUCCESS = "退出成功";
        const QString PASSWORD_CHANGED = "密码修改成功";
        const QString BALANCE_RECHARGED = "充值成功";
        const QString PROFILE_UPDATED = "个人信息更新成功";
        const QString PRODUCT_ADDED = "商品添加成功";
        const QString PRODUCT_UPDATED = "商品更新成功";
        const QString CART_UPDATED = "购物车更新成功";
        const QString ORDER_RESERVED = "订单预留成功";
        const QString ORDER_CREATED = "订单创建成功";
        const QString ORDER_CANCELLED = "订单取消成功";
        const QString PAYMENT_SUCCESS = "支付成功";
    }
}

#endif // PROTOCOL_H
