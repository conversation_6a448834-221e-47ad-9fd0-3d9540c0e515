/********************************************************************************
** Form generated from reading UI file 'ProfileDialog.ui'
**
** Created by: Qt User Interface Compiler version 6.8.3
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_PROFILEDIALOG_H
#define UI_PROFILEDIALOG_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QDialog>
#include <QtWidgets/QFormLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QTextEdit>
#include <QtWidgets/QVBoxLayout>

QT_BEGIN_NAMESPACE

class Ui_ProfileDialog
{
public:
    QVBoxLayout *verticalLayout;
    QHBoxLayout *avatarLayout;
    QLabel *avatarTitleLabel;
    QLabel *avatarLabel;
    QVBoxLayout *avatarButtonLayout;
    QPushButton *selectAvatarButton;
    QSpacerItem *avatarSpacer;
    QSpacerItem *horizontalSpacer;
    QFormLayout *formLayout;
    QLabel *usernameTitle;
    QLabel *usernameLabel;
    QLabel *userTypeTitle;
    QLabel *userTypeLabel;
    QLabel *balanceTitle;
    QLabel *balanceLabel;
    QLabel *signatureTitle;
    QTextEdit *signatureTextEdit;
    QSpacerItem *verticalSpacer;
    QHBoxLayout *buttonLayout;
    QSpacerItem *buttonSpacer;
    QPushButton *updateButton;
    QPushButton *cancelButton;

    void setupUi(QDialog *ProfileDialog)
    {
        if (ProfileDialog->objectName().isEmpty())
            ProfileDialog->setObjectName("ProfileDialog");
        ProfileDialog->resize(400, 350);
        verticalLayout = new QVBoxLayout(ProfileDialog);
        verticalLayout->setSpacing(12);
        verticalLayout->setObjectName("verticalLayout");
        verticalLayout->setContentsMargins(12, 12, 12, 12);
        avatarLayout = new QHBoxLayout();
        avatarLayout->setObjectName("avatarLayout");
        avatarTitleLabel = new QLabel(ProfileDialog);
        avatarTitleLabel->setObjectName("avatarTitleLabel");
        avatarTitleLabel->setMinimumSize(QSize(60, 0));

        avatarLayout->addWidget(avatarTitleLabel);

        avatarLabel = new QLabel(ProfileDialog);
        avatarLabel->setObjectName("avatarLabel");
        avatarLabel->setMinimumSize(QSize(100, 100));
        avatarLabel->setMaximumSize(QSize(100, 100));
        avatarLabel->setAlignment(Qt::AlignCenter);

        avatarLayout->addWidget(avatarLabel);

        avatarButtonLayout = new QVBoxLayout();
        avatarButtonLayout->setObjectName("avatarButtonLayout");
        selectAvatarButton = new QPushButton(ProfileDialog);
        selectAvatarButton->setObjectName("selectAvatarButton");

        avatarButtonLayout->addWidget(selectAvatarButton);

        avatarSpacer = new QSpacerItem(20, 40, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        avatarButtonLayout->addItem(avatarSpacer);


        avatarLayout->addLayout(avatarButtonLayout);

        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        avatarLayout->addItem(horizontalSpacer);


        verticalLayout->addLayout(avatarLayout);

        formLayout = new QFormLayout();
        formLayout->setObjectName("formLayout");
        formLayout->setFieldGrowthPolicy(QFormLayout::ExpandingFieldsGrow);
        usernameTitle = new QLabel(ProfileDialog);
        usernameTitle->setObjectName("usernameTitle");

        formLayout->setWidget(0, QFormLayout::LabelRole, usernameTitle);

        usernameLabel = new QLabel(ProfileDialog);
        usernameLabel->setObjectName("usernameLabel");

        formLayout->setWidget(0, QFormLayout::FieldRole, usernameLabel);

        userTypeTitle = new QLabel(ProfileDialog);
        userTypeTitle->setObjectName("userTypeTitle");

        formLayout->setWidget(1, QFormLayout::LabelRole, userTypeTitle);

        userTypeLabel = new QLabel(ProfileDialog);
        userTypeLabel->setObjectName("userTypeLabel");

        formLayout->setWidget(1, QFormLayout::FieldRole, userTypeLabel);

        balanceTitle = new QLabel(ProfileDialog);
        balanceTitle->setObjectName("balanceTitle");

        formLayout->setWidget(2, QFormLayout::LabelRole, balanceTitle);

        balanceLabel = new QLabel(ProfileDialog);
        balanceLabel->setObjectName("balanceLabel");

        formLayout->setWidget(2, QFormLayout::FieldRole, balanceLabel);

        signatureTitle = new QLabel(ProfileDialog);
        signatureTitle->setObjectName("signatureTitle");

        formLayout->setWidget(3, QFormLayout::LabelRole, signatureTitle);

        signatureTextEdit = new QTextEdit(ProfileDialog);
        signatureTextEdit->setObjectName("signatureTextEdit");
        signatureTextEdit->setMaximumSize(QSize(16777215, 80));

        formLayout->setWidget(3, QFormLayout::FieldRole, signatureTextEdit);


        verticalLayout->addLayout(formLayout);

        verticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        verticalLayout->addItem(verticalSpacer);

        buttonLayout = new QHBoxLayout();
        buttonLayout->setObjectName("buttonLayout");
        buttonSpacer = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        buttonLayout->addItem(buttonSpacer);

        updateButton = new QPushButton(ProfileDialog);
        updateButton->setObjectName("updateButton");
        updateButton->setMinimumSize(QSize(80, 30));

        buttonLayout->addWidget(updateButton);

        cancelButton = new QPushButton(ProfileDialog);
        cancelButton->setObjectName("cancelButton");
        cancelButton->setMinimumSize(QSize(80, 30));

        buttonLayout->addWidget(cancelButton);


        verticalLayout->addLayout(buttonLayout);


        retranslateUi(ProfileDialog);

        QMetaObject::connectSlotsByName(ProfileDialog);
    } // setupUi

    void retranslateUi(QDialog *ProfileDialog)
    {
        ProfileDialog->setWindowTitle(QCoreApplication::translate("ProfileDialog", "\344\270\252\344\272\272\344\277\241\346\201\257\350\256\276\347\275\256", nullptr));
        avatarTitleLabel->setText(QCoreApplication::translate("ProfileDialog", "\345\244\264\345\203\217\357\274\232", nullptr));
        avatarLabel->setText(QCoreApplication::translate("ProfileDialog", "\346\227\240\345\244\264\345\203\217", nullptr));
        selectAvatarButton->setText(QCoreApplication::translate("ProfileDialog", "\351\200\211\346\213\251\345\244\264\345\203\217", nullptr));
        usernameTitle->setText(QCoreApplication::translate("ProfileDialog", "\347\224\250\346\210\267\345\220\215\357\274\232", nullptr));
        usernameLabel->setText(QCoreApplication::translate("ProfileDialog", "-", nullptr));
        userTypeTitle->setText(QCoreApplication::translate("ProfileDialog", "\347\224\250\346\210\267\347\261\273\345\236\213\357\274\232", nullptr));
        userTypeLabel->setText(QCoreApplication::translate("ProfileDialog", "-", nullptr));
        balanceTitle->setText(QCoreApplication::translate("ProfileDialog", "\350\264\246\346\210\267\344\275\231\351\242\235\357\274\232", nullptr));
        balanceLabel->setText(QCoreApplication::translate("ProfileDialog", "\357\277\2450.00", nullptr));
        signatureTitle->setText(QCoreApplication::translate("ProfileDialog", "\344\270\252\346\200\247\347\255\276\345\220\215\357\274\232", nullptr));
        signatureTextEdit->setPlaceholderText(QCoreApplication::translate("ProfileDialog", "\350\257\267\350\276\223\345\205\245\344\270\252\346\200\247\347\255\276\345\220\215...", nullptr));
        updateButton->setText(QCoreApplication::translate("ProfileDialog", "\346\233\264\346\226\260", nullptr));
        cancelButton->setText(QCoreApplication::translate("ProfileDialog", "\345\217\226\346\266\210", nullptr));
    } // retranslateUi

};

namespace Ui {
    class ProfileDialog: public Ui_ProfileDialog {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_PROFILEDIALOG_H
