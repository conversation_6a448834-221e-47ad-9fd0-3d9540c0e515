<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>AddProductDialog</class>
 <widget class="QDialog" name="AddProductDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>450</width>
    <height>400</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>添加商品</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>8</number>
   </property>
   <property name="leftMargin">
    <number>12</number>
   </property>
   <property name="topMargin">
    <number>12</number>
   </property>
   <property name="rightMargin">
    <number>12</number>
   </property>
   <property name="bottomMargin">
    <number>12</number>
   </property>
   <item>
    <layout class="QFormLayout" name="formLayout">
     <property name="fieldGrowthPolicy">
      <enum>QFormLayout::ExpandingFieldsGrow</enum>
     </property>
     <item row="0" column="0">
      <widget class="QLabel" name="nameLabel">
       <property name="text">
        <string>商品名称：</string>
       </property>
      </widget>
     </item>
     <item row="0" column="1">
      <widget class="QLineEdit" name="nameLineEdit">
       <property name="placeholderText">
        <string>请输入商品名称</string>
       </property>
      </widget>
     </item>
     <item row="1" column="0">
      <widget class="QLabel" name="categoryLabel">
       <property name="text">
        <string>商品类别：</string>
       </property>
      </widget>
     </item>
     <item row="1" column="1">
      <widget class="QComboBox" name="categoryCombo"/>
     </item>
     <item row="2" column="0">
      <widget class="QLabel" name="priceLabel">
       <property name="text">
        <string>商品价格：</string>
       </property>
      </widget>
     </item>
     <item row="2" column="1">
      <widget class="QDoubleSpinBox" name="priceSpinBox">
       <property name="suffix">
        <string> 元</string>
       </property>
       <property name="decimals">
        <number>2</number>
       </property>
       <property name="minimum">
        <double>0.010000000000000</double>
       </property>
       <property name="maximum">
        <double>999999.989999999990687</double>
       </property>
      </widget>
     </item>
     <item row="3" column="0">
      <widget class="QLabel" name="stockLabel">
       <property name="text">
        <string>库存数量：</string>
       </property>
      </widget>
     </item>
     <item row="3" column="1">
      <widget class="QSpinBox" name="stockSpinBox">
       <property name="suffix">
        <string> 件</string>
       </property>
       <property name="maximum">
        <number>999999</number>
       </property>
      </widget>
     </item>
     <item row="4" column="0">
      <widget class="QLabel" name="discountLabel">
       <property name="text">
        <string>折扣：</string>
       </property>
      </widget>
     </item>
     <item row="4" column="1">
      <widget class="QDoubleSpinBox" name="discountSpinBox">
       <property name="decimals">
        <number>1</number>
       </property>
       <property name="minimum">
        <double>0.100000000000000</double>
       </property>
       <property name="maximum">
        <double>1.000000000000000</double>
       </property>
       <property name="singleStep">
        <double>0.100000000000000</double>
       </property>
       <property name="value">
        <double>1.000000000000000</double>
       </property>
      </widget>
     </item>
     <item row="5" column="0">
      <widget class="QLabel" name="extra1Label">
       <property name="text">
        <string>额外信息1：</string>
       </property>
      </widget>
     </item>
     <item row="5" column="1">
      <widget class="QLineEdit" name="extra1LineEdit"/>
     </item>
     <item row="6" column="0">
      <widget class="QLabel" name="extra2Label">
       <property name="text">
        <string>额外信息2：</string>
       </property>
      </widget>
     </item>
     <item row="6" column="1">
      <widget class="QLineEdit" name="extra2LineEdit"/>
     </item>
     <item row="7" column="0">
      <widget class="QLabel" name="imageLabel">
       <property name="text">
        <string>商品图片：</string>
       </property>
      </widget>
     </item>
     <item row="7" column="1">
      <layout class="QHBoxLayout" name="imageLayout">
       <item>
        <widget class="QLabel" name="imagePreviewLabel">
         <property name="minimumSize">
          <size>
           <width>64</width>
           <height>64</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>64</width>
           <height>64</height>
          </size>
         </property>
         <property name="text">
          <string>无图片</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
         <property name="styleSheet">
          <string>border: 1px solid #ccc;</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="selectImageButton">
         <property name="text">
          <string>选择图片</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="clearImageButton">
         <property name="text">
          <string>清除图片</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_image">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </item>
     <item row="8" column="0">
      <widget class="QLabel" name="remarkLabel">
       <property name="text">
        <string>商品备注：</string>
       </property>
      </widget>
     </item>
     <item row="8" column="1">
      <widget class="QTextEdit" name="remarkTextEdit">
       <property name="maximumSize">
        <size>
         <width>16777215</width>
         <height>80</height>
        </size>
       </property>
       <property name="placeholderText">
        <string>可选的商品描述信息</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <layout class="QHBoxLayout" name="buttonLayout">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="addButton">
       <property name="minimumSize">
        <size>
         <width>80</width>
         <height>30</height>
        </size>
       </property>
       <property name="text">
        <string>添加</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="cancelButton">
       <property name="minimumSize">
        <size>
         <width>80</width>
         <height>30</height>
        </size>
       </property>
       <property name="text">
        <string>取消</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
