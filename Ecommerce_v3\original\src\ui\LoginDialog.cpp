#include "LoginDialog.h"
#include "ui_LoginDialog.h"  // This file will be generated if you use Qt Designer. If not, recreate UI manually.
#include "RegisterDialog.h"
#include <QMessageBox>

LoginDialog::LoginDialog(UserManager* userMgr, QWidget *parent) :
    QDialog(parent),
    ui(new Ui::LoginDialog),
    m_userMgr(userMgr)
{
    ui->setupUi(this);
    setWindowTitle("E-Commerce Login");
}

LoginDialog::~LoginDialog()
{
    delete ui;
}

void LoginDialog::on_loginButton_clicked()
{
    QString username = ui->usernameLineEdit->text().trimmed();
    QString password = ui->passwordLineEdit->text();

    if (username.isEmpty() || password.isEmpty()) {
        QMessageBox::warning(this, "Input Error", "Username and password cannot be empty.");
        return;
    }

    User* u = m_userMgr->loginUser(username, password);
    if (!u) {
        QMessageBox::warning(this, "Login Failed", "Incorrect username or password.");
        return;
    }

    emit loginSuccess(u);
    accept();
}

void LoginDialog::on_registerButton_clicked()
{
    RegisterDialog dlg(m_userMgr, this);
    dlg.exec();
}
