[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\release", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\main.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/main.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\release", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\network\\Client.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/network/Client.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\release", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\network\\ClientManager.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/network/ClientManager.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\release", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\ui\\LoginDialog.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/ui/LoginDialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\release", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\ui\\RegisterDialog.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/ui/RegisterDialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\release", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\ui\\MainWindow.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/ui/MainWindow.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\release", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\ui\\AddProductDialog.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/ui/AddProductDialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\release", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\ui\\EditProductDialog.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/ui/EditProductDialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\release", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\ui\\ProfileDialog.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/ui/ProfileDialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\release", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\ui\\OrderDialog.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/ui/OrderDialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\release", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\ui\\IconManager.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/ui/IconManager.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\release", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\ui\\StyleManager.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/ui/StyleManager.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\release", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\ui\\ImageManager.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/ui/ImageManager.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\release", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\common\\Message.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/common/Message.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\release", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\common\\NetworkUtils.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/common/NetworkUtils.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\release", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\network\\Client.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/network/Client.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\release", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\network\\ClientManager.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/network/ClientManager.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\release", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\ui\\LoginDialog.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/ui/LoginDialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\release", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\ui\\RegisterDialog.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/ui/RegisterDialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\release", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\ui\\MainWindow.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/ui/MainWindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\release", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\ui\\AddProductDialog.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/ui/AddProductDialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\release", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\ui\\EditProductDialog.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/ui/EditProductDialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\release", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\ui\\ProfileDialog.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/ui/ProfileDialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\release", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\ui\\OrderDialog.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/ui/OrderDialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\release", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\ui\\IconManager.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/ui/IconManager.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\release", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\ui\\StyleManager.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/ui/StyleManager.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\release", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\ui\\ImageManager.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/ui/ImageManager.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\release", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\common\\Protocol.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/common/Protocol.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\release", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\common\\Message.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/common/Message.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\release", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\common\\NetworkUtils.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/common/NetworkUtils.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\release", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\ui_RegisterDialog.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/ui_RegisterDialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\release", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\ui_ProfileDialog.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/ui_ProfileDialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\release", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\ui_LoginDialog.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/ui_LoginDialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\release", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\ui_EditProductDialog.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/ui_EditProductDialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\release", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\ui_MainWindow.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/ui_MainWindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\release", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\ui_OrderDialog.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/ui_OrderDialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\release", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\client\\ui_AddProductDialog.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/ui_AddProductDialog.h"}]