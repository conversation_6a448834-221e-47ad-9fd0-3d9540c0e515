#include <QApplication>
#include <QDebug>
#include <QMessageBox>
#include "network/ClientManager.h"
#include "ui/LoginDialog.h"
#include "ui/MainWindow.h"

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    qInfo() << "=== E-Commerce Client Starting ===";
    
    // 创建客户端管理器
    ClientManager clientManager;
    
    // 连接到服务器
    QString serverHost = "localhost";
    quint16 serverPort = 8888;
    
    // 从命令行参数获取服务器地址（可选）
    if (argc > 1) {
        serverHost = QString(argv[1]);
    }
    if (argc > 2) {
        bool ok;
        quint16 customPort = QString(argv[2]).toUShort(&ok);
        if (ok && customPort > 0) {
            serverPort = customPort;
        }
    }
    
    qInfo() << "Connecting to server:" << serverHost << ":" << serverPort;
    
    if (!clientManager.connectToServer(serverHost, serverPort)) {
        QMessageBox::critical(nullptr, "连接错误", 
                             QString("无法连接到服务器 %1:%2\n请确保服务器正在运行。")
                             .arg(serverHost).arg(serverPort));
        return -1;
    }
    
    qInfo() << "Connected to server successfully.";
    
    // 显示登录对话框
    LoginDialog loginDialog(&clientManager);
    
    if (loginDialog.exec() != QDialog::Accepted) {
        qInfo() << "User cancelled login. Exiting.";
        return 0;
    }
    
    // 登录成功，显示主窗口
    MainWindow mainWindow(&clientManager);
    mainWindow.show();
    
    qInfo() << "=== E-Commerce Client Started Successfully ===";
    qInfo() << "Current user:" << clientManager.getCurrentUsername();
    qInfo() << "User type:" << clientManager.getCurrentUserType();
    
    // 运行事件循环
    int result = app.exec();
    
    qInfo() << "=== E-Commerce Client Shutting Down ===";
    
    // 断开连接
    clientManager.disconnectFromServer();
    
    qInfo() << "=== E-Commerce Client Stopped ===";
    
    return result;
}
