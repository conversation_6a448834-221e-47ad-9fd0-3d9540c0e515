/********************************************************************************
** Form generated from reading UI file 'EditProfileDialog.ui'
**
** Created by: Qt User Interface Compiler version 6.8.3
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_EDITPROFILEDIALOG_H
#define UI_EDITPROFILEDIALOG_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QDialog>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QTextEdit>
#include <QtWidgets/QVBoxLayout>

QT_BEGIN_NAMESPACE

class Ui_EditProfileDialog
{
public:
    QVBoxLayout *verticalLayout_dialog;
    QHBoxLayout *horizontalLayout_avatar;
    QLabel *avatarLabel;
    QPushButton *changeAvatarButton;
    QHBoxLayout *horizontalLayout_username;
    QLabel *labelUsername;
    QLineEdit *usernameLineEdit;
    QHBoxLayout *horizontalLayout_oldPwd;
    QLabel *labelOldPwd;
    QLineEdit *oldPwdLineEdit;
    QHBoxLayout *horizontalLayout_newPwd;
    QLabel *labelNewPwd;
    QLineEdit *passwordLineEdit;
    QHBoxLayout *horizontalLayout_signature;
    QLabel *labelSignature;
    QTextEdit *signatureTextEdit;
    QHBoxLayout *horizontalLayout_buttons;
    QSpacerItem *horizontalSpacer_buttonLeft;
    QPushButton *saveButton;
    QPushButton *cancelButton;
    QSpacerItem *horizontalSpacer_buttonRight;

    void setupUi(QDialog *EditProfileDialog)
    {
        if (EditProfileDialog->objectName().isEmpty())
            EditProfileDialog->setObjectName("EditProfileDialog");
        EditProfileDialog->resize(400, 350);
        verticalLayout_dialog = new QVBoxLayout(EditProfileDialog);
        verticalLayout_dialog->setObjectName("verticalLayout_dialog");
        horizontalLayout_avatar = new QHBoxLayout();
        horizontalLayout_avatar->setSpacing(10);
        horizontalLayout_avatar->setObjectName("horizontalLayout_avatar");
        avatarLabel = new QLabel(EditProfileDialog);
        avatarLabel->setObjectName("avatarLabel");
        avatarLabel->setMinimumSize(QSize(64, 64));
        avatarLabel->setMaximumSize(QSize(64, 64));
        avatarLabel->setScaledContents(true);
        avatarLabel->setPixmap(QPixmap(QString::fromUtf8(":/default_avatar.png")));

        horizontalLayout_avatar->addWidget(avatarLabel);

        changeAvatarButton = new QPushButton(EditProfileDialog);
        changeAvatarButton->setObjectName("changeAvatarButton");
        changeAvatarButton->setMinimumSize(QSize(100, 30));

        horizontalLayout_avatar->addWidget(changeAvatarButton);


        verticalLayout_dialog->addLayout(horizontalLayout_avatar);

        horizontalLayout_username = new QHBoxLayout();
        horizontalLayout_username->setSpacing(10);
        horizontalLayout_username->setObjectName("horizontalLayout_username");
        labelUsername = new QLabel(EditProfileDialog);
        labelUsername->setObjectName("labelUsername");
        labelUsername->setMinimumSize(QSize(80, 0));

        horizontalLayout_username->addWidget(labelUsername);

        usernameLineEdit = new QLineEdit(EditProfileDialog);
        usernameLineEdit->setObjectName("usernameLineEdit");

        horizontalLayout_username->addWidget(usernameLineEdit);


        verticalLayout_dialog->addLayout(horizontalLayout_username);

        horizontalLayout_oldPwd = new QHBoxLayout();
        horizontalLayout_oldPwd->setSpacing(10);
        horizontalLayout_oldPwd->setObjectName("horizontalLayout_oldPwd");
        labelOldPwd = new QLabel(EditProfileDialog);
        labelOldPwd->setObjectName("labelOldPwd");
        labelOldPwd->setMinimumSize(QSize(80, 0));

        horizontalLayout_oldPwd->addWidget(labelOldPwd);

        oldPwdLineEdit = new QLineEdit(EditProfileDialog);
        oldPwdLineEdit->setObjectName("oldPwdLineEdit");
        oldPwdLineEdit->setEchoMode(QLineEdit::Password);

        horizontalLayout_oldPwd->addWidget(oldPwdLineEdit);


        verticalLayout_dialog->addLayout(horizontalLayout_oldPwd);

        horizontalLayout_newPwd = new QHBoxLayout();
        horizontalLayout_newPwd->setSpacing(10);
        horizontalLayout_newPwd->setObjectName("horizontalLayout_newPwd");
        labelNewPwd = new QLabel(EditProfileDialog);
        labelNewPwd->setObjectName("labelNewPwd");
        labelNewPwd->setMinimumSize(QSize(80, 0));

        horizontalLayout_newPwd->addWidget(labelNewPwd);

        passwordLineEdit = new QLineEdit(EditProfileDialog);
        passwordLineEdit->setObjectName("passwordLineEdit");
        passwordLineEdit->setEchoMode(QLineEdit::Password);

        horizontalLayout_newPwd->addWidget(passwordLineEdit);


        verticalLayout_dialog->addLayout(horizontalLayout_newPwd);

        horizontalLayout_signature = new QHBoxLayout();
        horizontalLayout_signature->setSpacing(10);
        horizontalLayout_signature->setObjectName("horizontalLayout_signature");
        labelSignature = new QLabel(EditProfileDialog);
        labelSignature->setObjectName("labelSignature");
        labelSignature->setMinimumSize(QSize(80, 0));

        horizontalLayout_signature->addWidget(labelSignature);

        signatureTextEdit = new QTextEdit(EditProfileDialog);
        signatureTextEdit->setObjectName("signatureTextEdit");
        signatureTextEdit->setMinimumHeight(60);

        horizontalLayout_signature->addWidget(signatureTextEdit);


        verticalLayout_dialog->addLayout(horizontalLayout_signature);

        horizontalLayout_buttons = new QHBoxLayout();
        horizontalLayout_buttons->setObjectName("horizontalLayout_buttons");
        horizontalSpacer_buttonLeft = new QSpacerItem(0, 0, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_buttons->addItem(horizontalSpacer_buttonLeft);

        saveButton = new QPushButton(EditProfileDialog);
        saveButton->setObjectName("saveButton");
        saveButton->setMinimumSize(QSize(80, 30));

        horizontalLayout_buttons->addWidget(saveButton);

        cancelButton = new QPushButton(EditProfileDialog);
        cancelButton->setObjectName("cancelButton");
        cancelButton->setMinimumSize(QSize(80, 30));

        horizontalLayout_buttons->addWidget(cancelButton);

        horizontalSpacer_buttonRight = new QSpacerItem(0, 0, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_buttons->addItem(horizontalSpacer_buttonRight);


        verticalLayout_dialog->addLayout(horizontalLayout_buttons);


        retranslateUi(EditProfileDialog);

        QMetaObject::connectSlotsByName(EditProfileDialog);
    } // setupUi

    void retranslateUi(QDialog *EditProfileDialog)
    {
        EditProfileDialog->setWindowTitle(QCoreApplication::translate("EditProfileDialog", "\347\274\226\350\276\221\344\270\252\344\272\272\344\277\241\346\201\257", nullptr));
        changeAvatarButton->setText(QCoreApplication::translate("EditProfileDialog", "\346\233\264\346\215\242\345\244\264\345\203\217", nullptr));
        labelUsername->setText(QCoreApplication::translate("EditProfileDialog", "\347\224\250\346\210\267\345\220\215\357\274\232", nullptr));
        labelOldPwd->setText(QCoreApplication::translate("EditProfileDialog", "\346\227\247\345\257\206\347\240\201\357\274\232", nullptr));
        labelNewPwd->setText(QCoreApplication::translate("EditProfileDialog", "\346\226\260\345\257\206\347\240\201\357\274\232", nullptr));
        labelSignature->setText(QCoreApplication::translate("EditProfileDialog", "\344\270\252\346\200\247\347\255\276\345\220\215\357\274\232", nullptr));
        saveButton->setText(QCoreApplication::translate("EditProfileDialog", "\344\277\235\345\255\230", nullptr));
        cancelButton->setText(QCoreApplication::translate("EditProfileDialog", "\345\217\226\346\266\210", nullptr));
    } // retranslateUi

};

namespace Ui {
    class EditProfileDialog: public Ui_EditProfileDialog {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_EDITPROFILEDIALOG_H
