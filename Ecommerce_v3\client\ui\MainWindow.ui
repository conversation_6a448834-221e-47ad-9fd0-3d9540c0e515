<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1000</width>
    <height>700</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>电商交易平台</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <property name="styleSheet">
    <string>QWidget#centralwidget {
    background-image: url(:/images/background.jpg);
    background-repeat: no-repeat;
    background-position: center;
    background-attachment: fixed;
}</string>
   </property>
   <layout class="QVBoxLayout" name="verticalLayout_main">
    <item>
     <widget class="QTabWidget" name="tabs">
      <property name="styleSheet">
       <string>QTabWidget::pane {
    border: 2px solid #C0C0C0;
    border-radius: 8px;
    background-color: rgba(255, 255, 255, 0.9);
}

QTabBar::tab {
    background-color: rgba(240, 240, 240, 0.8);
    border: 1px solid #C0C0C0;
    border-bottom: none;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
    padding: 8px 16px;
    margin-right: 2px;
    font-weight: bold;
    font-size: 11pt;
}

QTabBar::tab:selected {
    background-color: rgba(255, 255, 255, 0.95);
    border-color: #4A90E2;
    color: #4A90E2;
}

QTabBar::tab:hover {
    background-color: rgba(250, 250, 250, 0.9);
}</string>
      </property>
      <property name="currentIndex">
       <number>0</number>
      </property>
      <widget class="QWidget" name="homeTab">
       <attribute name="title">
        <string>主页</string>
       </attribute>
       <layout class="QVBoxLayout" name="verticalLayout_home">
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_searchHome">
          <item>
           <widget class="QComboBox" name="categoryFilterCombo">
            <property name="minimumSize">
             <size>
              <width>100</width>
              <height>0</height>
             </size>
            </property>
            <item>
             <property name="text">
              <string>全部类型</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>图书</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>服装</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>食品</string>
             </property>
            </item>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="homeSearchLineEdit">
            <property name="placeholderText">
             <string>搜索商品名称...</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="searchHomeButton">
            <property name="styleSheet">
             <string>QPushButton {
    background-color: #4A90E2;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: bold;
    font-size: 10pt;
    min-width: 80px;
}

QPushButton:hover {
    background-color: #357ABD;
}

QPushButton:pressed {
    background-color: #2E5F8A;
}</string>
            </property>
            <property name="text">
             <string>搜索</string>
            </property>
            <property name="icon">
             <iconset resource="../../resources.qrc">
              <normaloff>:/icons/search.png</normaloff>
             </iconset>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="refreshHomeButton">
            <property name="styleSheet">
             <string>QPushButton {
    background-color: #5CB85C;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: bold;
    font-size: 10pt;
    min-width: 80px;
}

QPushButton:hover {
    background-color: #449D44;
}

QPushButton:pressed {
    background-color: #357A35;
}</string>
            </property>
            <property name="text">
             <string>刷新</string>
            </property>
            <property name="icon">
             <iconset resource="../../resources.qrc">
              <normaloff>:/icons/refresh.svg</normaloff>
             </iconset>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_searchHomeRight">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item>
         <widget class="QTableWidget" name="homeProductsTable">
          <property name="selectionBehavior">
           <enum>QAbstractItemView::SelectRows</enum>
          </property>
          <property name="sortingEnabled">
           <bool>true</bool>
          </property>
         </widget>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_homeButtons">
          <item>
           <spacer name="horizontalSpacer_homeLeft">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="addToCartButton">
            <property name="styleSheet">
             <string>QPushButton {
    background-color: #F0AD4E;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: bold;
    font-size: 10pt;
    min-width: 100px;
}

QPushButton:hover {
    background-color: #EC971F;
}

QPushButton:pressed {
    background-color: #D58512;
}</string>
            </property>
            <property name="text">
             <string>加入购物车</string>
            </property>
            <property name="icon">
             <iconset resource="../../resources.qrc">
              <normaloff>:/icons/cart.png</normaloff>
             </iconset>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_homeRight">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="cartTab">
       <attribute name="title">
        <string>购物车</string>
       </attribute>
       <layout class="QVBoxLayout" name="verticalLayout_cart">
        <item>
         <widget class="QTableWidget" name="cartTable">
          <property name="selectionBehavior">
           <enum>QAbstractItemView::SelectRows</enum>
          </property>
         </widget>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_cartButtons">
          <item>
           <spacer name="horizontalSpacer_cartLeft">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="refreshCartButton">
            <property name="styleSheet">
             <string>QPushButton {
    background-color: #5CB85C;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: bold;
    font-size: 10pt;
    min-width: 80px;
}

QPushButton:hover {
    background-color: #449D44;
}

QPushButton:pressed {
    background-color: #357A35;
}</string>
            </property>
            <property name="text">
             <string>刷新</string>
            </property>
            <property name="icon">
             <iconset resource="../../resources.qrc">
              <normaloff>:/icons/refresh.svg</normaloff>
             </iconset>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="generateOrderButton">
            <property name="styleSheet">
             <string>QPushButton {
    background-color: #4A90E2;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: bold;
    font-size: 10pt;
    min-width: 100px;
}

QPushButton:hover {
    background-color: #357ABD;
}

QPushButton:pressed {
    background-color: #2E5F8A;
}</string>
            </property>
            <property name="text">
             <string>生成订单</string>
            </property>
            <property name="icon">
             <iconset resource="../../resources.qrc">
              <normaloff>:/icons/order.svg</normaloff>
             </iconset>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="removeCartButton">
            <property name="styleSheet">
             <string>QPushButton {
    background-color: #D9534F;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: bold;
    font-size: 10pt;
    min-width: 100px;
}

QPushButton:hover {
    background-color: #C9302C;
}

QPushButton:pressed {
    background-color: #AC2925;
}</string>
            </property>
            <property name="text">
             <string>移除商品</string>
            </property>
            <property name="icon">
             <iconset resource="../../resources.qrc">
              <normaloff>:/icons/delete.svg</normaloff>
             </iconset>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_cartRight">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="productTab">
       <attribute name="title">
        <string>商品管理</string>
       </attribute>
       <layout class="QVBoxLayout" name="verticalLayout_product">
        <item>
         <widget class="QTableWidget" name="productProductsTable">
          <property name="selectionBehavior">
           <enum>QAbstractItemView::SelectRows</enum>
          </property>
         </widget>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_productButtons">
          <item>
           <spacer name="horizontalSpacer_productLeft">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="addProductButton">
            <property name="styleSheet">
             <string>QPushButton {
    background-color: #5CB85C;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: bold;
    font-size: 10pt;
    min-width: 100px;
}

QPushButton:hover {
    background-color: #449D44;
}

QPushButton:pressed {
    background-color: #357A35;
}</string>
            </property>
            <property name="text">
             <string>添加商品</string>
            </property>
            <property name="icon">
             <iconset resource="../../resources.qrc">
              <normaloff>:/icons/add.svg</normaloff>
             </iconset>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="refreshProductButton">
            <property name="styleSheet">
             <string>QPushButton {
    background-color: #5CB85C;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: bold;
    font-size: 10pt;
    min-width: 80px;
}

QPushButton:hover {
    background-color: #449D44;
}

QPushButton:pressed {
    background-color: #357A35;
}</string>
            </property>
            <property name="text">
             <string>刷新</string>
            </property>
            <property name="icon">
             <iconset resource="../../resources.qrc">
              <normaloff>:/icons/refresh.svg</normaloff>
             </iconset>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="updateProductButton">
            <property name="styleSheet">
             <string>QPushButton {
    background-color: #F0AD4E;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: bold;
    font-size: 10pt;
    min-width: 100px;
}

QPushButton:hover {
    background-color: #EC971F;
}

QPushButton:pressed {
    background-color: #D58512;
}</string>
            </property>
            <property name="text">
             <string>修改商品</string>
            </property>
            <property name="icon">
             <iconset resource="../../resources.qrc">
              <normaloff>:/icons/edit.svg</normaloff>
             </iconset>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="batchDiscountButton">
            <property name="styleSheet">
             <string>QPushButton {
    background-color: #5BC0DE;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: bold;
    font-size: 10pt;
    min-width: 100px;
}

QPushButton:hover {
    background-color: #31B0D5;
}

QPushButton:pressed {
    background-color: #269ABC;
}</string>
            </property>
            <property name="text">
             <string>批量折扣</string>
            </property>
            <property name="icon">
             <iconset resource="../../resources.qrc">
              <normaloff>:/icons/edit.svg</normaloff>
             </iconset>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_productRight">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="accountTab">
       <attribute name="title">
        <string>个人中心</string>
       </attribute>
       <layout class="QVBoxLayout" name="verticalLayout_account">
        <item>
         <layout class="QHBoxLayout" name="avatarLayout">
          <item>
           <widget class="QLabel" name="labelAvatar">
            <property name="font">
             <font>
              <pointsize>12</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="text">
             <string>头像：</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignVCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="avatarLabel">
            <property name="minimumSize">
             <size>
              <width>100</width>
              <height>100</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>100</width>
              <height>100</height>
             </size>
            </property>
            <property name="text">
             <string>头像</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
            <property name="styleSheet">
             <string>border: 1px solid gray;</string>
            </property>
            <property name="scaledContents">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="avatarSpacer">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QFormLayout" name="formLayout_account">
          <item row="0" column="0">
           <widget class="QLabel" name="labelUsername">
            <property name="font">
             <font>
              <pointsize>11</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="text">
             <string>用户名：</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QLabel" name="usernameLabel">
            <property name="font">
             <font>
              <pointsize>11</pointsize>
             </font>
            </property>
            <property name="text">
             <string>-</string>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QLabel" name="labelUserType">
            <property name="font">
             <font>
              <pointsize>11</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="text">
             <string>用户类型：</string>
            </property>
           </widget>
          </item>
          <item row="1" column="1">
           <widget class="QLabel" name="userTypeLabel">
            <property name="font">
             <font>
              <pointsize>11</pointsize>
             </font>
            </property>
            <property name="text">
             <string>-</string>
            </property>
           </widget>
          </item>
          <item row="2" column="0">
           <widget class="QLabel" name="labelBalance">
            <property name="font">
             <font>
              <pointsize>11</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="text">
             <string>账户余额：</string>
            </property>
           </widget>
          </item>
          <item row="2" column="1">
           <widget class="QLabel" name="balanceLabel">
            <property name="font">
             <font>
              <pointsize>11</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="styleSheet">
             <string>color: #2E8B57;</string>
            </property>
            <property name="text">
             <string>￥0.00</string>
            </property>
           </widget>
          </item>
          <item row="3" column="0">
           <widget class="QLabel" name="labelSignature">
            <property name="font">
             <font>
              <pointsize>11</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="text">
             <string>个性签名：</string>
            </property>
           </widget>
          </item>
          <item row="3" column="1">
           <widget class="QLabel" name="signatureLabel">
            <property name="font">
             <font>
              <pointsize>11</pointsize>
              <italic>true</italic>
             </font>
            </property>
            <property name="styleSheet">
             <string>color: #696969;</string>
            </property>
            <property name="text">
             <string>-</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <spacer name="verticalSpacer_accountTop">
          <property name="orientation">
           <enum>Qt::Vertical</enum>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>1</verstretch>
           </sizepolicy>
          </property>
          <property name="sizeHint">
           <size>
            <width>20</width>
            <height>40</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_accountButtons">
          <property name="spacing">
           <number>12</number>
          </property>
          <item>
           <spacer name="horizontalSpacer_accountLeft">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="refreshProfileButton">
            <property name="styleSheet">
             <string>QPushButton {
    background-color: #4A90E2;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: bold;
    font-size: 10pt;
    min-width: 80px;
}

QPushButton:hover {
    background-color: #357ABD;
}

QPushButton:pressed {
    background-color: #2E5F8A;
}</string>
            </property>
            <property name="text">
             <string>刷新</string>
            </property>
            <property name="icon">
             <iconset resource="../../resources.qrc">
              <normaloff>:/icons/refresh.svg</normaloff>
             </iconset>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="rechargeButton">
            <property name="styleSheet">
             <string>QPushButton {
    background-color: #5CB85C;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: bold;
    font-size: 10pt;
    min-width: 80px;
}

QPushButton:hover {
    background-color: #449D44;
}

QPushButton:pressed {
    background-color: #357A35;
}</string>
            </property>
            <property name="text">
             <string>充值</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="profileButton">
            <property name="styleSheet">
             <string>QPushButton {
    background-color: #F0AD4E;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: bold;
    font-size: 10pt;
    min-width: 80px;
}

QPushButton:hover {
    background-color: #EC971F;
}

QPushButton:pressed {
    background-color: #D58512;
}</string>
            </property>
            <property name="text">
             <string>个人信息</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="changePwdButton">
            <property name="styleSheet">
             <string>QPushButton {
    background-color: #5BC0DE;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: bold;
    font-size: 10pt;
    min-width: 80px;
}

QPushButton:hover {
    background-color: #31B0D5;
}

QPushButton:pressed {
    background-color: #269ABC;
}</string>
            </property>
            <property name="text">
             <string>修改密码</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="logoutButton">
            <property name="styleSheet">
             <string>QPushButton {
    background-color: #D9534F;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: bold;
    font-size: 10pt;
    min-width: 80px;
}

QPushButton:hover {
    background-color: #C9302C;
}

QPushButton:pressed {
    background-color: #AC2925;
}</string>
            </property>
            <property name="text">
             <string>退出登录</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_accountRight">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1000</width>
     <height>22</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>
