<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>OrderDialog</class>
 <widget class="QDialog" name="OrderDialog">
  <property name="geometry">
   <rect>
    <x>0</x><y>0</y><width>550</width><height>400</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>订单确认</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_order">
    <!-- 订单商品明细表格 -->
    <item>
     <widget class="QTableWidget" name="orderTable">
      <property name="columnCount"><number>6</number></property>
      <property name="rowCount"><number>0</number></property>
      <column><property name="text"><string>名称</string></property></column>
      <column><property name="text"><string>单价</string></property></column>
      <column><property name="text"><string>折后价</string></property></column>
      <column><property name="text"><string>数量</string></property></column>
      <column><property name="text"><string>小计</string></property></column>
      <column><property name="text"><string>商家</string></property></column>
     </widget>
    </item>

    <!-- 总金额 -->
    <item>
     <layout class="QHBoxLayout" name="horizontalLayout_total">
      <property name="spacing"><number>10</number></property>
      <item>
       <spacer name="horizontalSpacer_totalLeft">
        <property name="orientation"><enum>Qt::Horizontal</enum></property>
        <property name="sizeType"><enum>QSizePolicy::Expanding</enum></property>
       </spacer>
      </item>
      <item>
       <widget class="QLabel" name="labelTotal">
        <property name="text"><string>总金额: </string></property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="totalLabel">
        <property name="text"><string>0.00</string></property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_totalRight">
        <property name="orientation"><enum>Qt::Horizontal</enum></property>
        <property name="sizeType"><enum>QSizePolicy::Expanding</enum></property>
       </spacer>
      </item>
     </layout>
    </item>

    <!-- 确认/取消 按钮 -->
    <item>
     <layout class="QHBoxLayout" name="horizontalLayout_buttons">
      <item>
       <spacer name="horizontalSpacer_buttonsLeft">
        <property name="orientation"><enum>Qt::Horizontal</enum></property>
        <property name="sizeType"><enum>QSizePolicy::Expanding</enum></property>
       </spacer>
      </item>
      <item>
       <widget class="QPushButton" name="payButton">
        <property name="text"><string>支付</string></property>
        <property name="minimumSize"><size><width>80</width><height>30</height></size></property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="cancelButton">
        <property name="text"><string>取消</string></property>
        <property name="minimumSize"><size><width>80</width><height>30</height></size></property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_buttonsRight">
        <property name="orientation"><enum>Qt::Horizontal</enum></property>
        <property name="sizeType"><enum>QSizePolicy::Expanding</enum></property>
       </spacer>
      </item>
     </layout>
    </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
