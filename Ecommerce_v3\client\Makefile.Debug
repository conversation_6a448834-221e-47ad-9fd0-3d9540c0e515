#############################################################################
# Makefile for building: EcommerceClient
# Generated by qmake (3.1) (Qt 6.8.3)
# Project:  EcommerceClient.pro
# Template: app
#############################################################################

MAKEFILE      = Makefile.Debug

EQ            = =

####### Compiler, tools and options

CC            = gcc
CXX           = g++
DEFINES       = -DUNICODE -D_UNICODE -DWIN32 -DMINGW_HAS_SECURE_API=1 -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB -DQT_NEEDS_QMAIN
CFLAGS        = -fno-keep-inline-dllexport -g -Wall -Wextra -Wextra $(DEFINES)
CXXFLAGS      = -fno-keep-inline-dllexport -g -std=gnu++1z -Wall -Wextra -Wextra -fexceptions -mthreads $(DEFINES)
INCPATH       = -I. -ID:/software/Qt/6.8.3/mingw_64/include -ID:/software/Qt/6.8.3/mingw_64/include/QtWidgets -ID:/software/Qt/6.8.3/mingw_64/include/QtGui -ID:/software/Qt/6.8.3/mingw_64/include/QtNetwork -ID:/software/Qt/6.8.3/mingw_64/include/QtCore -Idebug -I. -I/include -ID:/software/Qt/6.8.3/mingw_64/mkspecs/win32-g++ 
LINKER      =        g++
LFLAGS        =        -Wl,-subsystem,windows -mthreads
LIBS        =        D:\software\Qt\6.8.3\mingw_64\lib\libQt6Widgets.a D:\software\Qt\6.8.3\mingw_64\lib\libQt6Gui.a D:\software\Qt\6.8.3\mingw_64\lib\libQt6Network.a D:\software\Qt\6.8.3\mingw_64\lib\libQt6Core.a -lmingw32 D:\software\Qt\6.8.3\mingw_64\lib\libQt6EntryPoint.a -lshell32  
QMAKE         = D:\software\Qt\6.8.3\mingw_64\bin\qmake.exe
DEL_FILE      = del
CHK_DIR_EXISTS= if not exist
MKDIR         = mkdir
COPY          = copy /y
COPY_FILE     = copy /y
COPY_DIR      = xcopy /s /q /y /i
INSTALL_FILE  = copy /y
INSTALL_PROGRAM = copy /y
INSTALL_DIR   = xcopy /s /q /y /i
QINSTALL      = D:\software\Qt\6.8.3\mingw_64\bin\qmake.exe -install qinstall
QINSTALL_PROGRAM = D:\software\Qt\6.8.3\mingw_64\bin\qmake.exe -install qinstall -exe
DEL_FILE      = del
SYMLINK       = $(QMAKE) -install ln -f -s
DEL_DIR       = rmdir
MOVE          = move
IDC           = idc
IDL           = midl
ZIP           = zip -r -9
DEF_FILE      = 
RES_FILE      = 
SED           = $(QMAKE) -install sed
MOVE          = move

####### Output directory

OBJECTS_DIR   = debug

####### Files

SOURCES       = main.cpp \
		network\Client.cpp \
		network\ClientManager.cpp \
		ui\LoginDialog.cpp \
		ui\RegisterDialog.cpp \
		ui\MainWindow.cpp \
		ui\AddProductDialog.cpp \
		ui\EditProductDialog.cpp \
		ui\ProfileDialog.cpp \
		ui\OrderDialog.cpp \
		ui\IconManager.cpp \
		ui\StyleManager.cpp \
		ui\ImageManager.cpp \
		..\common\Message.cpp \
		..\common\NetworkUtils.cpp debug\qrc_resources.cpp \
		debug\moc_Client.cpp \
		debug\moc_ClientManager.cpp \
		debug\moc_LoginDialog.cpp \
		debug\moc_RegisterDialog.cpp \
		debug\moc_MainWindow.cpp \
		debug\moc_AddProductDialog.cpp \
		debug\moc_EditProductDialog.cpp \
		debug\moc_ProfileDialog.cpp \
		debug\moc_OrderDialog.cpp
OBJECTS       = debug/main.o \
		debug/Client.o \
		debug/ClientManager.o \
		debug/LoginDialog.o \
		debug/RegisterDialog.o \
		debug/MainWindow.o \
		debug/AddProductDialog.o \
		debug/EditProductDialog.o \
		debug/ProfileDialog.o \
		debug/OrderDialog.o \
		debug/IconManager.o \
		debug/StyleManager.o \
		debug/ImageManager.o \
		debug/Message.o \
		debug/NetworkUtils.o \
		debug/qrc_resources.o \
		debug/moc_Client.o \
		debug/moc_ClientManager.o \
		debug/moc_LoginDialog.o \
		debug/moc_RegisterDialog.o \
		debug/moc_MainWindow.o \
		debug/moc_AddProductDialog.o \
		debug/moc_EditProductDialog.o \
		debug/moc_ProfileDialog.o \
		debug/moc_OrderDialog.o

DIST          =  network\Client.h \
		network\ClientManager.h \
		ui\LoginDialog.h \
		ui\RegisterDialog.h \
		ui\MainWindow.h \
		ui\AddProductDialog.h \
		ui\EditProductDialog.h \
		ui\ProfileDialog.h \
		ui\OrderDialog.h \
		ui\IconManager.h \
		ui\StyleManager.h \
		ui\ImageManager.h \
		..\common\Protocol.h \
		..\common\Message.h \
		..\common\NetworkUtils.h \
		../common/ClientProduct.h \
		../common/CartItem.h main.cpp \
		network\Client.cpp \
		network\ClientManager.cpp \
		ui\LoginDialog.cpp \
		ui\RegisterDialog.cpp \
		ui\MainWindow.cpp \
		ui\AddProductDialog.cpp \
		ui\EditProductDialog.cpp \
		ui\ProfileDialog.cpp \
		ui\OrderDialog.cpp \
		ui\IconManager.cpp \
		ui\StyleManager.cpp \
		ui\ImageManager.cpp \
		..\common\Message.cpp \
		..\common\NetworkUtils.cpp
QMAKE_TARGET  = EcommerceClient
DESTDIR        = debug\ #avoid trailing-slash linebreak
TARGET         = EcommerceClient.exe
DESTDIR_TARGET = debug\EcommerceClient.exe

####### Build rules

first: all
all: Makefile.Debug  debug/EcommerceClient.exe

debug/EcommerceClient.exe: D:/software/Qt/6.8.3/mingw_64/lib/libQt6Widgets.a D:/software/Qt/6.8.3/mingw_64/lib/libQt6Gui.a D:/software/Qt/6.8.3/mingw_64/lib/libQt6Network.a D:/software/Qt/6.8.3/mingw_64/lib/libQt6Core.a D:/software/Qt/6.8.3/mingw_64/lib/libQt6EntryPoint.a ui_LoginDialog.h ui_RegisterDialog.h ui_MainWindow.h ui_AddProductDialog.h ui_EditProductDialog.h ui_ProfileDialog.h ui_OrderDialog.h $(OBJECTS) 
	$(LINKER) $(LFLAGS) -o $(DESTDIR_TARGET) @debug\object_script.EcommerceClient.Debug $(LIBS)

qmake: FORCE
	@$(QMAKE) -o Makefile.Debug EcommerceClient.pro

qmake_all: FORCE

dist:
	$(ZIP) EcommerceClient.zip $(SOURCES) $(DIST) EcommerceClient.pro D:\software\Qt\6.8.3\mingw_64\mkspecs\features\spec_pre.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\device_config.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\common\sanitize.conf D:\software\Qt\6.8.3\mingw_64\mkspecs\common\gcc-base.conf D:\software\Qt\6.8.3\mingw_64\mkspecs\common\g++-base.conf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\win32\windows_vulkan_sdk.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\common\windows-vulkan.conf D:\software\Qt\6.8.3\mingw_64\mkspecs\common\g++-win32.conf D:\software\Qt\6.8.3\mingw_64\mkspecs\common\windows-desktop.conf D:\software\Qt\6.8.3\mingw_64\mkspecs\qconfig.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_ext_freetype.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_ext_libjpeg.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_ext_libpng.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_charts.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_charts_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_chartsqml.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_chartsqml_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_concurrent.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_concurrent_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_core.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_core_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_dbus.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_dbus_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_designer.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_designer_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_designercomponents_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_devicediscovery_support_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_entrypoint_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_example_icons_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_examples_asset_downloader_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_fb_support_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_ffmpegmediapluginimpl_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_freetype_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_gui.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_gui_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_harfbuzz_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_help.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_help_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_httpserver.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_httpserver_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_jpeg_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_labsanimation.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_labsanimation_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_labsfolderlistmodel.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_labsfolderlistmodel_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_labsplatform.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_labsplatform_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_labsqmlmodels.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_labsqmlmodels_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_labssettings.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_labssettings_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_labssharedimage.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_labssharedimage_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_labswavefrontmesh.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_labswavefrontmesh_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_linguist.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_multimedia.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_multimedia_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_multimediaquick_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_multimediatestlibprivate_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_multimediawidgets.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_multimediawidgets_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_network.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_network_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_opengl.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_opengl_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_openglwidgets.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_openglwidgets_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_packetprotocol_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_png_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_printsupport.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_printsupport_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qdoccatch_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qdoccatchconversions_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qdoccatchgenerators_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qml.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qml_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmlassetdownloader.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmlassetdownloader_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmlcompiler.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmlcompiler_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmlcore.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmlcore_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmldebug_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmldom_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmlintegration.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmlintegration_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmllocalstorage.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmllocalstorage_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmlls_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmlmeta.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmlmeta_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmlmodels.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmlmodels_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmlnetwork.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmlnetwork_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmltest.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmltest_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmltoolingsettings_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmltyperegistrar_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmlworkerscript.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmlworkerscript_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmlxmllistmodel.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmlxmllistmodel_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quick.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quick3dspatialaudio_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quick_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basic.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basic_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basicstyleimpl.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basicstyleimpl_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fluentwinui3styleimpl.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fluentwinui3styleimpl_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusion.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusion_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusionstyleimpl.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusionstyleimpl_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imagine.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imagine_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imaginestyleimpl.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imaginestyleimpl_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2impl.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2impl_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2material.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2material_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2materialstyleimpl.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2materialstyleimpl_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universal.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universal_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universalstyleimpl.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universalstyleimpl_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2windowsstyleimpl.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2windowsstyleimpl_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrolstestutilsprivate_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickdialogs2.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickdialogs2_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickdialogs2utils.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickdialogs2utils_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickeffects_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quicklayouts.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quicklayouts_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickparticles_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickshapes_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quicktemplates2.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quicktemplates2_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quicktestutilsprivate_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickvectorimage.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickvectorimage_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickvectorimagegenerator_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickwidgets.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickwidgets_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_serialport.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_serialport_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_spatialaudio.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_spatialaudio_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_sql.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_sql_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_svg.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_svg_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_svgwidgets.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_svgwidgets_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_testinternals_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_testlib.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_testlib_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_tools_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_uiplugin.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_uitools.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_uitools_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_websockets.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_websockets_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_widgets.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_widgets_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_xml.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_xml_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_zlib_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\features\qt_functions.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\qt_config.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\win32-g++\qmake.conf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\spec_post.prf .qmake.stash D:\software\Qt\6.8.3\mingw_64\mkspecs\features\exclusive_builds.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\toolchain.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\default_pre.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\win32\default_pre.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\resolve_config.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\exclusive_builds_post.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\default_post.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\build_pass.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\precompile_header.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\warn_on.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\permissions.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\qt.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\resources_functions.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\resources.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\moc.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\win32\opengl.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\uic.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\qmake_use.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\file_copies.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\win32\windows.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\testcase_targets.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\exceptions.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\yacc.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\lex.prf EcommerceClient.pro resources.qrc D:\software\Qt\6.8.3\mingw_64\lib\Qt6Widgets.prl D:\software\Qt\6.8.3\mingw_64\lib\Qt6Gui.prl D:\software\Qt\6.8.3\mingw_64\lib\Qt6Network.prl D:\software\Qt\6.8.3\mingw_64\lib\Qt6Core.prl D:\software\Qt\6.8.3\mingw_64\lib\Qt6EntryPoint.prl   resources.qrc D:\software\Qt\6.8.3\mingw_64\mkspecs\features\data\dummy.cpp network\Client.h network\ClientManager.h ui\LoginDialog.h ui\RegisterDialog.h ui\MainWindow.h ui\AddProductDialog.h ui\EditProductDialog.h ui\ProfileDialog.h ui\OrderDialog.h ui\IconManager.h ui\StyleManager.h ui\ImageManager.h ..\common\Protocol.h ..\common\Message.h ..\common\NetworkUtils.h ../common/ClientProduct.h ../common/CartItem.h  main.cpp network\Client.cpp network\ClientManager.cpp ui\LoginDialog.cpp ui\RegisterDialog.cpp ui\MainWindow.cpp ui\AddProductDialog.cpp ui\EditProductDialog.cpp ui\ProfileDialog.cpp ui\OrderDialog.cpp ui\IconManager.cpp ui\StyleManager.cpp ui\ImageManager.cpp ..\common\Message.cpp ..\common\NetworkUtils.cpp ui\LoginDialog.ui ui\RegisterDialog.ui ui\MainWindow.ui ui\AddProductDialog.ui ui\EditProductDialog.ui ui\ProfileDialog.ui ui\OrderDialog.ui    

clean: compiler_clean 
	-$(DEL_FILE) debug\main.o debug\Client.o debug\ClientManager.o debug\LoginDialog.o debug\RegisterDialog.o debug\MainWindow.o debug\AddProductDialog.o debug\EditProductDialog.o debug\ProfileDialog.o debug\OrderDialog.o debug\IconManager.o debug\StyleManager.o debug\ImageManager.o debug\Message.o debug\NetworkUtils.o debug\qrc_resources.o debug\moc_Client.o debug\moc_ClientManager.o debug\moc_LoginDialog.o debug\moc_RegisterDialog.o debug\moc_MainWindow.o debug\moc_AddProductDialog.o debug\moc_EditProductDialog.o debug\moc_ProfileDialog.o debug\moc_OrderDialog.o

distclean: clean 
	-$(DEL_FILE) .qmake.stash
	-$(DEL_FILE) $(DESTDIR_TARGET)
	-$(DEL_FILE) Makefile.Debug

mocclean: compiler_moc_header_clean compiler_moc_objc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_objc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_no_pch_compiler_make_all:
compiler_no_pch_compiler_clean:
compiler_rcc_make_all: debug/qrc_resources.cpp
compiler_rcc_clean:
	-$(DEL_FILE) debug\qrc_resources.cpp
debug/qrc_resources.cpp: resources.qrc \
		D:/software/Qt/6.8.3/mingw_64/bin/rcc.exe \
		images/default_clothing.jpg \
		images/default_book.jpg \
		images/background.jpg \
		images/default_food.jpg \
		avatars/default_avatar.png \
		icons/recharge.svg \
		icons/error.svg \
		icons/balance.png \
		icons/refresh.svg \
		icons/edit.svg \
		icons/delete.svg \
		icons/search.png \
		icons/password.png \
		icons/book.svg \
		icons/cart.png \
		icons/info.svg \
		icons/order.svg \
		icons/logout.png \
		icons/clothing.svg \
		icons/add.svg \
		icons/login.png \
		icons/profile.png \
		icons/warning.svg \
		icons/food.svg \
		icons/success.svg
	D:\software\Qt\6.8.3\mingw_64\bin\rcc.exe -name resources --no-zstd resources.qrc -o debug\qrc_resources.cpp

compiler_moc_predefs_make_all: debug/moc_predefs.h
compiler_moc_predefs_clean:
	-$(DEL_FILE) debug\moc_predefs.h
debug/moc_predefs.h: D:/software/Qt/6.8.3/mingw_64/mkspecs/features/data/dummy.cpp
	g++ -fno-keep-inline-dllexport -g -std=gnu++1z -Wall -Wextra -Wextra -dM -E -o debug\moc_predefs.h D:\software\Qt\6.8.3\mingw_64\mkspecs\features\data\dummy.cpp

compiler_moc_header_make_all: debug/moc_Client.cpp debug/moc_ClientManager.cpp debug/moc_LoginDialog.cpp debug/moc_RegisterDialog.cpp debug/moc_MainWindow.cpp debug/moc_AddProductDialog.cpp debug/moc_EditProductDialog.cpp debug/moc_ProfileDialog.cpp debug/moc_OrderDialog.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) debug\moc_Client.cpp debug\moc_ClientManager.cpp debug\moc_LoginDialog.cpp debug\moc_RegisterDialog.cpp debug\moc_MainWindow.cpp debug\moc_AddProductDialog.cpp debug\moc_EditProductDialog.cpp debug\moc_ProfileDialog.cpp debug\moc_OrderDialog.cpp
debug/moc_Client.cpp: network/Client.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnamespace.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversionchecks.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconfig.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcore-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcoreexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qprocessordetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsystemdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qassert.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtnoop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypes.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypeinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsysinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlogging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qflags.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasicatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qgenericatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qyieldcpu.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconstructormacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qforeach.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttypetraits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobalstatic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmalloc.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qminmax.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnumeric.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qoverload.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qswap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtresource.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttranslation.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qversiontagging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20type_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtmetamacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstring.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qchar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrefcount.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpair.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydataops.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qxptype_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20functional.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20memory.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringliteral.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qanystringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qutf8stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringbuilder.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhashfunctions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearraylist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringmatcher.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetatype.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatastream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevicebase.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfloat16.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmath.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetacontainer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopeguard.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbindingstorage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/QTcpSocket \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qtcpsocket.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qtnetworkglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qtnetwork-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qtnetworkexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qabstractsocket.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qhostaddress.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdebug.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtextstream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qset.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhash.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvariant.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q23utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QTimer \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasictimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QByteArray \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborcommon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatetime.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcalendar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlocale.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qregularexpression.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qurl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/quuid.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qendian.h \
		debug/moc_predefs.h \
		D:/software/Qt/6.8.3/mingw_64/bin/moc.exe
	D:\software\Qt\6.8.3\mingw_64\bin\moc.exe $(DEFINES) --include D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/debug/moc_predefs.h -ID:/software/Qt/6.8.3/mingw_64/mkspecs/win32-g++ -ID:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client -ID:/software/Qt/6.8.3/mingw_64/include -ID:/software/Qt/6.8.3/mingw_64/include/QtWidgets -ID:/software/Qt/6.8.3/mingw_64/include/QtGui -ID:/software/Qt/6.8.3/mingw_64/include/QtNetwork -ID:/software/Qt/6.8.3/mingw_64/include/QtCore -I"D:/VS Code/mingw/include/c++/13.2.0" -I"D:/VS Code/mingw/include/c++/13.2.0/x86_64-w64-mingw32" -I"D:/VS Code/mingw/include/c++/13.2.0/backward" -I"D:/VS Code/mingw/lib/gcc/x86_64-w64-mingw32/13.2.0/include" -I"D:/VS Code/mingw/lib/gcc/x86_64-w64-mingw32/13.2.0/include-fixed" -I"D:/VS Code/mingw/x86_64-w64-mingw32/include" network\Client.h -o debug\moc_Client.cpp

debug/moc_ClientManager.cpp: network/ClientManager.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnamespace.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversionchecks.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconfig.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcore-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcoreexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qprocessordetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsystemdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qassert.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtnoop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypes.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypeinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsysinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlogging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qflags.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasicatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qgenericatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qyieldcpu.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconstructormacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qforeach.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttypetraits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobalstatic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmalloc.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qminmax.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnumeric.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qoverload.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qswap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtresource.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttranslation.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qversiontagging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20type_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtmetamacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstring.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qchar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrefcount.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpair.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydataops.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qxptype_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20functional.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20memory.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringliteral.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qanystringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qutf8stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringbuilder.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhashfunctions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearraylist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringmatcher.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetatype.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatastream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevicebase.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfloat16.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmath.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetacontainer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopeguard.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbindingstorage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborcommon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdebug.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtextstream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qset.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhash.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatetime.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcalendar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlocale.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvariant.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q23utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qregularexpression.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qurl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/quuid.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qendian.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonArray \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonarray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QVector \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvector.h \
		debug/moc_predefs.h \
		D:/software/Qt/6.8.3/mingw_64/bin/moc.exe
	D:\software\Qt\6.8.3\mingw_64\bin\moc.exe $(DEFINES) --include D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/debug/moc_predefs.h -ID:/software/Qt/6.8.3/mingw_64/mkspecs/win32-g++ -ID:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client -ID:/software/Qt/6.8.3/mingw_64/include -ID:/software/Qt/6.8.3/mingw_64/include/QtWidgets -ID:/software/Qt/6.8.3/mingw_64/include/QtGui -ID:/software/Qt/6.8.3/mingw_64/include/QtNetwork -ID:/software/Qt/6.8.3/mingw_64/include/QtCore -I"D:/VS Code/mingw/include/c++/13.2.0" -I"D:/VS Code/mingw/include/c++/13.2.0/x86_64-w64-mingw32" -I"D:/VS Code/mingw/include/c++/13.2.0/backward" -I"D:/VS Code/mingw/lib/gcc/x86_64-w64-mingw32/13.2.0/include" -I"D:/VS Code/mingw/lib/gcc/x86_64-w64-mingw32/13.2.0/include-fixed" -I"D:/VS Code/mingw/x86_64-w64-mingw32/include" network\ClientManager.h -o debug\moc_ClientManager.cpp

debug/moc_LoginDialog.cpp: ui/LoginDialog.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QDialog \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qdialog.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtguiglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversionchecks.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconfig.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcore-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcoreexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qprocessordetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsystemdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qassert.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtnoop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypes.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypeinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsysinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlogging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qflags.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasicatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qgenericatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qyieldcpu.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconstructormacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qforeach.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttypetraits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobalstatic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmalloc.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qminmax.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnumeric.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qoverload.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qswap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtresource.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttranslation.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qversiontagging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtgui-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtguiexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgets-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qwidget.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qwindowdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnamespace.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20type_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtmetamacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qwindowdefs_win.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstring.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qchar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrefcount.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpair.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydataops.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qxptype_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20functional.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20memory.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringliteral.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qanystringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qutf8stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringbuilder.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhashfunctions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearraylist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringmatcher.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetatype.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatastream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevicebase.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfloat16.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmath.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetacontainer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopeguard.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbindingstorage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmargins.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q23utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qaction.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qkeysequence.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qicon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsize.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpixmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpaintdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrect.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpoint.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qcolor.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qrgb.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qrgba64.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qimage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpixelformat.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtransform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpolygon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qregion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qspan.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20iterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qline.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvariant.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdebug.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtextstream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qset.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhash.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpalette.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qbrush.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfont.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qendian.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfontmetrics.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfontinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qsizepolicy.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qcursor.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qbitmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qurl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qeventpoint.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qvector2d.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qvectornd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpointingdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qinputdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qscreen.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QList \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QRect \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QSize \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QSizeF \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/QTransform \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnativeinterface.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qscreen_platform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qguiapplication.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreapplication.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qeventloop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreapplication_platform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfuture.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfutureinterface.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmutex.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtsan_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qresultstore.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfuture_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qthreadpool.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qthread.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrunnable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexception.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpromise.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qinputmethod.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlocale.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qguiapplication_platform.h \
		debug/moc_predefs.h \
		D:/software/Qt/6.8.3/mingw_64/bin/moc.exe
	D:\software\Qt\6.8.3\mingw_64\bin\moc.exe $(DEFINES) --include D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/debug/moc_predefs.h -ID:/software/Qt/6.8.3/mingw_64/mkspecs/win32-g++ -ID:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client -ID:/software/Qt/6.8.3/mingw_64/include -ID:/software/Qt/6.8.3/mingw_64/include/QtWidgets -ID:/software/Qt/6.8.3/mingw_64/include/QtGui -ID:/software/Qt/6.8.3/mingw_64/include/QtNetwork -ID:/software/Qt/6.8.3/mingw_64/include/QtCore -I"D:/VS Code/mingw/include/c++/13.2.0" -I"D:/VS Code/mingw/include/c++/13.2.0/x86_64-w64-mingw32" -I"D:/VS Code/mingw/include/c++/13.2.0/backward" -I"D:/VS Code/mingw/lib/gcc/x86_64-w64-mingw32/13.2.0/include" -I"D:/VS Code/mingw/lib/gcc/x86_64-w64-mingw32/13.2.0/include-fixed" -I"D:/VS Code/mingw/x86_64-w64-mingw32/include" ui\LoginDialog.h -o debug\moc_LoginDialog.cpp

debug/moc_RegisterDialog.cpp: ui/RegisterDialog.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QDialog \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qdialog.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtguiglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversionchecks.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconfig.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcore-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcoreexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qprocessordetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsystemdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qassert.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtnoop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypes.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypeinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsysinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlogging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qflags.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasicatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qgenericatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qyieldcpu.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconstructormacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qforeach.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttypetraits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobalstatic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmalloc.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qminmax.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnumeric.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qoverload.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qswap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtresource.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttranslation.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qversiontagging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtgui-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtguiexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgets-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qwidget.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qwindowdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnamespace.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20type_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtmetamacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qwindowdefs_win.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstring.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qchar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrefcount.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpair.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydataops.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qxptype_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20functional.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20memory.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringliteral.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qanystringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qutf8stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringbuilder.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhashfunctions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearraylist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringmatcher.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetatype.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatastream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevicebase.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfloat16.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmath.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetacontainer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopeguard.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbindingstorage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmargins.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q23utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qaction.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qkeysequence.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qicon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsize.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpixmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpaintdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrect.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpoint.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qcolor.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qrgb.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qrgba64.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qimage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpixelformat.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtransform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpolygon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qregion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qspan.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20iterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qline.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvariant.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdebug.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtextstream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qset.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhash.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpalette.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qbrush.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfont.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qendian.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfontmetrics.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfontinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qsizepolicy.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qcursor.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qbitmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qurl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qeventpoint.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qvector2d.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qvectornd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpointingdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qinputdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qscreen.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QList \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QRect \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QSize \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QSizeF \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/QTransform \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnativeinterface.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qscreen_platform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qguiapplication.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreapplication.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qeventloop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreapplication_platform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfuture.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfutureinterface.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmutex.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtsan_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qresultstore.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfuture_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qthreadpool.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qthread.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrunnable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexception.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpromise.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qinputmethod.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlocale.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qguiapplication_platform.h \
		debug/moc_predefs.h \
		D:/software/Qt/6.8.3/mingw_64/bin/moc.exe
	D:\software\Qt\6.8.3\mingw_64\bin\moc.exe $(DEFINES) --include D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/debug/moc_predefs.h -ID:/software/Qt/6.8.3/mingw_64/mkspecs/win32-g++ -ID:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client -ID:/software/Qt/6.8.3/mingw_64/include -ID:/software/Qt/6.8.3/mingw_64/include/QtWidgets -ID:/software/Qt/6.8.3/mingw_64/include/QtGui -ID:/software/Qt/6.8.3/mingw_64/include/QtNetwork -ID:/software/Qt/6.8.3/mingw_64/include/QtCore -I"D:/VS Code/mingw/include/c++/13.2.0" -I"D:/VS Code/mingw/include/c++/13.2.0/x86_64-w64-mingw32" -I"D:/VS Code/mingw/include/c++/13.2.0/backward" -I"D:/VS Code/mingw/lib/gcc/x86_64-w64-mingw32/13.2.0/include" -I"D:/VS Code/mingw/lib/gcc/x86_64-w64-mingw32/13.2.0/include-fixed" -I"D:/VS Code/mingw/x86_64-w64-mingw32/include" ui\RegisterDialog.h -o debug\moc_RegisterDialog.cpp

debug/moc_MainWindow.cpp: ui/MainWindow.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QMainWindow \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qmainwindow.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtguiglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversionchecks.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconfig.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcore-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcoreexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qprocessordetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsystemdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qassert.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtnoop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypes.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypeinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsysinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlogging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qflags.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasicatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qgenericatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qyieldcpu.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconstructormacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qforeach.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttypetraits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobalstatic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmalloc.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qminmax.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnumeric.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qoverload.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qswap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtresource.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttranslation.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qversiontagging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtgui-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtguiexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgets-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qwidget.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qwindowdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnamespace.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20type_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtmetamacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qwindowdefs_win.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstring.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qchar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrefcount.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpair.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydataops.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qxptype_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20functional.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20memory.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringliteral.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qanystringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qutf8stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringbuilder.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhashfunctions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearraylist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringmatcher.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetatype.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatastream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevicebase.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfloat16.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmath.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetacontainer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopeguard.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbindingstorage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmargins.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q23utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qaction.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qkeysequence.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qicon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsize.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpixmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpaintdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrect.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpoint.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qcolor.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qrgb.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qrgba64.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qimage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpixelformat.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtransform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpolygon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qregion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qspan.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20iterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qline.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvariant.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdebug.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtextstream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qset.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhash.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpalette.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qbrush.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfont.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qendian.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfontmetrics.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfontinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qsizepolicy.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qcursor.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qbitmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qurl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qeventpoint.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qvector2d.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qvectornd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpointingdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qinputdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qscreen.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QList \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QRect \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QSize \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QSizeF \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/QTransform \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnativeinterface.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qscreen_platform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qguiapplication.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreapplication.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qeventloop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreapplication_platform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfuture.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfutureinterface.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmutex.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtsan_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qresultstore.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfuture_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qthreadpool.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qthread.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrunnable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexception.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpromise.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qinputmethod.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlocale.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qguiapplication_platform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtabwidget.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QVector \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvector.h \
		network/ClientManager.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborcommon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatetime.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcalendar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qregularexpression.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/quuid.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonArray \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonarray.h \
		debug/moc_predefs.h \
		D:/software/Qt/6.8.3/mingw_64/bin/moc.exe
	D:\software\Qt\6.8.3\mingw_64\bin\moc.exe $(DEFINES) --include D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/debug/moc_predefs.h -ID:/software/Qt/6.8.3/mingw_64/mkspecs/win32-g++ -ID:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client -ID:/software/Qt/6.8.3/mingw_64/include -ID:/software/Qt/6.8.3/mingw_64/include/QtWidgets -ID:/software/Qt/6.8.3/mingw_64/include/QtGui -ID:/software/Qt/6.8.3/mingw_64/include/QtNetwork -ID:/software/Qt/6.8.3/mingw_64/include/QtCore -I"D:/VS Code/mingw/include/c++/13.2.0" -I"D:/VS Code/mingw/include/c++/13.2.0/x86_64-w64-mingw32" -I"D:/VS Code/mingw/include/c++/13.2.0/backward" -I"D:/VS Code/mingw/lib/gcc/x86_64-w64-mingw32/13.2.0/include" -I"D:/VS Code/mingw/lib/gcc/x86_64-w64-mingw32/13.2.0/include-fixed" -I"D:/VS Code/mingw/x86_64-w64-mingw32/include" ui\MainWindow.h -o debug\moc_MainWindow.cpp

debug/moc_AddProductDialog.cpp: ui/AddProductDialog.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QDialog \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qdialog.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtguiglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversionchecks.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconfig.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcore-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcoreexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qprocessordetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsystemdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qassert.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtnoop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypes.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypeinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsysinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlogging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qflags.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasicatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qgenericatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qyieldcpu.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconstructormacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qforeach.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttypetraits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobalstatic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmalloc.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qminmax.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnumeric.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qoverload.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qswap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtresource.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttranslation.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qversiontagging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtgui-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtguiexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgets-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qwidget.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qwindowdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnamespace.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20type_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtmetamacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qwindowdefs_win.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstring.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qchar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrefcount.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpair.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydataops.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qxptype_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20functional.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20memory.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringliteral.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qanystringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qutf8stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringbuilder.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhashfunctions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearraylist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringmatcher.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetatype.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatastream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevicebase.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfloat16.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmath.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetacontainer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopeguard.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbindingstorage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmargins.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q23utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qaction.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qkeysequence.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qicon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsize.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpixmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpaintdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrect.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpoint.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qcolor.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qrgb.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qrgba64.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qimage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpixelformat.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtransform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpolygon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qregion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qspan.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20iterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qline.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvariant.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdebug.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtextstream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qset.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhash.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpalette.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qbrush.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfont.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qendian.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfontmetrics.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfontinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qsizepolicy.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qcursor.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qbitmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qurl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qeventpoint.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qvector2d.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qvectornd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpointingdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qinputdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qscreen.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QList \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QRect \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QSize \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QSizeF \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/QTransform \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnativeinterface.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qscreen_platform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qguiapplication.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreapplication.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qeventloop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreapplication_platform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfuture.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfutureinterface.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmutex.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtsan_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qresultstore.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfuture_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qthreadpool.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qthread.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrunnable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexception.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpromise.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qinputmethod.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlocale.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qguiapplication_platform.h \
		debug/moc_predefs.h \
		D:/software/Qt/6.8.3/mingw_64/bin/moc.exe
	D:\software\Qt\6.8.3\mingw_64\bin\moc.exe $(DEFINES) --include D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/debug/moc_predefs.h -ID:/software/Qt/6.8.3/mingw_64/mkspecs/win32-g++ -ID:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client -ID:/software/Qt/6.8.3/mingw_64/include -ID:/software/Qt/6.8.3/mingw_64/include/QtWidgets -ID:/software/Qt/6.8.3/mingw_64/include/QtGui -ID:/software/Qt/6.8.3/mingw_64/include/QtNetwork -ID:/software/Qt/6.8.3/mingw_64/include/QtCore -I"D:/VS Code/mingw/include/c++/13.2.0" -I"D:/VS Code/mingw/include/c++/13.2.0/x86_64-w64-mingw32" -I"D:/VS Code/mingw/include/c++/13.2.0/backward" -I"D:/VS Code/mingw/lib/gcc/x86_64-w64-mingw32/13.2.0/include" -I"D:/VS Code/mingw/lib/gcc/x86_64-w64-mingw32/13.2.0/include-fixed" -I"D:/VS Code/mingw/x86_64-w64-mingw32/include" ui\AddProductDialog.h -o debug\moc_AddProductDialog.cpp

debug/moc_EditProductDialog.cpp: ui/EditProductDialog.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QDialog \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qdialog.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtguiglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversionchecks.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconfig.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcore-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcoreexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qprocessordetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsystemdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qassert.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtnoop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypes.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypeinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsysinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlogging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qflags.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasicatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qgenericatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qyieldcpu.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconstructormacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qforeach.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttypetraits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobalstatic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmalloc.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qminmax.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnumeric.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qoverload.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qswap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtresource.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttranslation.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qversiontagging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtgui-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtguiexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgets-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qwidget.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qwindowdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnamespace.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20type_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtmetamacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qwindowdefs_win.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstring.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qchar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrefcount.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpair.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydataops.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qxptype_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20functional.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20memory.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringliteral.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qanystringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qutf8stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringbuilder.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhashfunctions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearraylist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringmatcher.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetatype.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatastream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevicebase.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfloat16.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmath.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetacontainer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopeguard.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbindingstorage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmargins.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q23utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qaction.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qkeysequence.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qicon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsize.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpixmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpaintdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrect.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpoint.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qcolor.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qrgb.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qrgba64.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qimage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpixelformat.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtransform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpolygon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qregion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qspan.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20iterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qline.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvariant.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdebug.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtextstream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qset.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhash.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpalette.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qbrush.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfont.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qendian.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfontmetrics.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfontinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qsizepolicy.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qcursor.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qbitmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qurl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qeventpoint.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qvector2d.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qvectornd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpointingdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qinputdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qscreen.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QList \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QRect \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QSize \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QSizeF \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/QTransform \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnativeinterface.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qscreen_platform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qguiapplication.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreapplication.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qeventloop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreapplication_platform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfuture.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfutureinterface.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmutex.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtsan_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qresultstore.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfuture_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qthreadpool.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qthread.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrunnable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexception.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpromise.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qinputmethod.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlocale.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qguiapplication_platform.h \
		network/ClientManager.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborcommon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatetime.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcalendar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qregularexpression.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/quuid.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonArray \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonarray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QVector \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvector.h \
		debug/moc_predefs.h \
		D:/software/Qt/6.8.3/mingw_64/bin/moc.exe
	D:\software\Qt\6.8.3\mingw_64\bin\moc.exe $(DEFINES) --include D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/debug/moc_predefs.h -ID:/software/Qt/6.8.3/mingw_64/mkspecs/win32-g++ -ID:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client -ID:/software/Qt/6.8.3/mingw_64/include -ID:/software/Qt/6.8.3/mingw_64/include/QtWidgets -ID:/software/Qt/6.8.3/mingw_64/include/QtGui -ID:/software/Qt/6.8.3/mingw_64/include/QtNetwork -ID:/software/Qt/6.8.3/mingw_64/include/QtCore -I"D:/VS Code/mingw/include/c++/13.2.0" -I"D:/VS Code/mingw/include/c++/13.2.0/x86_64-w64-mingw32" -I"D:/VS Code/mingw/include/c++/13.2.0/backward" -I"D:/VS Code/mingw/lib/gcc/x86_64-w64-mingw32/13.2.0/include" -I"D:/VS Code/mingw/lib/gcc/x86_64-w64-mingw32/13.2.0/include-fixed" -I"D:/VS Code/mingw/x86_64-w64-mingw32/include" ui\EditProductDialog.h -o debug\moc_EditProductDialog.cpp

debug/moc_ProfileDialog.cpp: ui/ProfileDialog.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QDialog \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qdialog.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtguiglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversionchecks.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconfig.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcore-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcoreexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qprocessordetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsystemdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qassert.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtnoop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypes.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypeinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsysinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlogging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qflags.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasicatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qgenericatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qyieldcpu.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconstructormacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qforeach.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttypetraits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobalstatic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmalloc.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qminmax.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnumeric.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qoverload.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qswap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtresource.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttranslation.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qversiontagging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtgui-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtguiexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgets-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qwidget.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qwindowdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnamespace.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20type_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtmetamacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qwindowdefs_win.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstring.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qchar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrefcount.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpair.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydataops.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qxptype_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20functional.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20memory.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringliteral.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qanystringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qutf8stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringbuilder.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhashfunctions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearraylist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringmatcher.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetatype.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatastream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevicebase.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfloat16.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmath.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetacontainer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopeguard.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbindingstorage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmargins.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q23utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qaction.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qkeysequence.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qicon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsize.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpixmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpaintdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrect.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpoint.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qcolor.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qrgb.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qrgba64.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qimage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpixelformat.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtransform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpolygon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qregion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qspan.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20iterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qline.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvariant.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdebug.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtextstream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qset.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhash.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpalette.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qbrush.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfont.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qendian.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfontmetrics.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfontinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qsizepolicy.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qcursor.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qbitmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qurl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qeventpoint.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qvector2d.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qvectornd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpointingdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qinputdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qscreen.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QList \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QRect \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QSize \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QSizeF \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/QTransform \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnativeinterface.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qscreen_platform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qguiapplication.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreapplication.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qeventloop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreapplication_platform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfuture.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfutureinterface.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmutex.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtsan_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qresultstore.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfuture_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qthreadpool.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qthread.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrunnable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexception.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpromise.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qinputmethod.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlocale.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qguiapplication_platform.h \
		debug/moc_predefs.h \
		D:/software/Qt/6.8.3/mingw_64/bin/moc.exe
	D:\software\Qt\6.8.3\mingw_64\bin\moc.exe $(DEFINES) --include D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/debug/moc_predefs.h -ID:/software/Qt/6.8.3/mingw_64/mkspecs/win32-g++ -ID:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client -ID:/software/Qt/6.8.3/mingw_64/include -ID:/software/Qt/6.8.3/mingw_64/include/QtWidgets -ID:/software/Qt/6.8.3/mingw_64/include/QtGui -ID:/software/Qt/6.8.3/mingw_64/include/QtNetwork -ID:/software/Qt/6.8.3/mingw_64/include/QtCore -I"D:/VS Code/mingw/include/c++/13.2.0" -I"D:/VS Code/mingw/include/c++/13.2.0/x86_64-w64-mingw32" -I"D:/VS Code/mingw/include/c++/13.2.0/backward" -I"D:/VS Code/mingw/lib/gcc/x86_64-w64-mingw32/13.2.0/include" -I"D:/VS Code/mingw/lib/gcc/x86_64-w64-mingw32/13.2.0/include-fixed" -I"D:/VS Code/mingw/x86_64-w64-mingw32/include" ui\ProfileDialog.h -o debug\moc_ProfileDialog.cpp

debug/moc_OrderDialog.cpp: ui/OrderDialog.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QDialog \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qdialog.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtguiglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversionchecks.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconfig.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcore-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcoreexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qprocessordetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsystemdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qassert.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtnoop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypes.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypeinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsysinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlogging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qflags.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasicatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qgenericatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qyieldcpu.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconstructormacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qforeach.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttypetraits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobalstatic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmalloc.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qminmax.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnumeric.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qoverload.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qswap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtresource.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttranslation.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qversiontagging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtgui-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtguiexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgets-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qwidget.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qwindowdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnamespace.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20type_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtmetamacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qwindowdefs_win.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstring.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qchar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrefcount.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpair.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydataops.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qxptype_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20functional.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20memory.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringliteral.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qanystringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qutf8stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringbuilder.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhashfunctions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearraylist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringmatcher.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetatype.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatastream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevicebase.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfloat16.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmath.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetacontainer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopeguard.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbindingstorage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmargins.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q23utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qaction.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qkeysequence.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qicon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsize.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpixmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpaintdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrect.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpoint.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qcolor.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qrgb.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qrgba64.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qimage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpixelformat.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtransform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpolygon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qregion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qspan.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20iterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qline.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvariant.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdebug.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtextstream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qset.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhash.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpalette.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qbrush.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfont.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qendian.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfontmetrics.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfontinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qsizepolicy.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qcursor.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qbitmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qurl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qeventpoint.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qvector2d.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qvectornd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpointingdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qinputdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qscreen.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QList \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QRect \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QSize \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QSizeF \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/QTransform \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnativeinterface.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qscreen_platform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qguiapplication.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreapplication.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qeventloop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreapplication_platform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfuture.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfutureinterface.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmutex.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtsan_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qresultstore.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfuture_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qthreadpool.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qthread.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrunnable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexception.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpromise.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qinputmethod.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlocale.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qguiapplication_platform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QVector \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvector.h \
		network/ClientManager.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborcommon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatetime.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcalendar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qregularexpression.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/quuid.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonArray \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonarray.h \
		debug/moc_predefs.h \
		D:/software/Qt/6.8.3/mingw_64/bin/moc.exe
	D:\software\Qt\6.8.3\mingw_64\bin\moc.exe $(DEFINES) --include D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client/debug/moc_predefs.h -ID:/software/Qt/6.8.3/mingw_64/mkspecs/win32-g++ -ID:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/client -ID:/software/Qt/6.8.3/mingw_64/include -ID:/software/Qt/6.8.3/mingw_64/include/QtWidgets -ID:/software/Qt/6.8.3/mingw_64/include/QtGui -ID:/software/Qt/6.8.3/mingw_64/include/QtNetwork -ID:/software/Qt/6.8.3/mingw_64/include/QtCore -I"D:/VS Code/mingw/include/c++/13.2.0" -I"D:/VS Code/mingw/include/c++/13.2.0/x86_64-w64-mingw32" -I"D:/VS Code/mingw/include/c++/13.2.0/backward" -I"D:/VS Code/mingw/lib/gcc/x86_64-w64-mingw32/13.2.0/include" -I"D:/VS Code/mingw/lib/gcc/x86_64-w64-mingw32/13.2.0/include-fixed" -I"D:/VS Code/mingw/x86_64-w64-mingw32/include" ui\OrderDialog.h -o debug\moc_OrderDialog.cpp

compiler_moc_objc_header_make_all:
compiler_moc_objc_header_clean:
compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_uic_make_all: ui_LoginDialog.h ui_RegisterDialog.h ui_MainWindow.h ui_AddProductDialog.h ui_EditProductDialog.h ui_ProfileDialog.h ui_OrderDialog.h
compiler_uic_clean:
	-$(DEL_FILE) ui_LoginDialog.h ui_RegisterDialog.h ui_MainWindow.h ui_AddProductDialog.h ui_EditProductDialog.h ui_ProfileDialog.h ui_OrderDialog.h
ui_LoginDialog.h: ui/LoginDialog.ui \
		D:/software/Qt/6.8.3/mingw_64/bin/uic.exe
	D:\software\Qt\6.8.3\mingw_64\bin\uic.exe ui\LoginDialog.ui -o ui_LoginDialog.h

ui_RegisterDialog.h: ui/RegisterDialog.ui \
		D:/software/Qt/6.8.3/mingw_64/bin/uic.exe
	D:\software\Qt\6.8.3\mingw_64\bin\uic.exe ui\RegisterDialog.ui -o ui_RegisterDialog.h

ui_MainWindow.h: ui/MainWindow.ui \
		D:/software/Qt/6.8.3/mingw_64/bin/uic.exe
	D:\software\Qt\6.8.3\mingw_64\bin\uic.exe ui\MainWindow.ui -o ui_MainWindow.h

ui_AddProductDialog.h: ui/AddProductDialog.ui \
		D:/software/Qt/6.8.3/mingw_64/bin/uic.exe
	D:\software\Qt\6.8.3\mingw_64\bin\uic.exe ui\AddProductDialog.ui -o ui_AddProductDialog.h

ui_EditProductDialog.h: ui/EditProductDialog.ui \
		D:/software/Qt/6.8.3/mingw_64/bin/uic.exe
	D:\software\Qt\6.8.3\mingw_64\bin\uic.exe ui\EditProductDialog.ui -o ui_EditProductDialog.h

ui_ProfileDialog.h: ui/ProfileDialog.ui \
		D:/software/Qt/6.8.3/mingw_64/bin/uic.exe
	D:\software\Qt\6.8.3\mingw_64\bin\uic.exe ui\ProfileDialog.ui -o ui_ProfileDialog.h

ui_OrderDialog.h: ui/OrderDialog.ui \
		D:/software/Qt/6.8.3/mingw_64/bin/uic.exe
	D:\software\Qt\6.8.3\mingw_64\bin\uic.exe ui\OrderDialog.ui -o ui_OrderDialog.h

compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_rcc_clean compiler_moc_predefs_clean compiler_moc_header_clean compiler_uic_clean 



####### Compile

debug/main.o: main.cpp D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QApplication \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qapplication.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtguiglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversionchecks.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconfig.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcore-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcoreexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qprocessordetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsystemdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qassert.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtnoop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypes.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypeinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsysinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlogging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qflags.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasicatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qgenericatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qyieldcpu.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconstructormacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qforeach.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttypetraits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobalstatic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmalloc.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qminmax.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnumeric.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qoverload.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qswap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtresource.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttranslation.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qversiontagging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtgui-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtguiexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgets-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreapplication.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstring.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qchar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20type_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrefcount.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnamespace.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtmetamacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpair.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydataops.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qxptype_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20functional.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20memory.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringliteral.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qanystringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qutf8stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringbuilder.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetatype.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatastream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevicebase.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfloat16.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhashfunctions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmath.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetacontainer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopeguard.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qeventloop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearraylist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringmatcher.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbindingstorage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnativeinterface.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdebug.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtextstream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qset.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhash.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreapplication_platform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfuture.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfutureinterface.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmutex.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtsan_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qresultstore.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfuture_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qthreadpool.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qthread.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrunnable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexception.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpromise.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvariant.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q23utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qwindowdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qwindowdefs_win.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpoint.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsize.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmargins.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qcursor.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qbitmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpixmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpaintdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrect.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qcolor.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qrgb.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qrgba64.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qimage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpixelformat.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtransform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpolygon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qregion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qspan.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20iterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qline.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qguiapplication.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qinputmethod.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlocale.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qguiapplication_platform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QDebug \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QMessageBox \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qmessagebox.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qdialog.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qwidget.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qaction.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qkeysequence.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qicon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpalette.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qbrush.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfont.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qendian.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfontmetrics.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfontinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qsizepolicy.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qurl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qeventpoint.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qvector2d.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qvectornd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpointingdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qinputdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qscreen.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QList \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QRect \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QSize \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QSizeF \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/QTransform \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qscreen_platform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qdialogbuttonbox.h \
		network/ClientManager.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborcommon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatetime.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcalendar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qregularexpression.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/quuid.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonArray \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonarray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QVector \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvector.h \
		ui/LoginDialog.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QDialog \
		ui/MainWindow.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QMainWindow \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qmainwindow.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtabwidget.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\main.o main.cpp

debug/Client.o: network/Client.cpp network/Client.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnamespace.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversionchecks.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconfig.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcore-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcoreexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qprocessordetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsystemdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qassert.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtnoop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypes.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypeinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsysinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlogging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qflags.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasicatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qgenericatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qyieldcpu.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconstructormacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qforeach.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttypetraits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobalstatic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmalloc.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qminmax.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnumeric.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qoverload.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qswap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtresource.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttranslation.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qversiontagging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20type_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtmetamacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstring.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qchar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrefcount.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpair.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydataops.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qxptype_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20functional.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20memory.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringliteral.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qanystringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qutf8stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringbuilder.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhashfunctions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearraylist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringmatcher.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetatype.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatastream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevicebase.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfloat16.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmath.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetacontainer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopeguard.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbindingstorage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/QTcpSocket \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qtcpsocket.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qtnetworkglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qtnetwork-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qtnetworkexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qabstractsocket.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qhostaddress.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdebug.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtextstream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qset.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhash.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvariant.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q23utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QTimer \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasictimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QByteArray \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborcommon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatetime.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcalendar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlocale.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qregularexpression.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qurl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/quuid.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qendian.h \
		../common/Message.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QString \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonDocument \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsondocument.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonArray \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonarray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QDebug \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QEventLoop \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qeventloop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/QHostAddress
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\Client.o network\Client.cpp

debug/ClientManager.o: network/ClientManager.cpp network/ClientManager.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnamespace.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversionchecks.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconfig.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcore-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcoreexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qprocessordetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsystemdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qassert.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtnoop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypes.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypeinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsysinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlogging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qflags.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasicatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qgenericatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qyieldcpu.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconstructormacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qforeach.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttypetraits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobalstatic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmalloc.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qminmax.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnumeric.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qoverload.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qswap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtresource.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttranslation.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qversiontagging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20type_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtmetamacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstring.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qchar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrefcount.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpair.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydataops.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qxptype_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20functional.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20memory.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringliteral.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qanystringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qutf8stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringbuilder.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhashfunctions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearraylist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringmatcher.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetatype.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatastream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevicebase.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfloat16.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmath.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetacontainer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopeguard.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbindingstorage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborcommon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdebug.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtextstream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qset.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhash.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatetime.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcalendar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlocale.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvariant.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q23utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qregularexpression.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qurl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/quuid.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qendian.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonArray \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonarray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QVector \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvector.h \
		network/Client.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/QTcpSocket \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qtcpsocket.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qtnetworkglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qtnetwork-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qtnetworkexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qabstractsocket.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qhostaddress.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QTimer \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasictimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QByteArray \
		../common/Protocol.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QString \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QDebug
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\ClientManager.o network\ClientManager.cpp

debug/LoginDialog.o: ui/LoginDialog.cpp ui/LoginDialog.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QDialog \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qdialog.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtguiglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversionchecks.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconfig.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcore-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcoreexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qprocessordetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsystemdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qassert.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtnoop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypes.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypeinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsysinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlogging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qflags.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasicatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qgenericatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qyieldcpu.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconstructormacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qforeach.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttypetraits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobalstatic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmalloc.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qminmax.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnumeric.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qoverload.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qswap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtresource.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttranslation.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qversiontagging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtgui-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtguiexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgets-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qwidget.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qwindowdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnamespace.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20type_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtmetamacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qwindowdefs_win.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstring.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qchar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrefcount.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpair.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydataops.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qxptype_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20functional.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20memory.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringliteral.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qanystringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qutf8stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringbuilder.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhashfunctions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearraylist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringmatcher.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetatype.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatastream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevicebase.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfloat16.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmath.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetacontainer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopeguard.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbindingstorage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmargins.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q23utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qaction.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qkeysequence.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qicon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsize.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpixmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpaintdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrect.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpoint.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qcolor.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qrgb.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qrgba64.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qimage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpixelformat.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtransform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpolygon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qregion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qspan.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20iterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qline.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvariant.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdebug.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtextstream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qset.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhash.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpalette.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qbrush.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfont.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qendian.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfontmetrics.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfontinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qsizepolicy.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qcursor.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qbitmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qurl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qeventpoint.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qvector2d.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qvectornd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpointingdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qinputdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qscreen.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QList \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QRect \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QSize \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QSizeF \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/QTransform \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnativeinterface.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qscreen_platform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qguiapplication.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreapplication.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qeventloop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreapplication_platform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfuture.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfutureinterface.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmutex.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtsan_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qresultstore.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfuture_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qthreadpool.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qthread.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrunnable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexception.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpromise.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qinputmethod.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlocale.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qguiapplication_platform.h \
		ui_LoginDialog.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QVariant \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QApplication \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qapplication.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QHBoxLayout \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qboxlayout.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qlayout.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qlayoutitem.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qgridlayout.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QLabel \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qlabel.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qframe.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpicture.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtextdocument.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QLineEdit \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qlineedit.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtextcursor.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtextformat.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpen.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtextoption.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QPushButton \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qpushbutton.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qabstractbutton.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QSpacerItem \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QVBoxLayout \
		ui/RegisterDialog.h \
		ui/StyleManager.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QString \
		network/ClientManager.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborcommon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatetime.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcalendar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qregularexpression.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/quuid.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonArray \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonarray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QVector \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvector.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QMessageBox \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qmessagebox.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qdialogbuttonbox.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\LoginDialog.o ui\LoginDialog.cpp

debug/RegisterDialog.o: ui/RegisterDialog.cpp ui/RegisterDialog.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QDialog \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qdialog.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtguiglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversionchecks.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconfig.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcore-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcoreexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qprocessordetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsystemdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qassert.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtnoop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypes.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypeinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsysinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlogging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qflags.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasicatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qgenericatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qyieldcpu.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconstructormacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qforeach.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttypetraits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobalstatic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmalloc.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qminmax.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnumeric.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qoverload.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qswap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtresource.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttranslation.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qversiontagging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtgui-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtguiexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgets-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qwidget.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qwindowdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnamespace.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20type_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtmetamacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qwindowdefs_win.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstring.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qchar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrefcount.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpair.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydataops.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qxptype_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20functional.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20memory.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringliteral.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qanystringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qutf8stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringbuilder.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhashfunctions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearraylist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringmatcher.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetatype.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatastream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevicebase.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfloat16.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmath.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetacontainer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopeguard.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbindingstorage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmargins.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q23utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qaction.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qkeysequence.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qicon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsize.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpixmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpaintdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrect.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpoint.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qcolor.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qrgb.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qrgba64.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qimage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpixelformat.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtransform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpolygon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qregion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qspan.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20iterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qline.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvariant.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdebug.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtextstream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qset.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhash.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpalette.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qbrush.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfont.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qendian.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfontmetrics.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfontinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qsizepolicy.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qcursor.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qbitmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qurl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qeventpoint.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qvector2d.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qvectornd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpointingdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qinputdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qscreen.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QList \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QRect \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QSize \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QSizeF \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/QTransform \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnativeinterface.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qscreen_platform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qguiapplication.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreapplication.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qeventloop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreapplication_platform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfuture.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfutureinterface.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmutex.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtsan_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qresultstore.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfuture_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qthreadpool.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qthread.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrunnable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexception.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpromise.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qinputmethod.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlocale.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qguiapplication_platform.h \
		ui_RegisterDialog.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QVariant \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QApplication \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qapplication.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QComboBox \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qcombobox.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qabstractitemdelegate.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qstyleoption.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qabstractspinbox.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qvalidator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qregularexpression.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qslider.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qabstractslider.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qstyle.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtabbar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtabwidget.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qrubberband.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qframe.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qabstractitemmodel.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QHBoxLayout \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qboxlayout.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qlayout.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qlayoutitem.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qgridlayout.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QLabel \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qlabel.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpicture.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtextdocument.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QLineEdit \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qlineedit.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtextcursor.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtextformat.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpen.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtextoption.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QPushButton \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qpushbutton.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qabstractbutton.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QSpacerItem \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QVBoxLayout \
		network/ClientManager.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborcommon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatetime.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcalendar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/quuid.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonArray \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonarray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QVector \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvector.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QMessageBox \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qmessagebox.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qdialogbuttonbox.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\RegisterDialog.o ui\RegisterDialog.cpp

debug/MainWindow.o: ui/MainWindow.cpp ui/MainWindow.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QMainWindow \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qmainwindow.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtguiglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversionchecks.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconfig.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcore-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcoreexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qprocessordetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsystemdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qassert.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtnoop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypes.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypeinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsysinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlogging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qflags.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasicatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qgenericatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qyieldcpu.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconstructormacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qforeach.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttypetraits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobalstatic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmalloc.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qminmax.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnumeric.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qoverload.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qswap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtresource.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttranslation.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qversiontagging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtgui-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtguiexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgets-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qwidget.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qwindowdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnamespace.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20type_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtmetamacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qwindowdefs_win.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstring.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qchar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrefcount.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpair.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydataops.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qxptype_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20functional.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20memory.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringliteral.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qanystringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qutf8stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringbuilder.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhashfunctions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearraylist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringmatcher.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetatype.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatastream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevicebase.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfloat16.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmath.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetacontainer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopeguard.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbindingstorage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmargins.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q23utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qaction.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qkeysequence.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qicon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsize.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpixmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpaintdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrect.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpoint.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qcolor.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qrgb.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qrgba64.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qimage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpixelformat.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtransform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpolygon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qregion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qspan.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20iterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qline.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvariant.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdebug.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtextstream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qset.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhash.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpalette.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qbrush.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfont.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qendian.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfontmetrics.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfontinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qsizepolicy.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qcursor.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qbitmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qurl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qeventpoint.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qvector2d.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qvectornd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpointingdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qinputdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qscreen.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QList \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QRect \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QSize \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QSizeF \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/QTransform \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnativeinterface.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qscreen_platform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qguiapplication.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreapplication.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qeventloop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreapplication_platform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfuture.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfutureinterface.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmutex.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtsan_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qresultstore.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfuture_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qthreadpool.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qthread.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrunnable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexception.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpromise.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qinputmethod.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlocale.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qguiapplication_platform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtabwidget.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QVector \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvector.h \
		network/ClientManager.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborcommon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatetime.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcalendar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qregularexpression.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/quuid.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonArray \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonarray.h \
		ui_MainWindow.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QVariant \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/QIcon \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QApplication \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qapplication.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QComboBox \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qcombobox.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qabstractitemdelegate.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qstyleoption.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qabstractspinbox.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qvalidator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qslider.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qabstractslider.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qstyle.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtabbar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qrubberband.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qframe.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qabstractitemmodel.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QFormLayout \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qformlayout.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QLayout \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qlayout.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qlayoutitem.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qboxlayout.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qgridlayout.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QHBoxLayout \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QHeaderView \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qheaderview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qabstractitemview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qabstractscrollarea.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qitemselectionmodel.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QLabel \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qlabel.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpicture.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtextdocument.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QLineEdit \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qlineedit.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtextcursor.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtextformat.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpen.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtextoption.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QMenuBar \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qmenubar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qmenu.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QPushButton \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qpushbutton.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qabstractbutton.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QSpacerItem \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QStatusBar \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qstatusbar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QTabWidget \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QTableWidget \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtablewidget.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtableview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QVBoxLayout \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QWidget \
		ui/AddProductDialog.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QDialog \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qdialog.h \
		ui/EditProductDialog.h \
		ui/ProfileDialog.h \
		ui/OrderDialog.h \
		ui/LoginDialog.h \
		ui/IconManager.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/QPixmap \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QString \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QHash \
		ui/StyleManager.h \
		ui/ImageManager.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QMessageBox \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qmessagebox.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qdialogbuttonbox.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QInputDialog \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qinputdialog.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QTableWidgetItem \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QFile \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfile.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfiledevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QCheckBox \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qcheckbox.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QSpinBox \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qspinbox.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\MainWindow.o ui\MainWindow.cpp

debug/AddProductDialog.o: ui/AddProductDialog.cpp ui/AddProductDialog.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QDialog \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qdialog.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtguiglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversionchecks.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconfig.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcore-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcoreexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qprocessordetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsystemdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qassert.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtnoop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypes.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypeinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsysinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlogging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qflags.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasicatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qgenericatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qyieldcpu.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconstructormacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qforeach.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttypetraits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobalstatic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmalloc.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qminmax.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnumeric.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qoverload.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qswap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtresource.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttranslation.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qversiontagging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtgui-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtguiexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgets-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qwidget.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qwindowdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnamespace.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20type_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtmetamacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qwindowdefs_win.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstring.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qchar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrefcount.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpair.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydataops.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qxptype_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20functional.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20memory.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringliteral.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qanystringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qutf8stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringbuilder.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhashfunctions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearraylist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringmatcher.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetatype.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatastream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevicebase.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfloat16.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmath.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetacontainer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopeguard.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbindingstorage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmargins.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q23utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qaction.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qkeysequence.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qicon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsize.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpixmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpaintdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrect.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpoint.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qcolor.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qrgb.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qrgba64.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qimage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpixelformat.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtransform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpolygon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qregion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qspan.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20iterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qline.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvariant.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdebug.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtextstream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qset.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhash.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpalette.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qbrush.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfont.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qendian.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfontmetrics.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfontinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qsizepolicy.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qcursor.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qbitmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qurl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qeventpoint.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qvector2d.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qvectornd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpointingdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qinputdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qscreen.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QList \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QRect \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QSize \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QSizeF \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/QTransform \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnativeinterface.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qscreen_platform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qguiapplication.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreapplication.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qeventloop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreapplication_platform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfuture.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfutureinterface.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmutex.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtsan_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qresultstore.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfuture_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qthreadpool.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qthread.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrunnable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexception.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpromise.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qinputmethod.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlocale.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qguiapplication_platform.h \
		ui_AddProductDialog.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QVariant \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QApplication \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qapplication.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QComboBox \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qcombobox.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qabstractitemdelegate.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qstyleoption.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qabstractspinbox.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qvalidator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qregularexpression.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qslider.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qabstractslider.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qstyle.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtabbar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtabwidget.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qrubberband.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qframe.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qabstractitemmodel.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QDoubleSpinBox \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qspinbox.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QFormLayout \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qformlayout.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QLayout \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qlayout.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qlayoutitem.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qboxlayout.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qgridlayout.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QHBoxLayout \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QLabel \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qlabel.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpicture.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtextdocument.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QLineEdit \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qlineedit.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtextcursor.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtextformat.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpen.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtextoption.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QPushButton \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qpushbutton.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qabstractbutton.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QSpacerItem \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QSpinBox \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QTextEdit \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtextedit.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qabstractscrollarea.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QVBoxLayout \
		ui/ImageManager.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QString \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/QPixmap \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QHash \
		network/ClientManager.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborcommon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatetime.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcalendar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/quuid.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonArray \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonarray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QVector \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvector.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QFileDialog \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qfiledialog.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdir.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdirlisting.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfiledevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfile.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfileinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtimezone.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QMessageBox \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qmessagebox.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qdialogbuttonbox.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\AddProductDialog.o ui\AddProductDialog.cpp

debug/EditProductDialog.o: ui/EditProductDialog.cpp ui/EditProductDialog.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QDialog \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qdialog.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtguiglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversionchecks.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconfig.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcore-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcoreexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qprocessordetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsystemdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qassert.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtnoop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypes.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypeinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsysinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlogging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qflags.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasicatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qgenericatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qyieldcpu.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconstructormacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qforeach.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttypetraits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobalstatic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmalloc.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qminmax.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnumeric.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qoverload.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qswap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtresource.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttranslation.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qversiontagging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtgui-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtguiexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgets-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qwidget.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qwindowdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnamespace.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20type_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtmetamacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qwindowdefs_win.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstring.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qchar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrefcount.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpair.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydataops.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qxptype_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20functional.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20memory.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringliteral.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qanystringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qutf8stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringbuilder.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhashfunctions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearraylist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringmatcher.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetatype.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatastream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevicebase.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfloat16.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmath.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetacontainer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopeguard.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbindingstorage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmargins.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q23utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qaction.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qkeysequence.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qicon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsize.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpixmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpaintdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrect.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpoint.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qcolor.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qrgb.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qrgba64.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qimage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpixelformat.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtransform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpolygon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qregion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qspan.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20iterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qline.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvariant.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdebug.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtextstream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qset.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhash.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpalette.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qbrush.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfont.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qendian.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfontmetrics.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfontinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qsizepolicy.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qcursor.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qbitmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qurl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qeventpoint.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qvector2d.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qvectornd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpointingdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qinputdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qscreen.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QList \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QRect \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QSize \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QSizeF \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/QTransform \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnativeinterface.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qscreen_platform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qguiapplication.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreapplication.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qeventloop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreapplication_platform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfuture.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfutureinterface.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmutex.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtsan_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qresultstore.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfuture_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qthreadpool.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qthread.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrunnable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexception.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpromise.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qinputmethod.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlocale.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qguiapplication_platform.h \
		network/ClientManager.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborcommon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatetime.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcalendar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qregularexpression.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/quuid.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonArray \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonarray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QVector \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvector.h \
		ui_EditProductDialog.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QVariant \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QApplication \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qapplication.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QDoubleSpinBox \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qspinbox.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qabstractspinbox.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qvalidator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QFormLayout \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qformlayout.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QLayout \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qlayout.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qlayoutitem.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qboxlayout.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qgridlayout.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QHBoxLayout \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QLabel \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qlabel.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qframe.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpicture.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtextdocument.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QLineEdit \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qlineedit.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtextcursor.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtextformat.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpen.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtextoption.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QPushButton \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qpushbutton.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qabstractbutton.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QSpacerItem \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QSpinBox \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QTextEdit \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtextedit.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qabstractscrollarea.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QVBoxLayout \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QMessageBox \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qmessagebox.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qdialogbuttonbox.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\EditProductDialog.o ui\EditProductDialog.cpp

debug/ProfileDialog.o: ui/ProfileDialog.cpp ui/ProfileDialog.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QDialog \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qdialog.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtguiglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversionchecks.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconfig.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcore-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcoreexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qprocessordetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsystemdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qassert.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtnoop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypes.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypeinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsysinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlogging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qflags.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasicatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qgenericatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qyieldcpu.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconstructormacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qforeach.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttypetraits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobalstatic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmalloc.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qminmax.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnumeric.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qoverload.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qswap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtresource.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttranslation.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qversiontagging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtgui-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtguiexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgets-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qwidget.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qwindowdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnamespace.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20type_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtmetamacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qwindowdefs_win.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstring.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qchar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrefcount.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpair.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydataops.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qxptype_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20functional.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20memory.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringliteral.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qanystringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qutf8stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringbuilder.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhashfunctions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearraylist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringmatcher.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetatype.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatastream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevicebase.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfloat16.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmath.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetacontainer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopeguard.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbindingstorage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmargins.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q23utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qaction.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qkeysequence.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qicon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsize.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpixmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpaintdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrect.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpoint.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qcolor.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qrgb.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qrgba64.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qimage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpixelformat.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtransform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpolygon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qregion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qspan.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20iterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qline.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvariant.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdebug.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtextstream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qset.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhash.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpalette.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qbrush.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfont.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qendian.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfontmetrics.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfontinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qsizepolicy.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qcursor.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qbitmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qurl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qeventpoint.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qvector2d.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qvectornd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpointingdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qinputdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qscreen.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QList \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QRect \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QSize \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QSizeF \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/QTransform \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnativeinterface.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qscreen_platform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qguiapplication.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreapplication.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qeventloop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreapplication_platform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfuture.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfutureinterface.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmutex.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtsan_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qresultstore.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfuture_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qthreadpool.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qthread.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrunnable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexception.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpromise.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qinputmethod.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlocale.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qguiapplication_platform.h \
		ui_ProfileDialog.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QVariant \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QApplication \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qapplication.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QFormLayout \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qformlayout.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QLayout \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qlayout.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qlayoutitem.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qboxlayout.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qgridlayout.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QHBoxLayout \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QLabel \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qlabel.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qframe.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpicture.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtextdocument.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QPushButton \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qpushbutton.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qabstractbutton.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QSpacerItem \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QTextEdit \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtextedit.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qabstractscrollarea.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtextoption.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtextcursor.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtextformat.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpen.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QVBoxLayout \
		network/ClientManager.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborcommon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatetime.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcalendar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qregularexpression.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/quuid.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonArray \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonarray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QVector \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvector.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QMessageBox \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qmessagebox.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qdialogbuttonbox.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QFileDialog \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qfiledialog.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdir.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdirlisting.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfiledevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfile.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfileinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtimezone.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/QPixmap \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QStandardPaths \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstandardpaths.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\ProfileDialog.o ui\ProfileDialog.cpp

debug/OrderDialog.o: ui/OrderDialog.cpp ui/OrderDialog.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QDialog \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qdialog.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtguiglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversionchecks.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconfig.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcore-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcoreexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qprocessordetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsystemdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qassert.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtnoop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypes.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypeinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsysinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlogging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qflags.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasicatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qgenericatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qyieldcpu.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconstructormacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qforeach.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttypetraits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobalstatic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmalloc.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qminmax.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnumeric.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qoverload.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qswap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtresource.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttranslation.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qversiontagging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtgui-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtguiexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgets-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qwidget.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qwindowdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnamespace.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20type_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtmetamacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qwindowdefs_win.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstring.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qchar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrefcount.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpair.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydataops.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qxptype_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20functional.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20memory.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringliteral.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qanystringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qutf8stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringbuilder.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhashfunctions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearraylist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringmatcher.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetatype.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatastream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevicebase.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfloat16.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmath.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetacontainer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopeguard.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbindingstorage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmargins.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q23utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qaction.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qkeysequence.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qicon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsize.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpixmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpaintdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrect.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpoint.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qcolor.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qrgb.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qrgba64.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qimage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpixelformat.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtransform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpolygon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qregion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qspan.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20iterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qline.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvariant.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdebug.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtextstream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qset.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhash.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpalette.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qbrush.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfont.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qendian.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfontmetrics.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qfontinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qsizepolicy.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qcursor.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qbitmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qurl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qeventpoint.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qvector2d.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qvectornd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpointingdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qinputdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qscreen.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QList \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QRect \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QSize \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QSizeF \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/QTransform \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnativeinterface.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qscreen_platform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qguiapplication.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreapplication.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qeventloop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreapplication_platform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfuture.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfutureinterface.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmutex.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtsan_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qresultstore.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfuture_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qthreadpool.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qthread.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrunnable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexception.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpromise.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qinputmethod.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlocale.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qguiapplication_platform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QVector \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvector.h \
		network/ClientManager.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborcommon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatetime.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcalendar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qregularexpression.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/quuid.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonArray \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonarray.h \
		ui_OrderDialog.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QVariant \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QApplication \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qapplication.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QHBoxLayout \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qboxlayout.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qlayout.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qlayoutitem.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qgridlayout.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QHeaderView \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qheaderview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qabstractitemview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qabstractscrollarea.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qframe.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qabstractitemmodel.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qitemselectionmodel.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qabstractitemdelegate.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qstyleoption.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qabstractspinbox.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qvalidator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qslider.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qabstractslider.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qstyle.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtabbar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtabwidget.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qrubberband.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QLabel \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qlabel.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpicture.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtextdocument.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QPushButton \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qpushbutton.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qabstractbutton.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QSpacerItem \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QTableWidget \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtablewidget.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtableview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QVBoxLayout \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QMessageBox \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qmessagebox.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qdialogbuttonbox.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QTableWidgetItem \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QCheckBox \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qcheckbox.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QSpinBox \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qspinbox.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\OrderDialog.o ui\OrderDialog.cpp

debug/IconManager.o: ui/IconManager.cpp ui/IconManager.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/QIcon \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qicon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtguiglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversionchecks.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconfig.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcore-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcoreexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qprocessordetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsystemdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qassert.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtnoop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypes.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypeinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsysinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlogging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qflags.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasicatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qgenericatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qyieldcpu.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconstructormacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qforeach.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttypetraits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobalstatic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmalloc.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qminmax.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnumeric.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qoverload.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qswap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtresource.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttranslation.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qversiontagging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtgui-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtguiexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsize.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnamespace.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20type_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtmetamacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhashfunctions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstring.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qchar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrefcount.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpair.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydataops.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qxptype_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20functional.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20memory.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringliteral.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qanystringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qutf8stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringbuilder.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmargins.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q23utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearraylist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringmatcher.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpixmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpaintdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qwindowdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qwindowdefs_win.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrect.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpoint.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qcolor.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qrgb.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qrgba64.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qimage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpixelformat.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtransform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpolygon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qregion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatastream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevicebase.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qspan.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20iterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qline.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/QPixmap \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QString \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QHash \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhash.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QApplication \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qapplication.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgets-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreapplication.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetatype.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfloat16.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmath.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetacontainer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopeguard.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qeventloop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbindingstorage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnativeinterface.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdebug.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtextstream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qset.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreapplication_platform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfuture.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfutureinterface.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmutex.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtsan_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qresultstore.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfuture_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qthreadpool.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qthread.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrunnable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexception.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpromise.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvariant.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qcursor.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qbitmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qguiapplication.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qinputmethod.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlocale.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qguiapplication_platform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QDir \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdir.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdirlisting.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfiledevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatetime.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcalendar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfile.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfileinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtimezone.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QDebug
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\IconManager.o ui\IconManager.cpp

debug/StyleManager.o: ui/StyleManager.cpp ui/StyleManager.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QString \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstring.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qchar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversionchecks.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconfig.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcore-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcoreexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qprocessordetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsystemdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qassert.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtnoop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypes.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypeinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsysinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlogging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qflags.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasicatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qgenericatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qyieldcpu.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconstructormacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qforeach.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttypetraits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobalstatic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmalloc.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qminmax.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnumeric.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qoverload.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qswap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtresource.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttranslation.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qversiontagging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20type_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrefcount.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnamespace.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtmetamacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpair.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydataops.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qxptype_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20functional.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20memory.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringliteral.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qanystringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qutf8stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringbuilder.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter_base.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\StyleManager.o ui\StyleManager.cpp

debug/ImageManager.o: ui/ImageManager.cpp ui/ImageManager.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QString \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstring.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qchar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversionchecks.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconfig.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcore-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcoreexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qprocessordetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsystemdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qassert.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtnoop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypes.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypeinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsysinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlogging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qflags.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasicatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qgenericatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qyieldcpu.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconstructormacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qforeach.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttypetraits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobalstatic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmalloc.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qminmax.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnumeric.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qoverload.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qswap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtresource.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttranslation.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qversiontagging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20type_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrefcount.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnamespace.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtmetamacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpair.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydataops.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qxptype_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20functional.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20memory.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringliteral.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qanystringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qutf8stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringbuilder.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/QPixmap \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpixmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtguiglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtgui-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtguiexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpaintdevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qwindowdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qwindowdefs_win.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrect.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhashfunctions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmargins.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q23utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsize.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpoint.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qcolor.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qrgb.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearraylist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringmatcher.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qrgba64.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qimage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpixelformat.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qtransform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qpolygon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtGui/qregion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatastream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevicebase.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qspan.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20iterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qline.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QSize \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QHash \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhash.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QFile \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfile.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfiledevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetatype.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfloat16.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmath.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetacontainer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopeguard.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbindingstorage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QDir \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdir.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdirlisting.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatetime.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcalendar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlocale.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvariant.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdebug.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtextstream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qset.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfileinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtimezone.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QStandardPaths \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstandardpaths.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QDebug
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\ImageManager.o ui\ImageManager.cpp

debug/Message.o: ../common/Message.cpp ../common/Message.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QString \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstring.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qchar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversionchecks.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconfig.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcore-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcoreexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qprocessordetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsystemdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qassert.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtnoop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypes.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypeinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsysinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlogging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qflags.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasicatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qgenericatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qyieldcpu.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconstructormacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qforeach.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttypetraits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobalstatic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmalloc.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qminmax.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnumeric.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qoverload.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qswap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtresource.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttranslation.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qversiontagging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20type_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrefcount.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnamespace.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtmetamacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpair.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydataops.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qxptype_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20functional.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20memory.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringliteral.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qanystringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qutf8stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringbuilder.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborcommon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetatype.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatastream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevicebase.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfloat16.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhashfunctions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmath.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetacontainer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopeguard.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdebug.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtextstream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearraylist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringmatcher.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qset.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhash.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatetime.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcalendar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlocale.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvariant.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q23utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbindingstorage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qregularexpression.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qurl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/quuid.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qendian.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonDocument \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsondocument.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonArray \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonarray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonParseError \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QDebug
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\Message.o ..\common\Message.cpp

debug/NetworkUtils.o: ../common/NetworkUtils.cpp ../common/NetworkUtils.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QString \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstring.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qchar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversionchecks.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconfig.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcore-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcoreexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qprocessordetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsystemdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qassert.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtnoop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypes.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypeinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsysinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlogging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qflags.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasicatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qgenericatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qyieldcpu.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconstructormacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qforeach.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttypetraits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobalstatic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmalloc.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qminmax.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnumeric.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qoverload.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qswap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtresource.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttranslation.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qversiontagging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20type_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrefcount.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnamespace.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtmetamacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpair.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydataops.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qxptype_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20functional.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20memory.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringliteral.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qanystringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qutf8stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringbuilder.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborcommon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetatype.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatastream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevicebase.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfloat16.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhashfunctions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmath.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetacontainer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopeguard.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdebug.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtextstream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearraylist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringmatcher.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qset.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhash.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatetime.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcalendar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlocale.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvariant.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q23utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbindingstorage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qregularexpression.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qurl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/quuid.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qendian.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonArray \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonarray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QDateTime \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QDebug
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\NetworkUtils.o ..\common\NetworkUtils.cpp

debug/qrc_resources.o: debug/qrc_resources.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\qrc_resources.o debug\qrc_resources.cpp

debug/moc_Client.o: debug/moc_Client.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_Client.o debug\moc_Client.cpp

debug/moc_ClientManager.o: debug/moc_ClientManager.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_ClientManager.o debug\moc_ClientManager.cpp

debug/moc_LoginDialog.o: debug/moc_LoginDialog.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_LoginDialog.o debug\moc_LoginDialog.cpp

debug/moc_RegisterDialog.o: debug/moc_RegisterDialog.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_RegisterDialog.o debug\moc_RegisterDialog.cpp

debug/moc_MainWindow.o: debug/moc_MainWindow.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_MainWindow.o debug\moc_MainWindow.cpp

debug/moc_AddProductDialog.o: debug/moc_AddProductDialog.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_AddProductDialog.o debug\moc_AddProductDialog.cpp

debug/moc_EditProductDialog.o: debug/moc_EditProductDialog.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_EditProductDialog.o debug\moc_EditProductDialog.cpp

debug/moc_ProfileDialog.o: debug/moc_ProfileDialog.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_ProfileDialog.o debug\moc_ProfileDialog.cpp

debug/moc_OrderDialog.o: debug/moc_OrderDialog.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_OrderDialog.o debug\moc_OrderDialog.cpp

####### Install

install:  FORCE

uninstall:  FORCE

FORCE:

.SUFFIXES:

