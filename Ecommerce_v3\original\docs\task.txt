增加功能：
1. 购物车管理
支持用户向“购物车”添加、删除指定数量的商品，也支持消费者修改当前“购物车”商品的拟购买数量。
要求“购物车”中的商品数量每时每刻不低于0，但是可以超过库存数量。
至此MainWindow.ui应该包含主页、购物车、商品管理、我的账户。主页在每个商品前面加一个“添加”按钮，点击后提示输入添加的数量。
购物车页面，每个商品展示ID、名称、类别、原价、折后价、购买数量、商家、备注，并在每个商品前面加一个勾选框
2. 订单生成
勾选想生成订单的商品后点击“生成订单”，计算并显示订单总金额。
生成订单需要弹出一个订单窗口。
3. 错误场景处理：如果存在某一商品超过库存则订单生成失败，并提示库存不足。
订单成功生成时，订单处于未支付状态，应将对应数量的商品冻结，也就是需要先把对应商品的库存减去订单购买的数量。
如果交易成功再对商家和消费者的余额进行变化；如果交易失败则把对应商品的库存加回去，并提示余额不足