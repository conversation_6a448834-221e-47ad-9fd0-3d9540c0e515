# 商品图片路径持久化测试指南

## 测试目标
验证商品图片路径能够正确保存到products.csv文件中，并在重启服务器和客户端后正确加载显示。

## 修改内容总结

### 1. Product类修改
- **toCsvString()方法**：添加了imagePath字段到CSV输出
- **新的CSV格式**：`id,name,price,stock,category,owner,discount,imagePath,...`

### 2. ProductManager类修改
- **loadProducts()方法**：
  - 最少字段数从7个增加到8个
  - 添加了imagePath字段的读取
  - 更新了各子类构造函数调用，传递imagePath参数

### 3. 数据文件更新
- **products.csv**：为现有商品添加了默认图片路径`default_book.jpg`

## 测试场景

### 场景1：现有商品图片路径加载测试
**目标**：验证现有商品的图片路径能正确加载

**步骤**：
1. 启动服务器（应该能正确加载products.csv中的图片路径）
2. 启动客户端并登录
3. 查看商品列表，确认图片路径正确显示

**预期结果**：
- 服务器启动无错误
- 客户端能正确显示商品图片（或默认图片）
- 图片路径为`default_book.jpg`

### 场景2：新增商品图片路径持久化测试
**目标**：验证新添加的商品图片路径能正确保存和加载

**步骤**：
1. 登录商家账户
2. 添加一个新商品，设置自定义图片路径
3. 保存商品
4. 重启服务器和客户端
5. 再次查看商品列表

**预期结果**：
- 新商品能成功保存
- products.csv文件包含正确的图片路径
- 重启后图片路径正确加载

### 场景3：不同类型商品图片路径测试
**目标**：验证Book、Clothing、Food三种类型商品的图片路径都能正确处理

**步骤**：
1. 分别添加Book、Clothing、Food类型的商品
2. 为每种类型设置不同的图片路径
3. 保存并重启系统
4. 验证所有类型商品的图片路径

**预期结果**：
- 所有类型商品的图片路径都能正确保存
- CSV文件格式正确：
  - Book: `id,name,price,stock,0,owner,discount,imagePath,author`
  - Clothing: `id,name,price,stock,1,owner,discount,imagePath,size,color`
  - Food: `id,name,price,stock,2,owner,discount,imagePath`

## CSV文件格式验证

### 修改前的格式
```
id,name,price,stock,category,owner,discount,子类字段...
```

### 修改后的格式
```
id,name,price,stock,category,owner,discount,imagePath,子类字段...
```

### 示例数据
```csv
# Book类型商品
1,我的粪斗,114.00,504,0,田所浩二,0.10,default_book.jpg,野兽先辈

# Clothing类型商品（如果有）
3,T恤,50.00,100,1,商家A,1.00,default_clothing.jpg,L,红色

# Food类型商品（如果有）
4,苹果,5.00,200,2,商家B,0.90,default_food.jpg
```

## 错误排查

### 常见问题1：服务器启动失败
**可能原因**：CSV文件格式不匹配
**解决方案**：
- 检查products.csv文件是否包含足够的字段
- 确保每行至少有8个字段（基础字段+imagePath）

### 常见问题2：图片路径丢失
**可能原因**：toCsvString()方法未包含imagePath
**解决方案**：
- 检查Product::toCsvString()方法是否包含imagePath字段
- 确保子类正确调用基类方法

### 常见问题3：新商品保存失败
**可能原因**：构造函数参数不匹配
**解决方案**：
- 检查ProductManager::addProduct()方法
- 确保所有子类构造函数都支持imagePath参数

## 验证检查点

### 1. 服务器端验证
- [ ] 服务器启动无错误日志
- [ ] ProductManager能正确加载所有商品
- [ ] 商品对象包含正确的图片路径

### 2. 客户端验证
- [ ] 商品列表正确显示
- [ ] 图片路径字段不为空
- [ ] 默认图片能正确显示

### 3. 数据持久化验证
- [ ] products.csv文件格式正确
- [ ] 新增商品后文件正确更新
- [ ] 重启后数据完整保留

### 4. 功能完整性验证
- [ ] 商品添加功能正常
- [ ] 商品编辑功能正常
- [ ] 商品删除功能正常
- [ ] 图片上传功能正常（如果实现）

## 测试数据准备

### 测试用商品数据
```csv
1,测试图书,25.00,50,0,testuser,1.00,test_book.jpg,测试作者
2,测试服装,80.00,30,1,testuser,0.90,test_clothing.jpg,M,蓝色
3,测试食品,15.00,100,2,testuser,0.95,test_food.jpg
```

### 测试用图片文件
- `default_book.jpg`
- `default_clothing.jpg`
- `default_food.jpg`
- `test_book.jpg`
- `test_clothing.jpg`
- `test_food.jpg`

## 性能考虑

### 文件I/O性能
- CSV文件读写性能在小规模数据下表现良好
- 大规模数据建议考虑数据库存储

### 内存使用
- 图片路径作为字符串存储，内存开销较小
- 实际图片文件不在内存中，只存储路径引用

## 后续改进建议

### 1. 图片文件管理
- 实现图片文件的统一管理
- 添加图片文件存在性验证
- 支持图片文件的自动清理

### 2. 路径验证
- 添加图片路径格式验证
- 支持相对路径和绝对路径
- 添加默认图片回退机制

### 3. 用户体验优化
- 图片预览功能
- 图片上传进度显示
- 图片格式转换支持
