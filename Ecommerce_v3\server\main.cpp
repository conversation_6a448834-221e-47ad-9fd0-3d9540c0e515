#include <QCoreApplication>
#include <QDebug>
#include <QDir>
#include <QStandardPaths>
#include "network/Server.h"
#include "src/managers/UserManager.h"
#include "src/managers/ProductManager.h"

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    qInfo() << "=== E-Commerce Server Starting ===";
    
    // 设置数据文件路径
    QString dataDir = QDir::currentPath() + "/data";
    QDir().mkpath(dataDir);
    
    QString userFilePath = dataDir + "/users.csv";
    QString productFilePath = dataDir + "/products.csv";
    
    qInfo() << "Data directory:" << dataDir;
    qInfo() << "User file:" << userFilePath;
    qInfo() << "Product file:" << productFilePath;
    
    // 初始化用户管理器
    UserManager userManager(userFilePath);
    if (!userManager.loadUsers()) {
        qWarning() << "Failed to load users file, starting with empty user list.";
    } else {
        qInfo() << "Users loaded successfully.";
    }
    
    // 初始化商品管理器
    ProductManager::init(productFilePath);
    qInfo() << "Products loaded successfully.";
    
    // 创建并启动服务器
    Server server;
    server.setUserManager(&userManager);
    server.setProductManager(ProductManager::instance());
    
    quint16 port = 8888;
    if (argc > 1) {
        bool ok;
        quint16 customPort = QString(argv[1]).toUShort(&ok);
        if (ok && customPort > 0) {
            port = customPort;
        }
    }
    
    if (!server.startServer(port)) {
        qCritical() << "Failed to start server. Exiting.";
        ProductManager::cleanup();
        return -1;
    }
    
    qInfo() << "=== E-Commerce Server Started Successfully ===";
    qInfo() << "Server is listening on port:" << port;
    qInfo() << "Press Ctrl+C to stop the server.";
    
    // 运行事件循环
    int result = app.exec();
    
    qInfo() << "=== E-Commerce Server Shutting Down ===";
    
    // 清理资源
    server.stopServer();
    ProductManager::cleanup();
    
    qInfo() << "=== E-Commerce Server Stopped ===";
    
    return result;
}
