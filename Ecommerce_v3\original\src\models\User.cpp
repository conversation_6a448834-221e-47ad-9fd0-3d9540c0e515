// User.cpp

#include "User.h"
#include <QCryptographicHash>
#include <QStringList>

User::User(const QString& username,
           const QString& password,
           double balance,
           UserType type,
           const QString& signature,
           const QString& avatarPath)
    : m_username(username),
      m_balance(balance),
      m_type(type),
      m_signature(signature),
      m_avatarPath(avatarPath)
{
    // 如果 password 为空，则表明后续会通过 setPasswordHashFromFile() 载入已存的 hash
    if (!password.isEmpty()) {
        m_passwordHash = QString(QCryptographicHash::hash(
                                     password.toUtf8(),
                                     QCryptographicHash::Sha256).toHex());
    }
}

const QString& User::getUsername() const
{
    return m_username;
}

void User::setUsername(const QString& newName)
{
    m_username = newName;
}

bool User::checkPassword(const QString& pwd) const
{
    QString hash = QString(QCryptographicHash::hash(
                               pwd.toUtf8(),
                               QCryptographicHash::Sha256).toHex());
    return hash == m_passwordHash;
}

void User::setPassword(const QString& newPwd)
{
    m_passwordHash = QString(QCryptographicHash::hash(
                                 newPwd.toUtf8(),
                                 QCryptographicHash::Sha256).toHex());
}

double User::getBalance() const
{
    return m_balance;
}

void User::setBalance(double amount)
{
    m_balance = amount;
}

void User::setPasswordHashFromFile(const QString& hash)
{
    m_passwordHash = hash;
}

QString User::getSignature() const
{
    return m_signature;
}

void User::setSignature(const QString& signature)
{
    m_signature = signature;
}

QString User::getAvatarPath() const
{
    return m_avatarPath;
}

void User::setAvatarPath(const QString& path)
{
    m_avatarPath = path;
}

QString User::toCsvString() const
{
    // 先复制签名和头像到局部变量，再做 replace ，避免修改成员自身
    QString sig   = m_signature;
    QString avat  = m_avatarPath;
    sig.replace(",", "\\,");
    avat.replace(",", "\\,");

    QStringList parts;
    parts << m_username
          << m_passwordHash
          << QString::number(m_balance, 'f', 2)
          << QString::number(static_cast<int>(m_type))
          << sig
          << avat;
    return parts.join(",");
}
