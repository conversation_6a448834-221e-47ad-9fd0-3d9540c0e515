#include "OrderDialog.h"
#include "ui_OrderDialog.h"

// 也在 cpp 中重复包含 MainWindow.h，确保对 CartItem 的引用都能解析
#include "MainWindow.h"
#include <QMessageBox>

OrderDialog::OrderDialog(const QVector<CartItem>& selectedItems,
                         User* user,
                         UserManager* userMgr,
                         ProductManager* prodMgr,
                         QWidget *parent) :
    QDialog(parent),
    ui(new Ui::OrderDialog),
    m_items(selectedItems),
    m_user(user),
    m_userMgr(userMgr),
    m_prodMgr(prodMgr),
    m_totalAmount(0.0)
{
    ui->setupUi(this);
    setWindowTitle("订单确认");

    loadOrderTable();
    calcTotal();
}

OrderDialog::~OrderDialog()
{
    delete ui;
}

void OrderDialog::loadOrderTable()
{
    ui->orderTable->setRowCount(0);
    int row = 0;
    for (const CartItem &ci : m_items) {
        Product* p = ci.product;

        ui->orderTable->insertRow(row);

        // 0. 名称
        ui->orderTable->setItem(row, 0,
            new QTableWidgetItem(p->getName()));

        // 1. 单价（原价）
        ui->orderTable->setItem(row, 1,
            new QTableWidgetItem(QString::number(p->getOriginalPrice(), 'f', 2)));

        // 2. 折后价
        ui->orderTable->setItem(row, 2,
            new QTableWidgetItem(QString::number(p->getDiscountedPrice(), 'f', 2)));

        // 3. 数量
        ui->orderTable->setItem(row, 3,
            new QTableWidgetItem(QString::number(ci.quantity)));

        // 4. 小计 = 折后价 * 数量
        double sub = p->getDiscountedPrice() * ci.quantity;
        ui->orderTable->setItem(row, 4,
            new QTableWidgetItem(QString::number(sub, 'f', 2)));

        // 5. 商家
        ui->orderTable->setItem(row, 5,
            new QTableWidgetItem(p->getOwner()));

        ++row;
    }
}

void OrderDialog::calcTotal()
{
    m_totalAmount = 0.0;
    for (const CartItem &ci : m_items) {
        m_totalAmount += ci.product->getDiscountedPrice() * ci.quantity;
    }
    ui->totalLabel->setText(QString::number(m_totalAmount, 'f', 2));
}

void OrderDialog::on_payButton_clicked()
{
    // 检查消费者余额
    if (m_user->getBalance() < m_totalAmount) {
        QMessageBox::warning(this, "余额不足", "您的余额不足，请先充值。");
        reject();
        return;
    }

    // 扣库存：先保底减少库存，若后续失败再回滚（此处默认库存检查已在 MainWindow 中完成）
    for (const CartItem &ci : m_items) {
        Product* p = ci.product;
        int newStock = p->getStock() - ci.quantity;
        p->setStock(newStock);
    }
    m_prodMgr->saveProducts();

    // 从消费者余额扣款
    m_userMgr->deductBalance(m_user->getUsername(), m_totalAmount);

    // 计算每个商家的收入，并充值到商家账户
    QMap<QString, double> sellerIncome;
    for (const CartItem &ci : m_items) {
        QString sellerName = ci.product->getOwner();
        double inc = ci.product->getDiscountedPrice() * ci.quantity;
        sellerIncome[sellerName] += inc;
    }
    for (auto it = sellerIncome.constBegin(); it != sellerIncome.constEnd(); ++it) {
        m_userMgr->rechargeBalance(it.key(), it.value());
    }

    m_userMgr->saveUsers();
    accept();
}

void OrderDialog::on_cancelButton_clicked()
{
    reject();
}
