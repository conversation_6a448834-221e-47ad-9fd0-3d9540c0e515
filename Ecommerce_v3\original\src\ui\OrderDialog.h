#ifndef ORDERDIALOG_H
#define ORDERDIALOG_H

#include <QDialog>
#include "MainWindow.h"    // 一定要包含，让编译器看到 CartItem 全定义
#include "User.h"
#include "UserManager.h"
#include "ProductManager.h"

namespace Ui {
class OrderDialog;
}

class OrderDialog : public QDialog
{
    Q_OBJECT

public:
    explicit OrderDialog(const QVector<CartItem>& selectedItems,
                         User* user,
                         UserManager* userMgr,
                         ProductManager* prodMgr,
                         QWidget *parent = nullptr);
    ~OrderDialog();

private slots:
    void on_payButton_clicked();
    void on_cancelButton_clicked();

private:
    Ui::OrderDialog *ui;
    QVector<CartItem> m_items;
    User* m_user;
    UserManager* m_userMgr;
    ProductManager* m_prodMgr;

    double m_totalAmount;

    void calcTotal();
    void loadOrderTable();
};

#endif // ORDERDIALOG_H
