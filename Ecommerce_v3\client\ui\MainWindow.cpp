#include "MainWindow.h"
#include "ui_MainWindow.h"
#include "AddProductDialog.h"
#include "EditProductDialog.h"
#include "ProfileDialog.h"
#include "OrderDialog.h"
#include "LoginDialog.h"
#include "IconManager.h"
#include "StyleManager.h"
#include "ImageManager.h"
#include "../network/ClientManager.h"
#include <QMessageBox>
#include <QInputDialog>
#include <QTableWidgetItem>
#include <QHeaderView>
#include <QApplication>
#include <QPixmap>
#include <QFile>
#include <QCheckBox>
#include <QSpinBox>
#include <QHBoxLayout>

MainWindow::MainWindow(ClientManager* clientMgr, QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow)
    , m_clientMgr(clientMgr)
    , m_productTab(nullptr)
{
    ui->setupUi(this);
    setupUI();
    setupTabsForUserType();
    loadProfileInfo();
    
    // 连接信号
    connect(m_clientMgr, &ClientManager::loginStatusChanged, this, &MainWindow::onLoginStatusChanged);
    connect(m_clientMgr, &ClientManager::balanceChanged, this, &MainWindow::onBalanceChanged);
    connect(m_clientMgr, &ClientManager::disconnected, this, &MainWindow::onDisconnected);
    
    // 初始加载数据
    loadHomeProducts();
    loadCartProducts();
    if (m_clientMgr->getCurrentUserType() == "seller") {
        loadProductProducts();
    }
}

MainWindow::~MainWindow()
{
    delete ui;
}

void MainWindow::setupUI()
{
    setWindowTitle("电商交易平台 - 网络版");
    
    // 隐藏表格行号
    ui->homeProductsTable->verticalHeader()->setVisible(false);
    ui->cartTable->verticalHeader()->setVisible(false);
    ui->productProductsTable->verticalHeader()->setVisible(false);
    
    // 设置表格列宽自适应
    ui->homeProductsTable->horizontalHeader()->setStretchLastSection(true);
    ui->cartTable->horizontalHeader()->setStretchLastSection(true);
    ui->productProductsTable->horizontalHeader()->setStretchLastSection(true);
    
    // 设置表格选择模式
    ui->homeProductsTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    ui->cartTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    ui->productProductsTable->setSelectionBehavior(QAbstractItemView::SelectRows);

    // 设置按钮图标
    setupButtonIcons();

    // 应用样式
    applyStyles();
}

void MainWindow::applyStyles()
{
    StyleManager& styleMgr = StyleManager::instance();

    // 应用表格样式
    ui->homeProductsTable->setStyleSheet(styleMgr.getTableStyle());
    ui->cartTable->setStyleSheet(styleMgr.getTableStyle());
    ui->productProductsTable->setStyleSheet(styleMgr.getTableStyle());

    // 应用主窗口样式
    this->setStyleSheet(styleMgr.getMainWindowStyle());
}

void MainWindow::setupButtonIcons()
{
    IconManager& iconMgr = IconManager::instance();

    // 个人中心按钮图标
    ui->refreshProfileButton->setIcon(iconMgr.getIcon(IconManager::Refresh));
    ui->rechargeButton->setIcon(iconMgr.getIcon(IconManager::Recharge));
    ui->profileButton->setIcon(iconMgr.getIcon(IconManager::Profile));
    ui->changePwdButton->setIcon(iconMgr.getIcon(IconManager::Password));
    ui->logoutButton->setIcon(iconMgr.getIcon(IconManager::Logout));

    // 商品管理按钮图标（如果存在）
    QPushButton* addProductBtn = findChild<QPushButton*>("addProductButton");
    QPushButton* refreshProductBtn = findChild<QPushButton*>("refreshProductButton");
    QPushButton* updateProductBtn = findChild<QPushButton*>("updateProductButton");
    QPushButton* batchDiscountBtn = findChild<QPushButton*>("batchDiscountButton");

    if (addProductBtn) addProductBtn->setIcon(iconMgr.getIcon(IconManager::Add));
    if (refreshProductBtn) refreshProductBtn->setIcon(iconMgr.getIcon(IconManager::Refresh));
    if (updateProductBtn) updateProductBtn->setIcon(iconMgr.getIcon(IconManager::Edit));
    if (batchDiscountBtn) batchDiscountBtn->setIcon(iconMgr.getIcon(IconManager::Edit));
}

void MainWindow::setupTabsForUserType()
{
    QString userType = m_clientMgr->getCurrentUserType();

    // 查找商品管理标签页的索引
    int productTabIndex = -1;
    for (int i = 0; i < ui->tabs->count(); ++i) {
        if (ui->tabs->tabText(i) == "商品管理") {
            productTabIndex = i;
            m_productTab = ui->tabs->widget(i);
            break;
        }
    }

    if (userType == "consumer") {
        // 消费者：移除商品管理标签页
        if (productTabIndex >= 0 && m_productTab) {
            ui->tabs->removeTab(productTabIndex);
        }
    } else if (userType == "seller") {
        // 商家：确保商品管理标签页存在，并且在正确位置（第3个位置，索引为2）
        if (productTabIndex < 0 && m_productTab) {
            // 重新添加商品管理标签页到正确位置：商品浏览、购物车、商品管理、个人中心
            ui->tabs->insertTab(2, m_productTab, "商品管理");
        }
    }
}

void MainWindow::loadProfileInfo()
{
    ui->usernameLabel->setText(m_clientMgr->getCurrentUsername());
    ui->userTypeLabel->setText(m_clientMgr->getCurrentUserType() == "consumer" ? "消费者" : "商家");

    // 显示个性签名
    QString signature = m_clientMgr->getCurrentSignature();
    ui->signatureLabel->setText(signature.isEmpty() ? "暂无个性签名" : signature);

    updateBalanceDisplay();
    updateAvatarDisplay();
}

void MainWindow::updateBalanceDisplay()
{
    ui->balanceLabel->setText(QString("￥%1").arg(m_clientMgr->getCurrentBalance(), 0, 'f', 2));
}

void MainWindow::updateAvatarDisplay()
{
    QString avatarPath = m_clientMgr->getCurrentAvatarPath();
    QPixmap pixmap;

    // 尝试加载用户自定义头像
    if (!avatarPath.isEmpty() && QFile::exists(avatarPath)) {
        pixmap.load(avatarPath);
    }

    // 如果没有自定义头像或加载失败，使用默认头像
    if (pixmap.isNull()) {
        QString defaultAvatarPath = ":/avatars/default_avatar.png";
        pixmap.load(defaultAvatarPath);
    }

    if (!pixmap.isNull()) {
        // 缩放头像到合适大小
        QPixmap scaledPixmap = pixmap.scaled(100, 100, Qt::KeepAspectRatio, Qt::SmoothTransformation);
        ui->avatarLabel->setPixmap(scaledPixmap);
    } else {
        ui->avatarLabel->setText("头像");
    }
}

void MainWindow::loadHomeProducts()
{
    QString errorMsg;
    m_currentProducts = m_clientMgr->getAllProducts(errorMsg);

    if (!errorMsg.isEmpty()) {
        QMessageBox::warning(this, "错误", "获取商品列表失败：" + errorMsg);
        return;
    }

    updateHomeProductsTable();
}

void MainWindow::updateHomeProductsTable()
{
    // 设置表格列
    ui->homeProductsTable->setColumnCount(9);
    QStringList headers = {"ID", "图片", "商品名称", "类别", "原价", "折后价", "库存", "商家", "备注"};
    ui->homeProductsTable->setHorizontalHeaderLabels(headers);

    // 设置列宽
    ui->homeProductsTable->setColumnWidth(0, 60);  // ID
    ui->homeProductsTable->setColumnWidth(1, 80);  // 图片
    ui->homeProductsTable->setColumnWidth(2, 150); // 商品名称
    ui->homeProductsTable->setColumnWidth(3, 80);  // 类别
    ui->homeProductsTable->setColumnWidth(4, 80);  // 原价
    ui->homeProductsTable->setColumnWidth(5, 80);  // 折后价
    ui->homeProductsTable->setColumnWidth(6, 60);  // 库存
    ui->homeProductsTable->setColumnWidth(7, 100); // 商家
    ui->homeProductsTable->setColumnWidth(8, 120); // 备注

    // 设置行高
    ui->homeProductsTable->setRowCount(m_currentProducts.size());
    for (int i = 0; i < m_currentProducts.size(); ++i) {
        ui->homeProductsTable->setRowHeight(i, 70);
    }

    // 填充数据
    ImageManager& imageMgr = ImageManager::instance();
    for (int i = 0; i < m_currentProducts.size(); ++i) {
        const ClientProduct& product = m_currentProducts[i];

        // ID列
        ui->homeProductsTable->setItem(i, 0, new QTableWidgetItem(QString::number(product.id)));

        // 图片列
        QLabel* imageLabel = new QLabel();
        QPixmap productImage = imageMgr.getProductImage(product.imagePath, product.category, QSize(64, 64));
        imageLabel->setPixmap(productImage);
        imageLabel->setAlignment(Qt::AlignCenter);
        imageLabel->setScaledContents(true);
        ui->homeProductsTable->setCellWidget(i, 1, imageLabel);

        // 商品名称
        ui->homeProductsTable->setItem(i, 2, new QTableWidgetItem(product.name));

        // 类别
        QString categoryName;
        switch (product.category) {
            case 0: categoryName = "图书"; break;
            case 1: categoryName = "服装"; break;
            case 2: categoryName = "食品"; break;
            default: categoryName = "未知"; break;
        }
        ui->homeProductsTable->setItem(i, 3, new QTableWidgetItem(categoryName));

        // 价格和其他信息
        ui->homeProductsTable->setItem(i, 4, new QTableWidgetItem(QString::number(product.originalPrice, 'f', 2)));
        ui->homeProductsTable->setItem(i, 5, new QTableWidgetItem(QString::number(product.discountedPrice, 'f', 2)));
        ui->homeProductsTable->setItem(i, 6, new QTableWidgetItem(QString::number(product.stock)));
        ui->homeProductsTable->setItem(i, 7, new QTableWidgetItem(product.owner));
        ui->homeProductsTable->setItem(i, 8, new QTableWidgetItem(product.remark));
    }
}

void MainWindow::loadCartProducts()
{
    QString errorMsg;
    m_currentCart = m_clientMgr->getCart(errorMsg);

    if (!errorMsg.isEmpty()) {
        QMessageBox::warning(this, "错误", "获取购物车失败：" + errorMsg);
        return;
    }

    // 设置表格列
    ui->cartTable->setColumnCount(11);
    QStringList headers = {"选择", "ID", "图片", "商品名称", "类别", "原价", "折后价", "数量", "商家", "备注", "小计"};
    ui->cartTable->setHorizontalHeaderLabels(headers);

    // 设置列宽
    ui->cartTable->setColumnWidth(0, 60);  // 选择
    ui->cartTable->setColumnWidth(1, 60);  // ID
    ui->cartTable->setColumnWidth(2, 80);  // 图片
    ui->cartTable->setColumnWidth(3, 120); // 商品名称
    ui->cartTable->setColumnWidth(4, 80);  // 类别
    ui->cartTable->setColumnWidth(5, 80);  // 原价
    ui->cartTable->setColumnWidth(6, 80);  // 折后价
    ui->cartTable->setColumnWidth(7, 80);  // 数量
    ui->cartTable->setColumnWidth(8, 100); // 商家
    ui->cartTable->setColumnWidth(9, 120); // 备注
    ui->cartTable->setColumnWidth(10, 80); // 小计

    // 填充数据
    ui->cartTable->setRowCount(m_currentCart.size());

    // 设置行高
    for (int i = 0; i < m_currentCart.size(); ++i) {
        ui->cartTable->setRowHeight(i, 70);
    }

    // 获取最新的商品信息以显示库存
    QVector<ClientProduct> allProducts = m_clientMgr->getAllProducts(errorMsg);

    for (int i = 0; i < m_currentCart.size(); ++i) {
        const CartItem& item = m_currentCart[i];

        // 找到对应商品的完整信息
        ClientProduct productInfo;
        bool found = false;
        for (const ClientProduct& product : allProducts) {
            if (product.id == item.productId) {
                productInfo = product;
                found = true;
                break;
            }
        }

        // 0. 复选框
        QWidget* cbWidget = new QWidget();
        QCheckBox* cb = new QCheckBox();
        cb->setChecked(false); // 默认不选中
        cb->setProperty("row", i);
        QHBoxLayout* layoutCB = new QHBoxLayout(cbWidget);
        layoutCB->addWidget(cb);
        layoutCB->setAlignment(Qt::AlignCenter);
        layoutCB->setContentsMargins(0, 0, 0, 0);
        ui->cartTable->setCellWidget(i, 0, cbWidget);

        // 1. ID
        ui->cartTable->setItem(i, 1, new QTableWidgetItem(QString::number(item.productId)));

        // 2. 图片
        QLabel* imageLabel = new QLabel();
        if (found) {
            ImageManager& imageMgr = ImageManager::instance();
            QPixmap productImage = imageMgr.getProductImage(productInfo.imagePath, productInfo.category, QSize(64, 64));
            imageLabel->setPixmap(productImage);
        }
        imageLabel->setAlignment(Qt::AlignCenter);
        imageLabel->setScaledContents(true);
        ui->cartTable->setCellWidget(i, 2, imageLabel);

        // 3. 商品名称
        ui->cartTable->setItem(i, 3, new QTableWidgetItem(item.productName));

        // 4. 类别
        QString categoryName = "未知";
        if (found) {
            switch (productInfo.category) {
                case 0: categoryName = "图书"; break;
                case 1: categoryName = "服装"; break;
                case 2: categoryName = "食品"; break;
                default: categoryName = "未知"; break;
            }
        }
        ui->cartTable->setItem(i, 4, new QTableWidgetItem(categoryName));

        // 5. 原价
        ui->cartTable->setItem(i, 5, new QTableWidgetItem(found ? QString::number(productInfo.originalPrice, 'f', 2) : "-"));

        // 6. 折后价
        ui->cartTable->setItem(i, 6, new QTableWidgetItem(QString::number(item.price, 'f', 2)));

        // 7. 数量（可编辑，可超过库存）
        QWidget* spinWidget = new QWidget();
        QSpinBox* sbQty = new QSpinBox();
        sbQty->setMinimum(0);
        sbQty->setMaximum(9999);
        sbQty->setValue(item.quantity);
        sbQty->setProperty("row", i);
        sbQty->setProperty("productId", item.productId);
        connect(sbQty, QOverload<int>::of(&QSpinBox::valueChanged), this, &MainWindow::onCartQuantityChanged);
        QHBoxLayout* layoutSpin = new QHBoxLayout(spinWidget);
        layoutSpin->addWidget(sbQty);
        layoutSpin->setAlignment(Qt::AlignCenter);
        layoutSpin->setContentsMargins(0, 0, 0, 0);
        ui->cartTable->setCellWidget(i, 7, spinWidget);

        // 8. 商家
        ui->cartTable->setItem(i, 8, new QTableWidgetItem(found ? productInfo.owner : "-"));

        // 9. 备注
        ui->cartTable->setItem(i, 9, new QTableWidgetItem(found ? productInfo.remark : "-"));

        // 10. 小计
        double subtotal = item.price * item.quantity;
        ui->cartTable->setItem(i, 10, new QTableWidgetItem(QString::number(subtotal, 'f', 2)));

        // 设置只读项
        ui->cartTable->item(i, 1)->setFlags(ui->cartTable->item(i, 1)->flags() & ~Qt::ItemIsEditable);
        ui->cartTable->item(i, 3)->setFlags(ui->cartTable->item(i, 3)->flags() & ~Qt::ItemIsEditable);
        ui->cartTable->item(i, 4)->setFlags(ui->cartTable->item(i, 4)->flags() & ~Qt::ItemIsEditable);
        ui->cartTable->item(i, 5)->setFlags(ui->cartTable->item(i, 5)->flags() & ~Qt::ItemIsEditable);
        ui->cartTable->item(i, 6)->setFlags(ui->cartTable->item(i, 6)->flags() & ~Qt::ItemIsEditable);
        ui->cartTable->item(i, 8)->setFlags(ui->cartTable->item(i, 8)->flags() & ~Qt::ItemIsEditable);
        ui->cartTable->item(i, 9)->setFlags(ui->cartTable->item(i, 9)->flags() & ~Qt::ItemIsEditable);
        ui->cartTable->item(i, 10)->setFlags(ui->cartTable->item(i, 10)->flags() & ~Qt::ItemIsEditable);
    }
}

void MainWindow::loadProductProducts()
{
    // 商家查看自己的商品
    QString errorMsg;
    QVector<ClientProduct> allProducts = m_clientMgr->getAllProducts(errorMsg);
    
    if (!errorMsg.isEmpty()) {
        QMessageBox::warning(this, "错误", "获取商品列表失败：" + errorMsg);
        return;
    }
    
    // 筛选出当前用户的商品
    QVector<ClientProduct> myProducts;
    QString currentUser = m_clientMgr->getCurrentUsername();
    for (const ClientProduct& product : allProducts) {
        if (product.owner == currentUser) {
            myProducts.append(product);
        }
    }
    
    // 设置表格列
    ui->productProductsTable->setColumnCount(9);
    QStringList headers = {"ID", "图片", "商品名称", "原价", "折后价", "库存", "折扣", "类别", "备注"};
    ui->productProductsTable->setHorizontalHeaderLabels(headers);

    // 设置列宽
    ui->productProductsTable->setColumnWidth(0, 60);  // ID
    ui->productProductsTable->setColumnWidth(1, 80);  // 图片
    ui->productProductsTable->setColumnWidth(2, 150); // 商品名称
    ui->productProductsTable->setColumnWidth(3, 80);  // 原价
    ui->productProductsTable->setColumnWidth(4, 80);  // 折后价
    ui->productProductsTable->setColumnWidth(5, 60);  // 库存
    ui->productProductsTable->setColumnWidth(6, 60);  // 折扣
    ui->productProductsTable->setColumnWidth(7, 80);  // 类别
    ui->productProductsTable->setColumnWidth(8, 120); // 备注

    // 填充数据
    ui->productProductsTable->setRowCount(myProducts.size());
    ImageManager& imageMgr = ImageManager::instance();
    for (int i = 0; i < myProducts.size(); ++i) {
        const ClientProduct& product = myProducts[i];

        // 设置行高
        ui->productProductsTable->setRowHeight(i, 70);

        // ID
        ui->productProductsTable->setItem(i, 0, new QTableWidgetItem(QString::number(product.id)));

        // 图片
        QLabel* imageLabel = new QLabel();
        QPixmap productImage = imageMgr.getProductImage(product.imagePath, product.category, QSize(64, 64));
        imageLabel->setPixmap(productImage);
        imageLabel->setAlignment(Qt::AlignCenter);
        imageLabel->setScaledContents(true);
        ui->productProductsTable->setCellWidget(i, 1, imageLabel);

        // 其他信息
        ui->productProductsTable->setItem(i, 2, new QTableWidgetItem(product.name));
        ui->productProductsTable->setItem(i, 3, new QTableWidgetItem(QString::number(product.originalPrice, 'f', 2)));
        ui->productProductsTable->setItem(i, 4, new QTableWidgetItem(QString::number(product.discountedPrice, 'f', 2)));
        ui->productProductsTable->setItem(i, 5, new QTableWidgetItem(QString::number(product.stock)));
        ui->productProductsTable->setItem(i, 6, new QTableWidgetItem(QString::number(product.discount, 'f', 2)));

        QString categoryName;
        switch (product.category) {
            case 0: categoryName = "图书"; break;
            case 1: categoryName = "服装"; break;
            case 2: categoryName = "食品"; break;
            default: categoryName = "未知"; break;
        }
        ui->productProductsTable->setItem(i, 7, new QTableWidgetItem(categoryName));
        ui->productProductsTable->setItem(i, 8, new QTableWidgetItem(product.remark));
    }
}

void MainWindow::on_refreshHomeButton_clicked()
{
    // 重置筛选条件
    ui->categoryFilterCombo->setCurrentIndex(0);
    ui->homeSearchLineEdit->clear();
    loadHomeProducts();
}

void MainWindow::on_searchHomeButton_clicked()
{
    QString searchText = ui->homeSearchLineEdit->text().trimmed();
    int categoryIndex = ui->categoryFilterCombo->currentIndex();
    int category = (categoryIndex == 0) ? -1 : (categoryIndex - 1); // 0=全部(-1), 1=图书(0), 2=服装(1), 3=食品(2)

    QString errorMsg;
    if (searchText.isEmpty() && category == -1) {
        // 如果没有搜索条件，直接获取所有商品
        loadHomeProducts();
        return;
    }

    // 使用筛选接口
    m_currentProducts = m_clientMgr->filterProducts(searchText, category, errorMsg);

    if (!errorMsg.isEmpty()) {
        QMessageBox::warning(this, "错误", "筛选失败：" + errorMsg);
        return;
    }

    // 更新表格显示（不重新获取数据）
    updateHomeProductsTable();
}

void MainWindow::on_addToCartButton_clicked()
{
    int currentRow = ui->homeProductsTable->currentRow();
    if (currentRow < 0) {
        QMessageBox::warning(this, "提示", "请选择要添加的商品");
        return;
    }

    // 移除用户类型限制，商家也可以购物

    const ClientProduct& product = m_currentProducts[currentRow];

    bool ok;
    int quantity = QInputDialog::getInt(this, "添加到购物车",
        QString("输入要添加的数量（当前库存 %1）:").arg(product.stock),
        1, 1, 9999, 1, &ok);
    if (!ok) return;

    QString errorMsg;
    if (!m_clientMgr->addToCart(product.id, quantity, errorMsg)) {
        QMessageBox::warning(this, "错误", "添加到购物车失败：" + errorMsg);
        return;
    }

    QMessageBox::information(this, "成功", "商品已添加到购物车");
    loadCartProducts(); // 刷新购物车
}

void MainWindow::on_generateOrderButton_clicked()
{
    if (m_currentCart.isEmpty()) {
        QMessageBox::warning(this, "提示", "购物车为空");
        return;
    }

    // 收集选中的商品
    QVector<CartItem> selectedItems;
    for (int i = 0; i < ui->cartTable->rowCount(); ++i) {
        QWidget* cbWidget = ui->cartTable->cellWidget(i, 0);
        if (cbWidget) {
            QCheckBox* cb = cbWidget->findChild<QCheckBox*>();
            if (cb && cb->isChecked()) {
                // 获取最新的数量
                QWidget* spinWidget = ui->cartTable->cellWidget(i, 7);
                if (spinWidget) {
                    QSpinBox* spinBox = spinWidget->findChild<QSpinBox*>();
                    if (spinBox && i < m_currentCart.size()) {
                        CartItem item = m_currentCart[i];
                        item.quantity = spinBox->value();
                        item.totalPrice = item.price * item.quantity;
                        if (item.quantity > 0) {
                            selectedItems.append(item);
                        }
                    }
                }
            }
        }
    }

    if (selectedItems.isEmpty()) {
        QMessageBox::information(this, "提示", "请先勾选要生成订单的商品并确认购买数量。");
        return;
    }

    // 构造订单项列表
    QVector<QPair<int, int>> orderItems;
    for (const CartItem& item : selectedItems) {
        orderItems.append(qMakePair(item.productId, item.quantity));
    }

    // 预订单处理（立即扣减库存）
    QString errorMsg;
    double totalAmount = 0.0;
    if (!m_clientMgr->reserveOrder(orderItems, errorMsg, totalAmount)) {
        QMessageBox::warning(this, "预订单失败", errorMsg);
        return;
    }

    QMessageBox::information(this, "库存预留成功",
        QString("商品库存已预留，订单总金额：￥%1\n请在订单确认窗口中完成支付。")
        .arg(totalAmount, 0, 'f', 2));

    // 打开订单对话框（此时库存已经扣减）
    OrderDialog orderDialog(selectedItems, m_clientMgr, this);
    if (orderDialog.exec() == QDialog::Accepted) {
        // 订单支付成功，刷新界面
        loadCartProducts(); // 刷新购物车
        updateBalanceDisplay(); // 更新余额显示
        QMessageBox::information(this, "交易完成", "感谢您的购买！");
    } else {
        // 用户取消订单，需要回滚库存
        QString cancelErrorMsg;
        if (!m_clientMgr->cancelOrder(cancelErrorMsg)) {
            QMessageBox::warning(this, "取消订单失败",
                "取消订单时发生错误：" + cancelErrorMsg + "\n请联系客服处理。");
        } else {
            QMessageBox::information(this, "订单已取消", "订单已取消，库存已恢复。");
        }
    }
}

void MainWindow::on_removeCartButton_clicked()
{
    // 收集选中的商品ID
    QVector<int> selectedProductIds;
    for (int i = 0; i < ui->cartTable->rowCount(); ++i) {
        QWidget* cbWidget = ui->cartTable->cellWidget(i, 0);
        if (cbWidget) {
            QCheckBox* cb = cbWidget->findChild<QCheckBox*>();
            if (cb && cb->isChecked() && i < m_currentCart.size()) {
                selectedProductIds.append(m_currentCart[i].productId);
            }
        }
    }

    if (selectedProductIds.isEmpty()) {
        QMessageBox::warning(this, "提示", "请先勾选要移除的商品");
        return;
    }

    // 移除选中的商品
    QString errorMsg;
    bool hasError = false;
    for (int productId : selectedProductIds) {
        if (!m_clientMgr->removeFromCart(productId, errorMsg)) {
            QMessageBox::warning(this, "错误", QString("移除商品ID %1 失败：%2").arg(productId).arg(errorMsg));
            hasError = true;
        }
    }

    if (!hasError) {
        QMessageBox::information(this, "成功", "选中的商品已从购物车移除");
    }

    loadCartProducts(); // 刷新购物车
}

void MainWindow::on_refreshCartButton_clicked()
{
    loadCartProducts();
}

void MainWindow::on_addProductButton_clicked()
{
    AddProductDialog dialog(m_clientMgr, this);
    if (dialog.exec() == QDialog::Accepted) {
        loadProductProducts(); // 刷新商品列表
    }
}

void MainWindow::on_refreshProductButton_clicked()
{
    loadProductProducts();
}

void MainWindow::on_updateProductButton_clicked()
{
    int currentRow = ui->productProductsTable->currentRow();
    if (currentRow < 0) {
        QMessageBox::warning(this, "提示", "请选择要修改的商品");
        return;
    }

    // 获取选中商品的ID
    int productId = ui->productProductsTable->item(currentRow, 0)->text().toInt();

    // 从当前商品列表中找到对应的商品
    QString errorMsg;
    QVector<ClientProduct> allProducts = m_clientMgr->getAllProducts(errorMsg);
    ClientProduct selectedProduct;
    bool found = false;

    for (const ClientProduct& product : allProducts) {
        if (product.id == productId && product.owner == m_clientMgr->getCurrentUsername()) {
            selectedProduct = product;
            found = true;
            break;
        }
    }

    if (!found) {
        QMessageBox::warning(this, "错误", "未找到选中的商品");
        return;
    }

    EditProductDialog dialog(selectedProduct, m_clientMgr, this);
    if (dialog.exec() == QDialog::Accepted) {
        loadProductProducts(); // 刷新商品列表
    }
}

void MainWindow::on_batchDiscountButton_clicked()
{
    if (m_clientMgr->getCurrentUserType() != "seller") {
        QMessageBox::warning(this, "提示", "只有商家可以设置批量折扣");
        return;
    }

    // 选择商品类型
    QStringList categories;
    categories << "图书" << "服装" << "食品";
    bool ok;
    QString categoryName = QInputDialog::getItem(this, "批量折扣设置",
        "请选择要设置折扣的商品类型：", categories, 0, false, &ok);
    if (!ok || categoryName.isEmpty()) return;

    // 获取折扣值
    double discount = QInputDialog::getDouble(this, "批量折扣设置",
        QString("请输入 %1 类商品的折扣率（0.1-1.0）：").arg(categoryName),
        1.0, 0.1, 1.0, 2, &ok);
    if (!ok) return;

    // 转换类型名称为索引
    int category = categories.indexOf(categoryName);
    if (category < 0) return;

    // 发送批量更新请求
    QString errorMsg;
    if (!m_clientMgr->batchUpdateDiscount(category, discount, errorMsg)) {
        QMessageBox::warning(this, "错误", "批量设置折扣失败：" + errorMsg);
        return;
    }

    QMessageBox::information(this, "成功",
        QString("成功为 %1 类商品设置折扣率为 %2").arg(categoryName).arg(discount));

    // 刷新商品列表
    loadProductProducts();
}

void MainWindow::on_refreshProfileButton_clicked()
{
    // 从服务器获取最新的用户信息
    QString errorMsg;
    if (!m_clientMgr->refreshUserInfo(errorMsg)) {
        QMessageBox::warning(this, "刷新失败", "获取最新用户信息失败：" + errorMsg);
        return;
    }

    // 重新加载个人信息显示
    loadProfileInfo();
    QMessageBox::information(this, "刷新成功", "个人信息已更新");
}

void MainWindow::on_rechargeButton_clicked()
{
    bool ok;
    double amount = QInputDialog::getDouble(this, "充值", "请输入充值金额：", 0, 0, 999999, 2, &ok);
    if (!ok || amount <= 0) return;

    QString errorMsg;
    if (!m_clientMgr->rechargeBalance(amount, errorMsg)) {
        QMessageBox::warning(this, "错误", "充值失败：" + errorMsg);
        return;
    }

    QMessageBox::information(this, "成功", QString("充值成功！当前余额：￥%1").arg(m_clientMgr->getCurrentBalance(), 0, 'f', 2));
}

void MainWindow::on_changePwdButton_clicked()
{
    QString oldPwd = QInputDialog::getText(this, "修改密码", "请输入旧密码：", QLineEdit::Password);
    if (oldPwd.isEmpty()) return;

    QString newPwd = QInputDialog::getText(this, "修改密码", "请输入新密码：", QLineEdit::Password);
    if (newPwd.isEmpty()) return;

    QString errorMsg;
    if (!m_clientMgr->changePassword(oldPwd, newPwd, errorMsg)) {
        QMessageBox::warning(this, "错误", "修改密码失败：" + errorMsg);
        return;
    }

    QMessageBox::information(this, "成功", "密码修改成功！");
}

void MainWindow::on_profileButton_clicked()
{
    ProfileDialog dialog(m_clientMgr, this);
    if (dialog.exec() == QDialog::Accepted) {
        loadProfileInfo(); // 刷新个人信息显示
    }
}

void MainWindow::on_logoutButton_clicked()
{
    // 确认退出登录
    int ret = QMessageBox::question(this, "确认", "确定要退出登录吗？",
                                   QMessageBox::Yes | QMessageBox::No,
                                   QMessageBox::No);
    if (ret != QMessageBox::Yes) {
        return;
    }

    QString errorMsg;

    // 先清空购物车
    if (!m_clientMgr->clearCart(errorMsg)) {
        // 如果清空购物车失败，记录错误但不阻止退出登录
        qWarning() << "清空购物车失败：" << errorMsg;
    }

    if (!m_clientMgr->logout(errorMsg)) {
        QMessageBox::warning(this, "错误", "退出登录失败：" + errorMsg);
        return;
    }

    // 清空界面数据
    ui->homeProductsTable->setRowCount(0);
    ui->cartTable->setRowCount(0);
    if (ui->tabs->count() > 3) { // 如果有商品管理标签页
        ui->productProductsTable->setRowCount(0);
    }

    // 隐藏主窗口
    this->hide();

    // 显示登录对话框
    LoginDialog loginDialog(m_clientMgr, this);
    if (loginDialog.exec() == QDialog::Accepted) {
        // 重新登录成功，重新设置界面和加载数据
        setupTabsForUserType();  // 根据新用户类型设置标签页
        loadProfileInfo();
        loadHomeProducts();
        loadCartProducts();
        if (m_clientMgr->getCurrentUserType() == "seller") {
            loadProductProducts();
        }
        this->show();
    } else {
        // 用户取消登录或登录失败，关闭程序
        QApplication::quit();
    }
}

void MainWindow::onLoginStatusChanged(bool loggedIn)
{
    if (!loggedIn) {
        QApplication::quit();
    }
}

void MainWindow::onBalanceChanged(double newBalance)
{
    Q_UNUSED(newBalance)
    updateBalanceDisplay();
}

void MainWindow::onDisconnected()
{
    QMessageBox::critical(this, "连接断开", "与服务器的连接已断开");
    QApplication::quit();
}

void MainWindow::onCartQuantityChanged(int value)
{
    QSpinBox* spinBox = qobject_cast<QSpinBox*>(sender());
    if (!spinBox) return;

    int row = spinBox->property("row").toInt();
    Q_UNUSED(spinBox->property("productId").toInt()); // 暂时不使用，但保留以备将来使用

    if (row >= 0 && row < m_currentCart.size()) {
        // 更新本地购物车数据
        m_currentCart[row].quantity = value;
        m_currentCart[row].totalPrice = m_currentCart[row].price * value;

        // 更新表格显示的小计
        if (ui->cartTable->item(row, 10)) {
            ui->cartTable->item(row, 10)->setText(QString::number(m_currentCart[row].totalPrice, 'f', 2));
        }

        // 同步到服务器（这里可以选择实时同步或者延迟同步）
        // 为了简化，我们在生成订单时再同步最新数量
    }
}
