[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\managers", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug\\debug", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\main.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/build/Desktop_Qt_6_8_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/src/main.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\managers", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug\\debug", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models\\Food.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/build/Desktop_Qt_6_8_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/src/models/Food.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\managers", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug\\debug", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models\\User.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/build/Desktop_Qt_6_8_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/src/models/User.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\managers", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug\\debug", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models\\Seller.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/build/Desktop_Qt_6_8_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/src/models/Seller.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\managers", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug\\debug", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models\\Consumer.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/build/Desktop_Qt_6_8_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/src/models/Consumer.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\managers", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug\\debug", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models\\Product.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/build/Desktop_Qt_6_8_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/src/models/Product.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\managers", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug\\debug", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models\\Book.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/build/Desktop_Qt_6_8_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/src/models/Book.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\managers", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug\\debug", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models\\Clothing.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/build/Desktop_Qt_6_8_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/src/models/Clothing.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\managers", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug\\debug", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models\\Order.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/build/Desktop_Qt_6_8_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/src/models/Order.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\managers", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug\\debug", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\managers\\UserManager.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/build/Desktop_Qt_6_8_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/src/managers/UserManager.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\managers", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug\\debug", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\managers\\ProductManager.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/build/Desktop_Qt_6_8_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/src/managers/ProductManager.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\managers", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug\\debug", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui\\EditProfileDialog.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/build/Desktop_Qt_6_8_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/src/ui/EditProfileDialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\managers", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug\\debug", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui\\LoginDialog.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/build/Desktop_Qt_6_8_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/src/ui/LoginDialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\managers", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug\\debug", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui\\OrderDialog.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/build/Desktop_Qt_6_8_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/src/ui/OrderDialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\managers", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug\\debug", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui\\RegisterDialog.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/build/Desktop_Qt_6_8_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/src/ui/RegisterDialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\managers", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug\\debug", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui\\MainWindow.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/build/Desktop_Qt_6_8_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/src/ui/MainWindow.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\managers", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug\\debug", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models\\Food.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/build/Desktop_Qt_6_8_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/src/models/Food.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\managers", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug\\debug", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models\\User.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/build/Desktop_Qt_6_8_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/src/models/User.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\managers", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug\\debug", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models\\Seller.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/build/Desktop_Qt_6_8_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/src/models/Seller.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\managers", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug\\debug", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models\\Consumer.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/build/Desktop_Qt_6_8_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/src/models/Consumer.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\managers", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug\\debug", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models\\Product.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/build/Desktop_Qt_6_8_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/src/models/Product.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\managers", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug\\debug", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models\\Book.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/build/Desktop_Qt_6_8_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/src/models/Book.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\managers", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug\\debug", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models\\Clothing.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/build/Desktop_Qt_6_8_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/src/models/Clothing.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\managers", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug\\debug", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models\\Order.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/build/Desktop_Qt_6_8_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/src/models/Order.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\managers", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug\\debug", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\managers\\UserManager.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/build/Desktop_Qt_6_8_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/src/managers/UserManager.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\managers", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug\\debug", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\managers\\ProductManager.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/build/Desktop_Qt_6_8_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/src/managers/ProductManager.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\managers", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug\\debug", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui\\EditProfileDialog.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/build/Desktop_Qt_6_8_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/src/ui/EditProfileDialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\managers", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug\\debug", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui\\LoginDialog.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/build/Desktop_Qt_6_8_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/src/ui/LoginDialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\managers", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug\\debug", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui\\OrderDialog.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/build/Desktop_Qt_6_8_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/src/ui/OrderDialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\managers", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug\\debug", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui\\RegisterDialog.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/build/Desktop_Qt_6_8_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/src/ui/RegisterDialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\managers", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug\\debug", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui\\MainWindow.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/build/Desktop_Qt_6_8_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/src/ui/MainWindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\managers", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug\\debug", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug\\ui_EditProfileDialog.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/build/Desktop_Qt_6_8_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/build/Desktop_Qt_6_8_3_MinGW_64_bit-Debug/ui_EditProfileDialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\managers", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug\\debug", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug\\ui_LoginDialog.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/build/Desktop_Qt_6_8_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/build/Desktop_Qt_6_8_3_MinGW_64_bit-Debug/ui_LoginDialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\managers", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug\\debug", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug\\ui_RegisterDialog.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/build/Desktop_Qt_6_8_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/build/Desktop_Qt_6_8_3_MinGW_64_bit-Debug/ui_RegisterDialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\managers", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug\\debug", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug\\ui_MainWindow.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/build/Desktop_Qt_6_8_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/build/Desktop_Qt_6_8_3_MinGW_64_bit-Debug/ui_MainWindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\managers", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\src\\ui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtWidgets", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtGui", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug\\debug", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v2\\build\\Desktop_Qt_6_8_3_MinGW_64_bit-Debug\\ui_OrderDialog.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/build/Desktop_Qt_6_8_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v2/build/Desktop_Qt_6_8_3_MinGW_64_bit-Debug/ui_OrderDialog.h"}]