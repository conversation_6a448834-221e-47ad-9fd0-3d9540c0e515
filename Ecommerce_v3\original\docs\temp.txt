构建时出现以下报错信息
D:\myCode\Qt\E-Commerce-Trading-Platform\Ecommerce_v2\src\ui\MainWindow.cpp:84: error: 'QFile' has not been declared
..\..\src\ui\MainWindow.cpp: In member function 'void MainWindow::loadProfileInfo()':
..\..\src\ui\MainWindow.cpp:84:34: error: 'QFile' has not been declared
   84 |     if (avatarPath.isEmpty() || !QFile::exists(avatarPath)) {
      |                                  ^~~~~
D:\myCode\Qt\E-Commerce-Trading-Platform\Ecommerce_v2\src\ui\MainWindow.cpp:218: warning: 'void QCheckBox::stateChanged(int)' is deprecated: Use checkStateChanged() instead [-Wdeprecated-declarations]
..\..\src\ui\MainWindow.cpp: In member function 'void MainWindow::loadCartProducts()':
..\..\src\ui\MainWindow.cpp:218:33: warning: 'void QCheckBox::stateChanged(int)' is deprecated: Use checkStateChanged() instead [-Wdeprecated-declarations]
  218 |         connect(cb, &QCheckBox::stateChanged, [this, row](int state){
      |                                 ^~~~~~~~~~~~
D:\software\Qt\6.8.3\mingw_64\include\QtWidgets\QCheckBox:1: In file included from D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QCheckBox:1,
In file included from D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/QCheckBox:1,
                 from ..\..\src\ui\MainWindow.cpp:7:
D:/software/Qt/6.8.3/mingw_64/include/QtWidgets/qcheckbox.h:41:10: note: declared here
   41 |     void stateChanged(int);
      |          ^~~~~~~~~~~~
:-1: error: [Makefile.Debug:3592: debug/MainWindow.o] Error 1
D:\myCode\Qt\E-Commerce-Trading-Platform\Ecommerce_v2\src\ui\MainWindow.cpp:84: error: Use of undeclared identifier 'QFile'
D:\myCode\Qt\E-Commerce-Trading-Platform\Ecommerce_v2\src\ui\MainWindow.cpp:218: 'stateChanged' is deprecated: Use checkStateChanged() instead
D:\myCode\Qt\E-Commerce-Trading-Platform\Ecommerce_v2\src\ui\MainWindow.cpp:18: warning: Included header Book.h is not used directly (fixes available)
https://clangd.llvm.org/guides/include-cleaner
D:\myCode\Qt\E-Commerce-Trading-Platform\Ecommerce_v2\src\ui\MainWindow.cpp:19: warning: Included header Clothing.h is not used directly (fixes available)
https://clangd.llvm.org/guides/include-cleaner
D:\myCode\Qt\E-Commerce-Trading-Platform\Ecommerce_v2\src\ui\MainWindow.cpp:20: warning: Included header Food.h is not used directly (fixes available)
https://clangd.llvm.org/guides/include-cleaner

接下来我重新给出我当前的MainWindow.cpp和MainWindow.h，在分析这些代码后根据报错信息修改代码
//MainWindow.h
#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QVector>
#include <QPair>

#include "User.h"
#include "UserManager.h"
#include "ProductManager.h"

// 简单的购物车项结构
struct CartItem {
    Product* product;   // 指向 ProductManager 中的商品对象
    int      quantity;  // 用户想购买的数量
    bool     selected;  // 是否在“生成订单”时被勾选
};

QT_BEGIN_NAMESPACE
namespace Ui { class MainWindow; }
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(User* user, UserManager* userMgr, ProductManager* prodMgr, QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    // —— 主页 Tab 的槽函数 —— 
    void on_filterButton_clicked();
    void on_searchHomeButton_clicked();
    void on_refreshHomeButton_clicked();
    // void on_purchaseHomeButton_clicked();
    void on_addToCartButton_clicked(); // 添加到购物车按钮槽，参数为所在行号

    // “购物车”相关
    void on_generateOrderButton_clicked();

    // —— 商品管理 Tab 的槽函数 —— 
    void on_addProductButton_clicked();
    void on_refreshProductButton_clicked();
    void on_updateProductButton_clicked();
    void on_setDiscountButton_clicked();

    // —— 我的账户 Tab 的槽函数 —— 
    void on_rechargeButton_clicked();
    void on_changePwdButton_clicked();
    void on_logoutButton_clicked();        // 新增：退出登录
    void on_editProfileButton_clicked();  // 新增：编辑个人信息

private:
    void setupUI();
    void loadProfileInfo();
    void loadHomeProducts();                // 统一加载主页表格（考虑 m_currentFilter + 搜索文本）
    void loadProductProducts();
    void loadCartProducts();

    Ui::MainWindow *ui;
    User* m_currentUser;
    UserManager* m_userMgr;
    ProductManager* m_prodMgr;

    Product::Category m_currentFilter;     // 当前筛选类别，-1 表示“全部”

    QVector<CartItem> m_cart; // 购物车数据：每个 CartItem 记录一个商品指针、数量和是否选中
};

#endif // MAINWINDOW_H


//MainWindow.cpp
#include "MainWindow.h"
#include "ui_MainWindow.h"

#include <QMessageBox>
#include <QInputDialog>
#include <QTableWidgetItem>
#include <QCheckBox>
#include <QSpinBox>
#include <QPushButton>
#include <QHBoxLayout>
#include <QApplication>

#include "LoginDialog.h"
#include "EditProfileDialog.h"
#include "OrderDialog.h"

#include "Product.h"
#include "Book.h"
#include "Clothing.h"
#include "Food.h"


MainWindow::MainWindow(User* user, UserManager* userMgr, ProductManager* prodMgr, QWidget *parent)
    : QMainWindow(parent),
      ui(new Ui::MainWindow),
      m_currentUser(user),
      m_userMgr(userMgr),
      m_prodMgr(prodMgr),
      m_currentFilter(static_cast<Product::Category>(-1))
{
    ui->setupUi(this);
    setupUI();
    loadProfileInfo();

    // 如果是消费者，则隐藏“商品管理”Tab（索引 2，因为现在为：主页(0)、购物车(1)、商品管理(2)、我的账户(3)）
    if (m_currentUser->getUserType() == User::ConsumerType) {
        ui->tabs->removeTab(2);
    }

    loadHomeProducts();
    // 新增：初始化购物车表格（为空）
    loadCartProducts();
    loadProductProducts();
}

MainWindow::~MainWindow()
{
    delete ui;
}

void MainWindow::setupUI()
{
    setWindowTitle(QString("欢迎，%1").arg(m_currentUser->getUsername()));

    // 隐藏行号
    ui->homeProductsTable->verticalHeader()->setVisible(false);
    ui->productProductsTable->verticalHeader()->setVisible(false);
    ui->cartTable->verticalHeader()->setVisible(false);

    // 让表格最后一列自适应充满剩余宽度
    ui->homeProductsTable->horizontalHeader()->setStretchLastSection(true);
    ui->productProductsTable->horizontalHeader()->setStretchLastSection(true);
    ui->cartTable->horizontalHeader()->setStretchLastSection(true);

    // 头像控件属性（如果有）
    ui->avatarLabel->setScaledContents(true);
    ui->avatarLabel->setMinimumSize(64, 64);
    ui->avatarLabel->setMaximumSize(64, 64);
}

void MainWindow::loadProfileInfo()
{
    // 显示用户名、余额、签名、头像
    ui->usernameLabel->setText(m_currentUser->getUsername());
    ui->balanceLabel->setText(QString::number(m_currentUser->getBalance(), 'f', 2));

    // 个性签名
    QString sig = m_currentUser->getSignature();
    if (sig.isEmpty()) sig = "这个人很懒，什么都没写";
    ui->signatureLabel->setText(sig);

    // 头像
    QString avatarPath = m_currentUser->getAvatarPath();
    if (avatarPath.isEmpty() || !QFile::exists(avatarPath)) {
        avatarPath = ":/default_avatar.png";
    }
    ui->avatarLabel->setPixmap(
        QPixmap(avatarPath).scaled(64, 64, Qt::KeepAspectRatio, Qt::SmoothTransformation)
    );
}


// —— 主页：加载商品，考虑当前 m_currentFilter 和 搜索框内容 —— 
void MainWindow::loadHomeProducts()
{
    QVector<Product*> all = m_prodMgr->getAllProducts();
    QString keyword = ui->homeSearchLineEdit->text().trimmed();

    ui->homeProductsTable->setRowCount(0);
    int row = 0;
    for (Product* p : all) {
        // 1. 筛选类型
        if (m_currentFilter != static_cast<Product::Category>(-1) &&
            p->getCategory() != m_currentFilter) {
            continue;
        }
        // 2. 搜索关键字
        if (!keyword.isEmpty() &&
            !p->getName().contains(keyword, Qt::CaseInsensitive)) {
            continue;
        }

        ui->homeProductsTable->insertRow(row);

        // 0. “添加”按钮
        QPushButton* addBtn = new QPushButton("添加");
        addBtn->setProperty("row", row);
        connect(addBtn, &QPushButton::clicked,
                this, &MainWindow::on_addToCartButton_clicked);
        ui->homeProductsTable->setCellWidget(row, 0, addBtn);

        // 1. ID
        ui->homeProductsTable->setItem(row, 1,
            new QTableWidgetItem(QString::number(p->getId())));

        // 2. 名称
        ui->homeProductsTable->setItem(row, 2,
            new QTableWidgetItem(p->getName()));

        // 3. 类别
        QString catStr;
        switch (p->getCategory()) {
        case Product::BookCategory:    catStr = "图书"; break;
        case Product::ClothingCategory:catStr = "服装"; break;
        case Product::FoodCategory:    catStr = "食物"; break;
        }
        ui->homeProductsTable->setItem(row, 3,
            new QTableWidgetItem(catStr));

        // 4. 原价
        ui->homeProductsTable->setItem(row, 4,
            new QTableWidgetItem(QString::number(p->getOriginalPrice(), 'f', 2)));

        // 5. 折后价
        ui->homeProductsTable->setItem(row, 5,
            new QTableWidgetItem(QString::number(p->getDiscountedPrice(), 'f', 2)));

        // 6. 库存
        ui->homeProductsTable->setItem(row, 6,
            new QTableWidgetItem(QString::number(p->getStock())));

        // 7. 商家
        ui->homeProductsTable->setItem(row, 7,
            new QTableWidgetItem(p->getOwner()));

        // 8. 备注 (Book: 作者；Clothing: 尺码+颜色；Food: 空)
        ui->homeProductsTable->setItem(row, 8,
            new QTableWidgetItem(p->getRemark()));

        ++row;
    }
}

// 主页：添加到购物车
void MainWindow::on_addToCartButton_clicked()
{
    QPushButton* btn = qobject_cast<QPushButton*>(sender());
    if (!btn) return;
    int row = btn->property("row").toInt();
    if (row < 0 || row >= ui->homeProductsTable->rowCount()) return;

    int prodId = ui->homeProductsTable->item(row, 1)->text().toInt();
    Product* p = m_prodMgr->getProductById(prodId);
    if (!p) return;

    bool ok = false;
    int qty = QInputDialog::getInt(this,
                                   "添加到购物车",
                                   QString("输入要添加的数量（当前库存 %1）:").arg(p->getStock()),
                                   1, 1, 9999, 1, &ok);
    if (!ok) return;

    // 如果该商品已在购物车，就累加数量，否则新建条目
    bool found = false;
    for (CartItem &ci : m_cart) {
        if (ci.product->getId() == prodId) {
            ci.quantity += qty;
            found = true;
            break;
        }
    }
    if (!found) {
        CartItem ci;
        ci.product = p;
        ci.quantity = qty;
        ci.selected = false;
        m_cart.append(ci);
    }

    loadCartProducts();
}

void MainWindow::loadCartProducts()
{
    ui->cartTable->setRowCount(0);
    int row = 0;
    for (int i = 0; i < m_cart.size(); ++i) {
        CartItem &ci = m_cart[i];
        Product* p = ci.product;

        ui->cartTable->insertRow(row);

        // 0. 复选框
        QWidget* cbWidget = new QWidget();
        QCheckBox* cb = new QCheckBox();
        cb->setChecked(ci.selected);
        cb->setProperty("row", row);
        connect(cb, &QCheckBox::stateChanged, [this, row](int state){
            if (row >= 0 && row < m_cart.size())
                m_cart[row].selected = (state == Qt::Checked);
        });
        QHBoxLayout* layoutCB = new QHBoxLayout(cbWidget);
        layoutCB->addWidget(cb);
        layoutCB->setAlignment(Qt::AlignCenter);
        layoutCB->setContentsMargins(0,0,0,0);
        ui->cartTable->setCellWidget(row, 0, cbWidget);

        // 1. ID
        ui->cartTable->setItem(row, 1,
            new QTableWidgetItem(QString::number(p->getId())));

        // 2. 名称
        ui->cartTable->setItem(row, 2,
            new QTableWidgetItem(p->getName()));

        // 3. 类别
        QString catStr;
        switch (p->getCategory()) {
        case Product::BookCategory:    catStr = "图书"; break;
        case Product::ClothingCategory:catStr = "服装"; break;
        case Product::FoodCategory:    catStr = "食物"; break;
        }
        ui->cartTable->setItem(row, 3,
            new QTableWidgetItem(catStr));

        // 4. 原价
        ui->cartTable->setItem(row, 4,
            new QTableWidgetItem(QString::number(p->getOriginalPrice(), 'f', 2)));

        // 5. 折后价
        ui->cartTable->setItem(row, 5,
            new QTableWidgetItem(QString::number(p->getDiscountedPrice(), 'f', 2)));

        // 6. 购买数量 (可超过库存)
        QWidget* spinWidget = new QWidget();
        QSpinBox* sbQty = new QSpinBox();
        sbQty->setMinimum(0);
        sbQty->setMaximum(9999);
        sbQty->setValue(ci.quantity);
        sbQty->setProperty("row", row);
        connect(sbQty, QOverload<int>::of(&QSpinBox::valueChanged), [this, row](int val){
            if (row >= 0 && row < m_cart.size()) {
                m_cart[row].quantity = val;
            }
        });
        QHBoxLayout* layoutSpin = new QHBoxLayout(spinWidget);
        layoutSpin->addWidget(sbQty);
        layoutSpin->setAlignment(Qt::AlignCenter);
        layoutSpin->setContentsMargins(0,0,0,0);
        ui->cartTable->setCellWidget(row, 6, spinWidget);

        // 7. 商家
        ui->cartTable->setItem(row, 7,
            new QTableWidgetItem(p->getOwner()));

        // 8. 备注
        ui->cartTable->setItem(row, 8,
            new QTableWidgetItem(p->getRemark()));

        ++row;
    }
}

// 购物车：生成订单
void MainWindow::on_generateOrderButton_clicked()
{
    QVector<CartItem> selectedItems;
    for (const CartItem &ci : m_cart) {
        if (ci.selected && ci.quantity > 0) {
            selectedItems.append(ci);
        }
    }
    if (selectedItems.isEmpty()) {
        QMessageBox::information(this, "提示", "请先勾选要生成订单的商品并确认购买数量。");
        return;
    }

    // 检查库存
    for (const CartItem &ci : selectedItems) {
        Product* p = ci.product;
        if (ci.quantity > p->getStock()) {
            QMessageBox::warning(this, "库存不足",
                                 QString("商品 [%1] 的需求数量 (%2) 超过库存 (%3)。")
                                 .arg(p->getName())
                                 .arg(ci.quantity)
                                 .arg(p->getStock()));
            return;
        }
    }

    // 弹出 OrderDialog
    OrderDialog dlg(selectedItems, m_currentUser, m_userMgr, m_prodMgr, this);
    if (dlg.exec() == QDialog::Accepted) {
        // 如果支付成功，则把对应项目从购物车里移除
        for (const CartItem &ci : selectedItems) {
            for (int i = 0; i < m_cart.size(); ++i) {
                if (m_cart[i].product->getId() == ci.product->getId()) {
                    m_cart.remove(i);
                    break;
                }
            }
        }
        loadCartProducts();
        loadHomeProducts();
        loadProductProducts();
        QMessageBox::information(this, "下单成功", "订单已生成并支付成功！");
    }
}

// —— 商品管理：只显示当前商家自己的商品 —— 
void MainWindow::loadProductProducts()
{
    ui->productProductsTable->setRowCount(0);
    auto prods = m_prodMgr->getAllProducts();
    int row = 0;
    for (auto p : prods) {
        if (p->getOwner() != m_currentUser->getUsername()) continue;

        ui->productProductsTable->insertRow(row);
        ui->productProductsTable->setItem(row, 0, new QTableWidgetItem(QString::number(p->getId())));
        ui->productProductsTable->setItem(row, 1, new QTableWidgetItem(p->getName()));

        QString catStr;
        switch (p->getCategory()) {
            case Product::BookCategory:    catStr = "Book";    break;
            case Product::ClothingCategory:catStr = "Clothing";break;
            case Product::FoodCategory:    catStr = "Food";    break;
        }
        ui->productProductsTable->setItem(row, 2, new QTableWidgetItem(catStr));

        ui->productProductsTable->setItem(row, 3, new QTableWidgetItem(QString::number(p->getOriginalPrice(), 'f', 2)));
        ui->productProductsTable->setItem(row, 4, new QTableWidgetItem(QString::number(p->getDiscountedPrice(), 'f', 2)));
        ui->productProductsTable->setItem(row, 5, new QTableWidgetItem(QString::number(p->getStock())));
        ui->productProductsTable->setItem(row, 6, new QTableWidgetItem(p->getOwner()));
        ui->productProductsTable->setItem(row, 7, new QTableWidgetItem(p->getRemark()));
        row++;
    }
}

// —— 主页：筛选功能 —— 
void MainWindow::on_filterButton_clicked()
{
    QStringList categories;
    categories << "全部" << "Book" << "Clothing" << "Food";
    bool ok;
    QString choice = QInputDialog::getItem(this,
        "筛选商品类型", "请选择类别：", categories, 0, false, &ok);
    if (!ok || choice.isEmpty()) return;

    if (choice == "全部") {
        m_currentFilter = static_cast<Product::Category>(-1);
    }
    else if (choice == "Book") {
        m_currentFilter = Product::BookCategory;
    }
    else if (choice == "Clothing") {
        m_currentFilter = Product::ClothingCategory;
    }
    else {
        m_currentFilter = Product::FoodCategory;
    }

    loadHomeProducts();
}

void MainWindow::on_searchHomeButton_clicked()
{
    loadHomeProducts();
}

void MainWindow::on_refreshHomeButton_clicked()
{
    ui->homeSearchLineEdit->clear();
    m_currentFilter = static_cast<Product::Category>(-1);
    loadHomeProducts();
}

// —— 商品管理：添加商品，不设置折扣 —— 
void MainWindow::on_addProductButton_clicked()
{
    QStringList categories;
    categories << "Book" << "Clothing" << "Food";
    bool ok;
    QString choice = QInputDialog::getItem(this,
        "添加商品", "请选择商品类型：", categories, 0, false, &ok);
    if (!ok || choice.isEmpty()) return;

    Product::Category cat = (choice == "Book" ? Product::BookCategory
                                 : choice == "Clothing" ? Product::ClothingCategory
                                                        : Product::FoodCategory);

    QString name = QInputDialog::getText(this,
        "添加商品", "名称：", QLineEdit::Normal, "", &ok);
    if (!ok || name.isEmpty()) return;

    double price = QInputDialog::getDouble(this,
        "添加商品", "原价：", 0.0, 0, 1e9, 2, &ok);
    if (!ok) return;

    int stock = QInputDialog::getInt(this,
        "添加商品", "库存：", 1, 1, 1e9, 1, &ok);
    if (!ok) return;

    double discount = 1.0; // 初始折扣 = 1.0

    QString extra1, extra2;
    if (cat == Product::BookCategory) {
        extra1 = QInputDialog::getText(this, "添加图书", "作者：", QLineEdit::Normal, "", &ok);
        if (!ok || extra1.isEmpty()) return;
    }
    else if (cat == Product::ClothingCategory) {
        extra1 = QInputDialog::getText(this, "添加服装", "尺码：", QLineEdit::Normal, "", &ok);
        if (!ok || extra1.isEmpty()) return;
        extra2 = QInputDialog::getText(this, "添加服装", "颜色：", QLineEdit::Normal, "", &ok);
        if (!ok || extra2.isEmpty()) return;
    }
    // Food 无额外参数

    int newId = m_prodMgr->getNextProductId();
    bool added = m_prodMgr->addProduct(cat, newId, name, price, stock,
                                       m_currentUser->getUsername(), discount,
                                       extra1, extra2);
    if (added) {
        loadHomeProducts();
        loadProductProducts();
        QMessageBox::information(this, "成功", QString("%1 添加成功！").arg(choice));
    } else {
        QMessageBox::warning(this, "失败", "添加失败，请检查 ID 是否冲突。");
    }
}

void MainWindow::on_refreshProductButton_clicked()
{
    loadProductProducts();
}

void MainWindow::on_updateProductButton_clicked()
{
    bool ok;
    int id = QInputDialog::getInt(this,
        "更新商品", "请输入要更新的商品 ID：", 0, 0, 1e9, 1, &ok);
    if (!ok) return;

    Product* p = m_prodMgr->getProductById(id);
    if (!p) {
        QMessageBox::warning(this, "未找到", "该商品不存在。");
        return;
    }
    if (p->getOwner() != m_currentUser->getUsername()) {
        QMessageBox::warning(this, "无权限", "只能更新自己发布的商品。");
        return;
    }

    double newPrice = QInputDialog::getDouble(this,
        "更新商品", QString("新价格 (当前原价：%1)：").arg(p->getOriginalPrice()), p->getOriginalPrice(), 0, 1e9, 2, &ok);
    if (!ok) return;

    int newStock = QInputDialog::getInt(this,
        "更新商品", QString("新库存 (当前库存：%1)：").arg(p->getStock()), p->getStock(), 0, 1e9, 1, &ok);
    if (!ok) return;

    double newDiscount = QInputDialog::getDouble(this,
        "更新商品", QString("新折扣率 (当前折扣：%1)：").arg(p->getDiscount()), p->getDiscount(), 0.0, 1.0, 2, &ok);
    if (!ok) return;

    bool ok1 = m_prodMgr->updateProductPrice(id, newPrice);
    bool ok2 = m_prodMgr->updateProductStock(id, newStock);
    bool ok3 = m_prodMgr->updateProductDiscount(id, newDiscount);

    if (ok1 && ok2 && ok3) {
        loadProductProducts();
        QMessageBox::information(this, "成功", "商品信息更新成功！");
    } else {
        QMessageBox::warning(this, "失败", "更新失败，请重试。");
    }
}

void MainWindow::on_setDiscountButton_clicked()
{
    QStringList categories;
    categories << "Book" << "Clothing" << "Food";
    bool ok;
    QString choice = QInputDialog::getItem(this,
        "设置折扣", "请选择类别：", categories, 0, false, &ok);
    if (!ok || choice.isEmpty()) return;

    Product::Category cat = (choice == "Book" ? Product::BookCategory
                                 : choice == "Clothing" ? Product::ClothingCategory
                                                        : Product::FoodCategory);

    double newDiscount = QInputDialog::getDouble(this,
        "设置折扣", QString("%1 类商品折扣率 (0.0~1.0)：").arg(choice), 1.0, 0.0, 1.0, 2, &ok);
    if (!ok) return;

    bool anyChanged = false;
    auto prods = m_prodMgr->getAllProducts();
    for (auto p : prods) {
        if (p->getOwner() == m_currentUser->getUsername() &&
            p->getCategory() == cat) {
            m_prodMgr->updateProductDiscount(p->getId(), newDiscount);
            anyChanged = true;
        }
    }
    if (anyChanged) {
        loadProductProducts();
        QMessageBox::information(this, "成功", QString("%1 类折扣已更新为 %2").arg(choice).arg(newDiscount));
    } else {
        QMessageBox::information(this, "提示", "您没有该类别的商品。");
    }
}

// —— 我的账户：充值、修改密码（保持不变） —— 
void MainWindow::on_rechargeButton_clicked()
{
    bool ok;
    double amount = QInputDialog::getDouble(this, "充值余额", "请输入充值金额：", 0.0, 0, 1e9, 2, &ok);
    if (!ok) return;
    if (amount <= 0) {
        QMessageBox::warning(this, "输入错误", "金额必须大于 0。");
        return;
    }
    if (m_userMgr->rechargeBalance(m_currentUser->getUsername(), amount)) {
        loadProfileInfo();
        QMessageBox::information(this, "成功", "充值成功！");
    } else {
        QMessageBox::warning(this, "失败", "充值失败。");
    }
}

// —— “编辑个人信息” 按钮
void MainWindow::on_editProfileButton_clicked()
{
    EditProfileDialog dlg(m_currentUser, m_userMgr, this);
    if (dlg.exec() == QDialog::Accepted) {
        // 重载界面展示
        loadProfileInfo();
        // 如果用户名可能改变，还需刷新商品列表等
        loadHomeProducts();
        loadProductProducts();
    }
}

void MainWindow::on_changePwdButton_clicked()
{
    bool ok;
    QString oldPwd = QInputDialog::getText(this, "修改密码", "旧密码：", QLineEdit::Password, "", &ok);
    if (!ok) return;
    QString newPwd = QInputDialog::getText(this, "修改密码", "新密码：", QLineEdit::Password, "", &ok);
    if (!ok) return;
    if (newPwd.isEmpty()) {
        QMessageBox::warning(this, "输入错误", "新密码不能为空。");
        return;
    }
    if (m_userMgr->changePassword(m_currentUser->getUsername(), oldPwd, newPwd)) {
        QMessageBox::information(this, "成功", "密码修改成功！");
    } else {
        QMessageBox::warning(this, "失败", "原密码错误或修改失败。");
    }
}

// —— 我的账户：退出登录 —— 
void MainWindow::on_logoutButton_clicked()
{
    this->hide();
    LoginDialog loginDlg(m_userMgr);
    User* newUser = nullptr;
    connect(&loginDlg, &LoginDialog::loginSuccess,
            [&](User* u){ newUser = u; });
    if (loginDlg.exec() == QDialog::Accepted && newUser) {
        m_currentUser = newUser;
        m_currentFilter = static_cast<Product::Category>(-1);
        ui->homeSearchLineEdit->clear();
        ui->homeProductsTable->setRowCount(0);
        ui->cartTable->setRowCount(0);
        ui->productProductsTable->setRowCount(0);
        loadProfileInfo();
        loadHomeProducts();

        // 根据用户类型动态增删“商品管理”Tab
        if (m_currentUser->getUserType() == User::SellerType) {
            if (ui->tabs->count() == 3) {
                ui->tabs->insertTab(2, ui->productTab, "商品管理");
            }
        } else {
            if (ui->tabs->count() == 4) {
                ui->tabs->removeTab(2);
            }
        }
        loadCartProducts();
        loadProductProducts();
        this->show();
    } else {
        qApp->exit(0);
    }
}