总结本项目，写一份实验报告

目录结构如下

1 前言
题目简要介绍
对题目的需求理解

2 总体设计
子系统划分，各子系统之间的关系（说明+图示）

3 详细设计
分子系统给出详细设计，每个子系统用到的类，类之间的的关系（说明+图示）
    3.1 账户管理子系统和商品管理子系统（单机版）详细设计
    3.2 交易管理子系统（单机版）详细设计
    3.3 电商交易平台（网络版）详细设计
    3.4	数据库说明
    3.5 接口协议说明

4 实现
分小节描述：
实现过程中遇到的主要问题和解决方案
想法
经验
教训

其中前两个单机版的子系统位于original文件夹内
第一个子系统功能如下：用户注册&登录、修改账户密码、余额管理、添加商品、展示平台商品信息、搜索平台商品信息、商品信息管理
第二个子系统功能如下：购物车管理、订单生成、网上支付
第三个子系统为网络版，包含前两个子系统的功能，采用CS结构，客户端与服务器系统之间使用socket进行通信