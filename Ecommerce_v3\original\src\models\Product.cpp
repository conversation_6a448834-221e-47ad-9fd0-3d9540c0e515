#include "Product.h"
#include <QStringList>

Product::Product(int id, const QString& name, double price, int stock,
                 const QString& owner, double discount)
    : m_id(id),
    m_name(name),
    m_price(price),
    m_stock(stock),
    m_owner(owner),
    m_discount(discount)
{
}

int Product::getId() const { return m_id; }

const QString& Product::getName() const { return m_name; }

double Product::getOriginalPrice() const { return m_price; }

int Product::getStock() const { return m_stock; }

const QString& Product::getOwner() const { return m_owner; }

double Product::getDiscount() const { return m_discount; }

void Product::setPrice(double price) { m_price = price; }

void Product::setStock(int stock) { m_stock = stock; }

void Product::setOwner(const QString& owner) { m_owner = owner; }

void Product::setDiscount(double discount)
{
    if (discount < 0.0) discount = 0.0;
    if (discount > 1.0) discount = 1.0;
    m_discount = discount;
}

QString Product::toCsvString() const
{
    // 基础格式：id,name,price,stock,category,owner,discount, … 子类字段 …
    QStringList parts;
    parts << QString::number(m_id)
          << m_name
          << QString::number(m_price, 'f', 2)
          << QString::number(m_stock)
          << QString::number(getCategory())
          << m_owner
          << QString::number(m_discount, 'f', 2);
    // 子类会 append 自己的额外字段并 join 返回
    return parts.join(",");
}
