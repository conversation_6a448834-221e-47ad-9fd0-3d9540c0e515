/********************************************************************************
** Form generated from reading UI file 'LoginDialog.ui'
**
** Created by: Qt User Interface Compiler version 6.8.3
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_LOGINDIALOG_H
#define UI_LOGINDIALOG_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QDialog>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QVBoxLayout>

QT_BEGIN_NAMESPACE

class Ui_LoginDialog
{
public:
    QVBoxLayout *verticalLayout;
    QHBoxLayout *horizontalLayout_username;
    QLabel *labelUsername;
    QLineEdit *usernameLineEdit;
    QHBoxLayout *horizontalLayout_password;
    QLabel *labelPassword;
    QLineEdit *passwordLineEdit;
    QHBoxLayout *horizontalLayout_buttons;
    QSpacerItem *horizontalSpacer_left;
    QPushButton *loginButton;
    QPushButton *registerButton;
    QSpacerItem *horizontalSpacer_right;

    void setupUi(QDialog *LoginDialog)
    {
        if (LoginDialog->objectName().isEmpty())
            LoginDialog->setObjectName("LoginDialog");
        LoginDialog->resize(400, 180);
        verticalLayout = new QVBoxLayout(LoginDialog);
        verticalLayout->setSpacing(8);
        verticalLayout->setContentsMargins(12, 12, 12, 12);
        verticalLayout->setObjectName("verticalLayout");
        horizontalLayout_username = new QHBoxLayout();
        horizontalLayout_username->setSpacing(6);
        horizontalLayout_username->setObjectName("horizontalLayout_username");
        labelUsername = new QLabel(LoginDialog);
        labelUsername->setObjectName("labelUsername");
        labelUsername->setMinimumSize(QSize(70, 0));

        horizontalLayout_username->addWidget(labelUsername);

        usernameLineEdit = new QLineEdit(LoginDialog);
        usernameLineEdit->setObjectName("usernameLineEdit");

        horizontalLayout_username->addWidget(usernameLineEdit);


        verticalLayout->addLayout(horizontalLayout_username);

        horizontalLayout_password = new QHBoxLayout();
        horizontalLayout_password->setSpacing(6);
        horizontalLayout_password->setObjectName("horizontalLayout_password");
        labelPassword = new QLabel(LoginDialog);
        labelPassword->setObjectName("labelPassword");
        labelPassword->setMinimumSize(QSize(70, 0));

        horizontalLayout_password->addWidget(labelPassword);

        passwordLineEdit = new QLineEdit(LoginDialog);
        passwordLineEdit->setObjectName("passwordLineEdit");
        passwordLineEdit->setEchoMode(QLineEdit::Password);

        horizontalLayout_password->addWidget(passwordLineEdit);


        verticalLayout->addLayout(horizontalLayout_password);

        horizontalLayout_buttons = new QHBoxLayout();
        horizontalLayout_buttons->setSpacing(12);
        horizontalLayout_buttons->setObjectName("horizontalLayout_buttons");
        horizontalSpacer_left = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_buttons->addItem(horizontalSpacer_left);

        loginButton = new QPushButton(LoginDialog);
        loginButton->setObjectName("loginButton");
        loginButton->setMinimumSize(QSize(80, 30));

        horizontalLayout_buttons->addWidget(loginButton);

        registerButton = new QPushButton(LoginDialog);
        registerButton->setObjectName("registerButton");
        registerButton->setMinimumSize(QSize(80, 30));

        horizontalLayout_buttons->addWidget(registerButton);

        horizontalSpacer_right = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_buttons->addItem(horizontalSpacer_right);


        verticalLayout->addLayout(horizontalLayout_buttons);


        retranslateUi(LoginDialog);

        QMetaObject::connectSlotsByName(LoginDialog);
    } // setupUi

    void retranslateUi(QDialog *LoginDialog)
    {
        LoginDialog->setWindowTitle(QCoreApplication::translate("LoginDialog", "\347\224\250\346\210\267\347\231\273\345\275\225", nullptr));
        labelUsername->setText(QCoreApplication::translate("LoginDialog", "\347\224\250\346\210\267\345\220\215\357\274\232", nullptr));
        usernameLineEdit->setPlaceholderText(QCoreApplication::translate("LoginDialog", "\350\257\267\350\276\223\345\205\245\347\224\250\346\210\267\345\220\215", nullptr));
        labelPassword->setText(QCoreApplication::translate("LoginDialog", "\345\257\206\347\240\201\357\274\232", nullptr));
        passwordLineEdit->setPlaceholderText(QCoreApplication::translate("LoginDialog", "\350\257\267\350\276\223\345\205\245\345\257\206\347\240\201", nullptr));
        loginButton->setText(QCoreApplication::translate("LoginDialog", "\347\231\273\345\275\225", nullptr));
        registerButton->setText(QCoreApplication::translate("LoginDialog", "\346\263\250\345\206\214", nullptr));
    } // retranslateUi

};

namespace Ui {
    class LoginDialog: public Ui_LoginDialog {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_LOGINDIALOG_H
