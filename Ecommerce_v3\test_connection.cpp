#include <QCoreApplication>
#include <QTcpSocket>
#include <QDebug>
#include <QTimer>

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    QTcpSocket socket;
    
    qDebug() << "Attempting to connect to localhost:8888...";
    
    socket.connectToHost("localhost", 8888);
    
    if (socket.waitForConnected(5000)) {
        qDebug() << "Connected successfully!";
        socket.write("test");
        socket.waitForBytesWritten(1000);
        socket.disconnectFromHost();
    } else {
        qDebug() << "Connection failed:" << socket.errorString();
    }
    
    return 0;
}
