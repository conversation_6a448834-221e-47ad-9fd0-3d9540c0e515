#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QVector>
#include <QPair>

#include "User.h"
#include "UserManager.h"
#include "ProductManager.h"

// 简单的购物车项结构
struct CartItem {
    Product* product;   // 指向 ProductManager 中的商品对象
    int      quantity;  // 用户想购买的数量
    bool     selected;  // 是否在“生成订单”时被勾选
};

QT_BEGIN_NAMESPACE
namespace Ui { class MainWindow; }
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(User* user, UserManager* userMgr, ProductManager* prodMgr, QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    // —— 主页 Tab 的槽函数 —— 
    void on_filterButton_clicked();
    void on_searchHomeButton_clicked();
    void on_refreshHomeButton_clicked();
    void on_addToCartButton_clicked();
    
    // —— 购物车 Tab 的槽函数 —— 
    void on_generateOrderButton_clicked();
    void on_removeCartButton_clicked();

    // —— 商品管理 Tab 的槽函数 —— 
    void on_addProductButton_clicked();
    void on_refreshProductButton_clicked();
    void on_updateProductButton_clicked();
    void on_setDiscountButton_clicked();

    // —— 我的账户 Tab 的槽函数 —— 
    void on_rechargeButton_clicked();
    void on_changePwdButton_clicked();
    void on_editProfileButton_clicked();  
    void on_logoutButton_clicked();       

private:
    void setupUI();
    void loadProfileInfo();
    void loadHomeProducts();                // 统一加载主页表格（考虑 m_currentFilter + 搜索文本）
    void loadProductProducts();
    void loadCartProducts();

    Ui::MainWindow *ui;
    User* m_currentUser;
    UserManager* m_userMgr;
    ProductManager* m_prodMgr;

    Product::Category m_currentFilter;     // 当前筛选类别，-1 表示“全部”

    QVector<CartItem> m_cart; // 购物车数据：每个 CartItem 记录一个商品指针、数量和是否选中
};

#endif // MAINWINDOW_H
