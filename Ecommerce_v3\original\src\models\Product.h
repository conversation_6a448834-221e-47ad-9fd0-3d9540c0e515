#ifndef PRODUCT_H
#define PRODUCT_H

#include <QString>

class Product
{
public:
    enum Category { BookCategory = 0, ClothingCategory = 1, FoodCategory = 2 };

    // 构造函数增加 owner（商家）和 discount（折扣率）
    Product(int id, const QString& name, double price, int stock,
            const QString& owner, double discount = 1.0);

    virtual ~Product() = default;

    int getId() const;
    const QString& getName() const;
    double getOriginalPrice() const;
    int getStock() const;
    const QString& getOwner() const;     // 商家用户名
    double getDiscount() const;          // 当前商品折扣率（0.0~1.0）

    void setPrice(double price);
    void setStock(int stock);
    void setOwner(const QString& owner);
    void setDiscount(double discount);   // 设置折扣率

    // 折后价 = 原价 * m_discount
    virtual double getDiscountedPrice() const { return m_price * m_discount; }

    virtual Category getCategory() const = 0;
    virtual QString getRemark() const = 0;      // 返回“备注”信息
    virtual QString toCsvString() const;        // 用于保存到 CSV 文件

protected:
    int m_id;
    QString m_name;
    double m_price;    // 原价
    int m_stock;
    QString m_owner;   // 发布该商品的商家
    double m_discount; // 单件商品的折扣率 (0.0~1.0)
};

#endif // PRODUCT_H
