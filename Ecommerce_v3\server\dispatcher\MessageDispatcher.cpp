#include "MessageDispatcher.h"
#include "../../common/Message.h"
#include "../../common/Protocol.h"
#include "../network/ClientHandler.h"
#include "../src/managers/UserManager.h"
#include "../src/managers/ProductManager.h"
#include "../src/models/User.h"
#include "../src/models/Consumer.h"
#include "../src/models/Seller.h"
#include "../src/models/Product.h"
#include "../src/models/Book.h"
#include "../src/models/Clothing.h"
#include "../src/models/Food.h"
#include <QDebug>
#include <QJsonArray>

MessageDispatcher::MessageDispatcher(QObject* parent)
    : QObject(parent)
    , m_userManager(nullptr)
    , m_productManager(nullptr)
{
}

QString MessageDispatcher::processMessage(const QString& messageStr, ClientHandler* client)
{
    Message msg = Message::fromJson(messageStr);
    if (!msg.isValid()) {
        return createErrorResponse(Protocol::ErrorMessage::INVALID_REQUEST).toJson();
    }
    
    QString action = msg.getAction();
    Response response;
    
    // 用户相关操作
    if (action == Protocol::Request::LOGIN) {
        response = handleLogin(msg, client);
    }
    else if (action == Protocol::Request::REGISTER) {
        response = handleRegister(msg, client);
    }
    else if (action == Protocol::Request::LOGOUT) {
        response = handleLogout(msg, client);
    }
    else if (action == Protocol::Request::CHANGE_PASSWORD) {
        response = handleChangePassword(msg, client);
    }
    else if (action == Protocol::Request::RECHARGE_BALANCE) {
        response = handleRechargeBalance(msg, client);
    }
    else if (action == Protocol::Request::UPDATE_PROFILE) {
        response = handleUpdateProfile(msg, client);
    }
    else if (action == Protocol::Request::GET_USER_INFO) {
        response = handleGetUserInfo(msg, client);
    }
    // 商品相关操作
    else if (action == Protocol::Request::GET_ALL_PRODUCTS) {
        response = handleGetAllProducts(msg, client);
    }
    else if (action == Protocol::Request::SEARCH_PRODUCTS) {
        response = handleSearchProducts(msg, client);
    }
    else if (action == Protocol::Request::FILTER_PRODUCTS) {
        response = handleFilterProducts(msg, client);
    }
    else if (action == Protocol::Request::ADD_PRODUCT) {
        response = handleAddProduct(msg, client);
    }
    else if (action == Protocol::Request::UPDATE_PRODUCT_STOCK) {
        response = handleUpdateProductStock(msg, client);
    }
    else if (action == Protocol::Request::UPDATE_PRODUCT_PRICE) {
        response = handleUpdateProductPrice(msg, client);
    }
    else if (action == Protocol::Request::UPDATE_PRODUCT_DISCOUNT) {
        response = handleUpdateProductDiscount(msg, client);
    }
    else if (action == Protocol::Request::BATCH_UPDATE_DISCOUNT) {
        response = handleBatchUpdateDiscount(msg, client);
    }
    // 购物车相关操作
    else if (action == Protocol::Request::ADD_TO_CART) {
        response = handleAddToCart(msg, client);
    }
    else if (action == Protocol::Request::REMOVE_FROM_CART) {
        response = handleRemoveFromCart(msg, client);
    }
    else if (action == Protocol::Request::GET_CART) {
        response = handleGetCart(msg, client);
    }
    else if (action == Protocol::Request::CLEAR_CART) {
        response = handleClearCart(msg, client);
    }
    // 订单相关操作
    else if (action == Protocol::Request::RESERVE_ORDER) {
        response = handleReserveOrder(msg, client);
    }
    else if (action == Protocol::Request::CREATE_ORDER) {
        response = handleCreateOrder(msg, client);
    }
    else if (action == Protocol::Request::CANCEL_ORDER) {
        response = handleCancelOrder(msg, client);
    }
    else {
        response = createErrorResponse("未知的请求类型: " + action);
    }
    
    return response.toJson();
}

Response MessageDispatcher::handleLogin(const Message& msg, ClientHandler* client)
{
    if (!m_userManager) {
        return createErrorResponse(Protocol::ErrorMessage::SERVER_ERROR, Protocol::Status::INTERNAL_ERROR);
    }
    
    QJsonObject data = msg.getData();
    QString username = data["username"].toString();
    QString password = data["password"].toString();
    
    if (username.isEmpty() || password.isEmpty()) {
        return createErrorResponse("用户名和密码不能为空");
    }
    
    User* user = m_userManager->loginUser(username, password);
    if (!user) {
        return createErrorResponse(Protocol::ErrorMessage::INVALID_CREDENTIALS, Protocol::Status::UNAUTHORIZED);
    }
    
    // 设置当前用户
    client->setCurrentUser(user);
    
    // 构造用户信息
    QJsonObject userData;
    userData["username"] = user->getUsername();
    userData["balance"] = user->getBalance();
    userData["userType"] = (user->getUserType() == User::ConsumerType) ? "consumer" : "seller";
    userData["signature"] = user->getSignature();
    userData["avatarPath"] = user->getAvatarPath();
    
    return createSuccessResponse(Protocol::SuccessMessage::LOGIN_SUCCESS, userData);
}

Response MessageDispatcher::handleRegister(const Message& msg, ClientHandler* client)
{
    if (!m_userManager) {
        return createErrorResponse(Protocol::ErrorMessage::SERVER_ERROR, Protocol::Status::INTERNAL_ERROR);
    }
    
    QJsonObject data = msg.getData();
    QString username = data["username"].toString();
    QString password = data["password"].toString();
    QString userTypeStr = data["userType"].toString();
    
    if (username.isEmpty() || password.isEmpty() || userTypeStr.isEmpty()) {
        return createErrorResponse("用户名、密码和用户类型不能为空");
    }
    
    User::UserType userType;
    if (userTypeStr == "consumer") {
        userType = User::ConsumerType;
    } else if (userTypeStr == "seller") {
        userType = User::SellerType;
    } else {
        return createErrorResponse("无效的用户类型");
    }
    
    if (!m_userManager->registerUser(username, password, userType)) {
        return createErrorResponse(Protocol::ErrorMessage::USER_EXISTS);
    }
    
    return createSuccessResponse(Protocol::SuccessMessage::REGISTER_SUCCESS);
}

Response MessageDispatcher::handleLogout(const Message& msg, ClientHandler* client)
{
    client->setCurrentUser(nullptr);
    return createSuccessResponse(Protocol::SuccessMessage::LOGOUT_SUCCESS);
}

bool MessageDispatcher::requireLogin(ClientHandler* client)
{
    return client && client->getCurrentUser() != nullptr;
}

bool MessageDispatcher::requireSellerRole(ClientHandler* client)
{
    if (!requireLogin(client)) {
        return false;
    }
    return client->getCurrentUser()->getUserType() == User::SellerType;
}

Response MessageDispatcher::createErrorResponse(const QString& message, int status)
{
    Response resp(false, message);
    resp.setStatus(status);
    return resp;
}

Response MessageDispatcher::createSuccessResponse(const QString& message, const QJsonObject& data)
{
    Response resp(true, message, data);
    resp.setStatus(Protocol::Status::SUCCESS);
    return resp;
}

Response MessageDispatcher::handleChangePassword(const Message& msg, ClientHandler* client)
{
    if (!requireLogin(client)) {
        return createErrorResponse(Protocol::ErrorMessage::INVALID_CREDENTIALS, Protocol::Status::UNAUTHORIZED);
    }

    if (!m_userManager) {
        return createErrorResponse(Protocol::ErrorMessage::SERVER_ERROR, Protocol::Status::INTERNAL_ERROR);
    }

    QJsonObject data = msg.getData();
    QString oldPassword = data["oldPassword"].toString();
    QString newPassword = data["newPassword"].toString();

    if (oldPassword.isEmpty() || newPassword.isEmpty()) {
        return createErrorResponse("旧密码和新密码不能为空");
    }

    QString username = client->getCurrentUser()->getUsername();
    if (!m_userManager->changePassword(username, oldPassword, newPassword)) {
        return createErrorResponse("旧密码错误");
    }

    return createSuccessResponse(Protocol::SuccessMessage::PASSWORD_CHANGED);
}

Response MessageDispatcher::handleRechargeBalance(const Message& msg, ClientHandler* client)
{
    if (!requireLogin(client)) {
        return createErrorResponse(Protocol::ErrorMessage::INVALID_CREDENTIALS, Protocol::Status::UNAUTHORIZED);
    }

    if (!m_userManager) {
        return createErrorResponse(Protocol::ErrorMessage::SERVER_ERROR, Protocol::Status::INTERNAL_ERROR);
    }

    QJsonObject data = msg.getData();
    double amount = data["amount"].toDouble();

    if (amount <= 0) {
        return createErrorResponse("充值金额必须大于0");
    }

    QString username = client->getCurrentUser()->getUsername();
    if (!m_userManager->rechargeBalance(username, amount)) {
        return createErrorResponse("充值失败");
    }

    // 更新客户端的用户余额信息
    User* user = client->getCurrentUser();
    QJsonObject userData;
    userData["balance"] = user->getBalance();

    return createSuccessResponse(Protocol::SuccessMessage::BALANCE_RECHARGED, userData);
}

Response MessageDispatcher::handleUpdateProfile(const Message& msg, ClientHandler* client)
{
    if (!requireLogin(client)) {
        return createErrorResponse(Protocol::ErrorMessage::INVALID_CREDENTIALS, Protocol::Status::UNAUTHORIZED);
    }

    if (!m_userManager) {
        return createErrorResponse(Protocol::ErrorMessage::SERVER_ERROR, Protocol::Status::INTERNAL_ERROR);
    }

    QJsonObject data = msg.getData();
    QString signature = data["signature"].toString();
    QString avatarPath = data["avatarPath"].toString();

    QString username = client->getCurrentUser()->getUsername();

    bool success = true;
    if (!signature.isNull()) {
        success &= m_userManager->updateSignature(username, signature);
    }
    if (!avatarPath.isNull()) {
        success &= m_userManager->updateAvatarPath(username, avatarPath);
    }

    if (!success) {
        return createErrorResponse("更新个人信息失败");
    }

    return createSuccessResponse(Protocol::SuccessMessage::PROFILE_UPDATED);
}

Response MessageDispatcher::handleGetAllProducts(const Message& msg, ClientHandler* client)
{
    if (!m_productManager) {
        return createErrorResponse(Protocol::ErrorMessage::SERVER_ERROR, Protocol::Status::INTERNAL_ERROR);
    }

    QVector<Product*> products = m_productManager->getAllProducts();
    QJsonArray productArray;

    for (Product* product : products) {
        QJsonObject productObj;
        productObj["id"] = product->getId();
        productObj["name"] = product->getName();
        productObj["originalPrice"] = product->getOriginalPrice();
        productObj["discountedPrice"] = product->getDiscountedPrice();
        productObj["stock"] = product->getStock();
        productObj["owner"] = product->getOwner();
        productObj["discount"] = product->getDiscount();
        productObj["category"] = static_cast<int>(product->getCategory());
        productObj["remark"] = product->getRemark();
        productObj["imagePath"] = product->getImagePath();

        // 添加特定类型的额外信息
        if (product->getCategory() == Product::BookCategory) {
            Book* book = dynamic_cast<Book*>(product);
            if (book) {
                productObj["author"] = book->getAuthor();
            }
        } else if (product->getCategory() == Product::ClothingCategory) {
            Clothing* clothing = dynamic_cast<Clothing*>(product);
            if (clothing) {
                productObj["size"] = clothing->getSize();
                productObj["color"] = clothing->getColor();
            }
        }

        productArray.append(productObj);
    }

    QJsonObject responseData;
    responseData["products"] = productArray;

    return createSuccessResponse("获取商品列表成功", responseData);
}

Response MessageDispatcher::handleSearchProducts(const Message& msg, ClientHandler* client)
{
    if (!m_productManager) {
        return createErrorResponse(Protocol::ErrorMessage::SERVER_ERROR, Protocol::Status::INTERNAL_ERROR);
    }

    QJsonObject data = msg.getData();
    QString searchName = data["name"].toString();

    if (searchName.isEmpty()) {
        return createErrorResponse("搜索关键词不能为空");
    }

    QVector<Product*> products = m_productManager->searchByName(searchName);
    QJsonArray productArray;

    for (Product* product : products) {
        QJsonObject productObj;
        productObj["id"] = product->getId();
        productObj["name"] = product->getName();
        productObj["originalPrice"] = product->getOriginalPrice();
        productObj["discountedPrice"] = product->getDiscountedPrice();
        productObj["stock"] = product->getStock();
        productObj["owner"] = product->getOwner();
        productObj["discount"] = product->getDiscount();
        productObj["category"] = static_cast<int>(product->getCategory());
        productObj["remark"] = product->getRemark();

        // 添加特定类型的额外信息
        if (product->getCategory() == Product::BookCategory) {
            Book* book = dynamic_cast<Book*>(product);
            if (book) {
                productObj["author"] = book->getAuthor();
            }
        } else if (product->getCategory() == Product::ClothingCategory) {
            Clothing* clothing = dynamic_cast<Clothing*>(product);
            if (clothing) {
                productObj["size"] = clothing->getSize();
                productObj["color"] = clothing->getColor();
            }
        }

        productArray.append(productObj);
    }

    QJsonObject responseData;
    responseData["products"] = productArray;

    return createSuccessResponse("搜索商品成功", responseData);
}

Response MessageDispatcher::handleFilterProducts(const Message& msg, ClientHandler* client)
{
    if (!m_productManager) {
        return createErrorResponse(Protocol::ErrorMessage::SERVER_ERROR, Protocol::Status::INTERNAL_ERROR);
    }

    QJsonObject data = msg.getData();
    QString keyword = data["keyword"].toString();
    int category = data["category"].toInt(-1); // -1表示全部类型

    QVector<Product*> allProducts = m_productManager->getAllProducts();
    QJsonArray productArray;

    for (Product* product : allProducts) {
        bool matchKeyword = keyword.isEmpty() ||
                           product->getName().contains(keyword, Qt::CaseInsensitive);
        bool matchCategory = (category == -1) ||
                            (static_cast<int>(product->getCategory()) == category);

        if (matchKeyword && matchCategory) {
            QJsonObject productObj;
            productObj["id"] = product->getId();
            productObj["name"] = product->getName();
            productObj["originalPrice"] = product->getOriginalPrice();
            productObj["discountedPrice"] = product->getDiscountedPrice();
            productObj["stock"] = product->getStock();
            productObj["owner"] = product->getOwner();
            productObj["category"] = static_cast<int>(product->getCategory());
            productObj["discount"] = product->getDiscount();
            productObj["remark"] = product->getRemark();
            productObj["imagePath"] = product->getImagePath();
            productArray.append(productObj);
        }
    }

    QJsonObject responseData;
    responseData["products"] = productArray;

    return createSuccessResponse("筛选商品成功", responseData);
}

Response MessageDispatcher::handleAddProduct(const Message& msg, ClientHandler* client)
{
    if (!requireSellerRole(client)) {
        return createErrorResponse(Protocol::ErrorMessage::PERMISSION_DENIED, Protocol::Status::UNAUTHORIZED);
    }

    if (!m_productManager) {
        return createErrorResponse(Protocol::ErrorMessage::SERVER_ERROR, Protocol::Status::INTERNAL_ERROR);
    }

    QJsonObject data = msg.getData();
    QString name = data["name"].toString();
    double price = data["price"].toDouble();
    int stock = data["stock"].toInt();
    double discount = data["discount"].toDouble(1.0);
    int categoryInt = data["category"].toInt();
    QString remark = data["remark"].toString();
    QString imagePath = data["imagePath"].toString();

    if (name.isEmpty() || price <= 0 || stock < 0) {
        return createErrorResponse("商品信息不完整或无效");
    }

    Product::Category category = static_cast<Product::Category>(categoryInt);
    QString owner = client->getCurrentUser()->getUsername();
    int productId = m_productManager->getNextProductId();

    QString extra1 = data["extra1"].toString();
    QString extra2 = data["extra2"].toString();

    if (!m_productManager->addProduct(category, productId, name, price, stock, owner, discount, extra1, extra2, imagePath)) {
        return createErrorResponse("添加商品失败");
    }

    QJsonObject responseData;
    responseData["productId"] = productId;

    return createSuccessResponse(Protocol::SuccessMessage::PRODUCT_ADDED, responseData);
}

Response MessageDispatcher::handleUpdateProduct(const Message& msg, ClientHandler* client)
{
    if (!requireSellerRole(client)) {
        return createErrorResponse(Protocol::ErrorMessage::PERMISSION_DENIED, Protocol::Status::UNAUTHORIZED);
    }

    if (!m_productManager) {
        return createErrorResponse(Protocol::ErrorMessage::SERVER_ERROR, Protocol::Status::INTERNAL_ERROR);
    }

    QJsonObject data = msg.getData();
    int productId = data["productId"].toInt();

    Product* product = m_productManager->getProductById(productId);
    if (!product) {
        return createErrorResponse(Protocol::ErrorMessage::PRODUCT_NOT_FOUND, Protocol::Status::NOT_FOUND);
    }

    // 检查是否是商品所有者
    if (product->getOwner() != client->getCurrentUser()->getUsername()) {
        return createErrorResponse(Protocol::ErrorMessage::PERMISSION_DENIED, Protocol::Status::UNAUTHORIZED);
    }

    // 更新商品信息（这里简化处理，实际可能需要更复杂的更新逻辑）
    if (data.contains("price")) {
        double newPrice = data["price"].toDouble();
        if (newPrice > 0) {
            m_productManager->updateProductPrice(productId, newPrice);
        }
    }

    if (data.contains("stock")) {
        int newStock = data["stock"].toInt();
        if (newStock >= 0) {
            m_productManager->updateProductStock(productId, newStock);
        }
    }

    if (data.contains("discount")) {
        double newDiscount = data["discount"].toDouble();
        if (newDiscount >= 0 && newDiscount <= 1.0) {
            m_productManager->updateProductDiscount(productId, newDiscount);
        }
    }

    return createSuccessResponse(Protocol::SuccessMessage::PRODUCT_UPDATED);
}

Response MessageDispatcher::handleUpdateProductStock(const Message& msg, ClientHandler* client)
{
    if (!requireSellerRole(client)) {
        return createErrorResponse(Protocol::ErrorMessage::PERMISSION_DENIED, Protocol::Status::UNAUTHORIZED);
    }

    if (!m_productManager) {
        return createErrorResponse(Protocol::ErrorMessage::SERVER_ERROR, Protocol::Status::INTERNAL_ERROR);
    }

    QJsonObject data = msg.getData();
    int productId = data["productId"].toInt();
    int newStock = data["stock"].toInt();

    Product* product = m_productManager->getProductById(productId);
    if (!product) {
        return createErrorResponse(Protocol::ErrorMessage::PRODUCT_NOT_FOUND, Protocol::Status::NOT_FOUND);
    }

    if (product->getOwner() != client->getCurrentUser()->getUsername()) {
        return createErrorResponse(Protocol::ErrorMessage::PERMISSION_DENIED, Protocol::Status::UNAUTHORIZED);
    }

    if (newStock < 0) {
        return createErrorResponse("库存数量不能为负数");
    }

    if (!m_productManager->updateProductStock(productId, newStock)) {
        return createErrorResponse("更新库存失败");
    }

    return createSuccessResponse("库存更新成功");
}

Response MessageDispatcher::handleUpdateProductPrice(const Message& msg, ClientHandler* client)
{
    if (!requireSellerRole(client)) {
        return createErrorResponse(Protocol::ErrorMessage::PERMISSION_DENIED, Protocol::Status::UNAUTHORIZED);
    }

    if (!m_productManager) {
        return createErrorResponse(Protocol::ErrorMessage::SERVER_ERROR, Protocol::Status::INTERNAL_ERROR);
    }

    QJsonObject data = msg.getData();
    int productId = data["productId"].toInt();
    double newPrice = data["price"].toDouble();

    Product* product = m_productManager->getProductById(productId);
    if (!product) {
        return createErrorResponse(Protocol::ErrorMessage::PRODUCT_NOT_FOUND, Protocol::Status::NOT_FOUND);
    }

    if (product->getOwner() != client->getCurrentUser()->getUsername()) {
        return createErrorResponse(Protocol::ErrorMessage::PERMISSION_DENIED, Protocol::Status::UNAUTHORIZED);
    }

    if (newPrice <= 0) {
        return createErrorResponse("价格必须大于0");
    }

    if (!m_productManager->updateProductPrice(productId, newPrice)) {
        return createErrorResponse("更新价格失败");
    }

    return createSuccessResponse("价格更新成功");
}

Response MessageDispatcher::handleUpdateProductDiscount(const Message& msg, ClientHandler* client)
{
    if (!requireSellerRole(client)) {
        return createErrorResponse(Protocol::ErrorMessage::PERMISSION_DENIED, Protocol::Status::UNAUTHORIZED);
    }

    if (!m_productManager) {
        return createErrorResponse(Protocol::ErrorMessage::SERVER_ERROR, Protocol::Status::INTERNAL_ERROR);
    }

    QJsonObject data = msg.getData();
    int productId = data["productId"].toInt();
    double newDiscount = data["discount"].toDouble();

    Product* product = m_productManager->getProductById(productId);
    if (!product) {
        return createErrorResponse(Protocol::ErrorMessage::PRODUCT_NOT_FOUND, Protocol::Status::NOT_FOUND);
    }

    if (product->getOwner() != client->getCurrentUser()->getUsername()) {
        return createErrorResponse(Protocol::ErrorMessage::PERMISSION_DENIED, Protocol::Status::UNAUTHORIZED);
    }

    if (newDiscount < 0 || newDiscount > 1.0) {
        return createErrorResponse("折扣必须在0到1之间");
    }

    if (!m_productManager->updateProductDiscount(productId, newDiscount)) {
        return createErrorResponse("更新折扣失败");
    }

    return createSuccessResponse("折扣更新成功");
}

Response MessageDispatcher::handleBatchUpdateDiscount(const Message& msg, ClientHandler* client)
{
    if (!requireSellerRole(client)) {
        return createErrorResponse(Protocol::ErrorMessage::PERMISSION_DENIED, Protocol::Status::UNAUTHORIZED);
    }

    if (!m_productManager) {
        return createErrorResponse(Protocol::ErrorMessage::SERVER_ERROR, Protocol::Status::INTERNAL_ERROR);
    }

    QJsonObject data = msg.getData();
    int category = data["category"].toInt();
    double newDiscount = data["discount"].toDouble();

    if (newDiscount < 0 || newDiscount > 1.0) {
        return createErrorResponse("折扣必须在0到1之间");
    }

    QString currentUser = client->getCurrentUser()->getUsername();
    QVector<Product*> allProducts = m_productManager->getAllProducts();
    int updatedCount = 0;

    for (Product* product : allProducts) {
        // 只更新当前用户的指定类型商品
        if (product->getOwner() == currentUser &&
            static_cast<int>(product->getCategory()) == category) {
            if (m_productManager->updateProductDiscount(product->getId(), newDiscount)) {
                updatedCount++;
            }
        }
    }

    if (updatedCount == 0) {
        return createErrorResponse("没有找到符合条件的商品");
    }

    return createSuccessResponse(QString("成功更新了 %1 个商品的折扣").arg(updatedCount));
}

Response MessageDispatcher::handleAddToCart(const Message& msg, ClientHandler* client)
{
    if (!requireLogin(client)) {
        return createErrorResponse(Protocol::ErrorMessage::INVALID_CREDENTIALS, Protocol::Status::UNAUTHORIZED);
    }

    if (!m_productManager) {
        return createErrorResponse(Protocol::ErrorMessage::SERVER_ERROR, Protocol::Status::INTERNAL_ERROR);
    }

    QJsonObject data = msg.getData();
    int productId = data["productId"].toInt();
    int quantity = data["quantity"].toInt(1);

    if (quantity <= 0) {
        return createErrorResponse("数量必须大于0");
    }

    Product* product = m_productManager->getProductById(productId);
    if (!product) {
        return createErrorResponse(Protocol::ErrorMessage::PRODUCT_NOT_FOUND, Protocol::Status::NOT_FOUND);
    }

    // 允许添加超过库存的数量到购物车

    // 获取或创建购物车
    if (!m_carts.contains(client)) {
        m_carts[client] = QVector<QPair<int, int>>();
    }

    QVector<QPair<int, int>>& cart = m_carts[client];

    // 检查商品是否已在购物车中
    bool found = false;
    for (auto& item : cart) {
        if (item.first == productId) {
            item.second += quantity;
            found = true;
            break;
        }
    }

    if (!found) {
        cart.append(qMakePair(productId, quantity));
    }

    return createSuccessResponse(Protocol::SuccessMessage::CART_UPDATED);
}

Response MessageDispatcher::handleRemoveFromCart(const Message& msg, ClientHandler* client)
{
    if (!requireLogin(client)) {
        return createErrorResponse(Protocol::ErrorMessage::INVALID_CREDENTIALS, Protocol::Status::UNAUTHORIZED);
    }

    QJsonObject data = msg.getData();
    int productId = data["productId"].toInt();

    if (!m_carts.contains(client)) {
        return createErrorResponse("购物车为空");
    }

    QVector<QPair<int, int>>& cart = m_carts[client];

    for (int i = 0; i < cart.size(); ++i) {
        if (cart[i].first == productId) {
            cart.removeAt(i);
            return createSuccessResponse(Protocol::SuccessMessage::CART_UPDATED);
        }
    }

    return createErrorResponse("商品不在购物车中");
}

Response MessageDispatcher::handleGetCart(const Message& msg, ClientHandler* client)
{
    if (!requireLogin(client)) {
        return createErrorResponse(Protocol::ErrorMessage::INVALID_CREDENTIALS, Protocol::Status::UNAUTHORIZED);
    }

    if (!m_productManager) {
        return createErrorResponse(Protocol::ErrorMessage::SERVER_ERROR, Protocol::Status::INTERNAL_ERROR);
    }

    QJsonArray cartArray;

    if (m_carts.contains(client)) {
        const QVector<QPair<int, int>>& cart = m_carts[client];

        for (const auto& item : cart) {
            Product* product = m_productManager->getProductById(item.first);
            if (product) {
                QJsonObject cartItem;
                cartItem["productId"] = item.first;
                cartItem["quantity"] = item.second;
                cartItem["productName"] = product->getName();
                cartItem["price"] = product->getDiscountedPrice();
                cartItem["totalPrice"] = product->getDiscountedPrice() * item.second;
                cartArray.append(cartItem);
            }
        }
    }

    QJsonObject responseData;
    responseData["cartItems"] = cartArray;

    return createSuccessResponse("获取购物车成功", responseData);
}

Response MessageDispatcher::handleClearCart(const Message& msg, ClientHandler* client)
{
    if (!requireLogin(client)) {
        return createErrorResponse(Protocol::ErrorMessage::INVALID_CREDENTIALS, Protocol::Status::UNAUTHORIZED);
    }

    if (m_carts.contains(client)) {
        m_carts[client].clear();
    }

    return createSuccessResponse("购物车已清空");
}

Response MessageDispatcher::handleReserveOrder(const Message& msg, ClientHandler* client)
{
    if (!requireLogin(client)) {
        return createErrorResponse(Protocol::ErrorMessage::INVALID_CREDENTIALS, Protocol::Status::UNAUTHORIZED);
    }

    if (!m_productManager || !m_userManager) {
        return createErrorResponse(Protocol::ErrorMessage::SERVER_ERROR, Protocol::Status::INTERNAL_ERROR);
    }

    QJsonObject data = msg.getData();
    QJsonArray orderItems = data["orderItems"].toArray();

    if (orderItems.isEmpty()) {
        return createErrorResponse("订单为空，无法预留");
    }

    double totalAmount = 0.0;
    QVector<QPair<int, int>> itemsToProcess; // productId, quantity

    // 验证订单项并计算总金额
    for (const QJsonValue& value : orderItems) {
        QJsonObject itemObj = value.toObject();
        int productId = itemObj["productId"].toInt();
        int quantity = itemObj["quantity"].toInt();

        if (quantity <= 0) {
            continue; // 跳过数量为0的商品
        }

        Product* product = m_productManager->getProductById(productId);
        if (!product) {
            return createErrorResponse(QString("商品ID %1 不存在").arg(productId));
        }

        if (product->getStock() < quantity) {
            return createErrorResponse(QString("商品 %1 库存不足！需要：%2，库存：%3")
                .arg(product->getName()).arg(quantity).arg(product->getStock()));
        }

        totalAmount += product->getDiscountedPrice() * quantity;
        itemsToProcess.append(qMakePair(productId, quantity));
    }

    if (itemsToProcess.isEmpty()) {
        return createErrorResponse("没有有效的订单项");
    }

    // 清理该客户端之前的预订单（如果有）
    if (m_reservedOrders.contains(client)) {
        qDebug() << "清理客户端之前的预订单";
        rollbackStock(m_reservedOrders[client].second);
        m_reservedOrders.remove(client);
    }

    // 立即扣减库存并备份
    qDebug() << "开始预订单处理，用户:" << client->getCurrentUser()->getUsername()
             << "订单金额:" << totalAmount;

    QVector<QPair<int, int>> stockBackup; // 备份原始库存
    for (const auto& item : itemsToProcess) {
        Product* product = m_productManager->getProductById(item.first);
        int originalStock = product->getStock();
        stockBackup.append(qMakePair(item.first, originalStock));
        int newStock = originalStock - item.second;
        m_productManager->updateProductStock(item.first, newStock);

        qDebug() << "预订单库存扣减 - 商品ID:" << item.first
                 << "原库存:" << originalStock
                 << "扣减数量:" << item.second
                 << "新库存:" << newStock;
    }

    // 保存预订单信息
    m_reservedOrders[client] = qMakePair(itemsToProcess, stockBackup);

    QJsonObject responseData;
    responseData["totalAmount"] = totalAmount;
    responseData["reservedItems"] = orderItems;
    responseData["userBalance"] = client->getCurrentUser()->getBalance();

    qDebug() << "预订单创建成功，库存已预留";
    return createSuccessResponse(Protocol::SuccessMessage::ORDER_RESERVED, responseData);
}

Response MessageDispatcher::handleCreateOrder(const Message& msg, ClientHandler* client)
{
    if (!requireLogin(client)) {
        return createErrorResponse(Protocol::ErrorMessage::INVALID_CREDENTIALS, Protocol::Status::UNAUTHORIZED);
    }

    if (!m_productManager || !m_userManager) {
        return createErrorResponse(Protocol::ErrorMessage::SERVER_ERROR, Protocol::Status::INTERNAL_ERROR);
    }

    // 检查是否有预订单
    if (!m_reservedOrders.contains(client)) {
        return createErrorResponse("没有找到预订单，请先预留订单");
    }

    auto reservedOrder = m_reservedOrders[client];
    QVector<QPair<int, int>> itemsToProcess = reservedOrder.first;
    QVector<QPair<int, int>> stockBackup = reservedOrder.second;

    // 计算总金额
    double totalAmount = 0.0;
    for (const auto& item : itemsToProcess) {
        Product* product = m_productManager->getProductById(item.first);
        if (!product) {
            // 商品不存在，回滚库存
            rollbackStock(stockBackup);
            m_reservedOrders.remove(client);
            return createErrorResponse(QString("商品ID %1 不存在").arg(item.first));
        }
        totalAmount += product->getDiscountedPrice() * item.second;
    }

    qDebug() << "开始处理预订单支付，用户:" << client->getCurrentUser()->getUsername()
             << "订单金额:" << totalAmount;

    // 尝试支付处理
    User* user = client->getCurrentUser();
    QString paymentError;

    if (!processOrderPayment(itemsToProcess, totalAmount, user, paymentError)) {
        // 支付失败，回滚库存
        qDebug() << "支付失败，开始回滚库存，错误信息:" << paymentError;
        rollbackStock(stockBackup);
        m_reservedOrders.remove(client);
        qDebug() << "库存回滚完成";
        return createErrorResponse(paymentError);
    }

    qDebug() << "支付成功，订单处理完成";

    // 支付成功，清除预订单记录
    m_reservedOrders.remove(client);

    // 保存用户数据
    m_userManager->saveUsers();

    // 从购物车中移除已购买的商品
    if (m_carts.contains(client)) {
        QVector<QPair<int, int>>& cart = m_carts[client];
        for (const auto& orderItem : itemsToProcess) {
            for (int i = cart.size() - 1; i >= 0; --i) {
                if (cart[i].first == orderItem.first) {
                    cart.removeAt(i);
                    break;
                }
            }
        }
    }

    QJsonObject responseData;
    responseData["totalAmount"] = totalAmount;
    responseData["newBalance"] = user->getBalance();
    responseData["orderStatus"] = "completed";

    return createSuccessResponse(Protocol::SuccessMessage::ORDER_CREATED, responseData);
}

Response MessageDispatcher::handleCancelOrder(const Message& msg, ClientHandler* client)
{
    Q_UNUSED(msg)

    if (!requireLogin(client)) {
        return createErrorResponse(Protocol::ErrorMessage::INVALID_CREDENTIALS, Protocol::Status::UNAUTHORIZED);
    }

    // 检查是否有预订单
    if (!m_reservedOrders.contains(client)) {
        return createErrorResponse("没有找到预订单");
    }

    auto reservedOrder = m_reservedOrders[client];
    QVector<QPair<int, int>> stockBackup = reservedOrder.second;

    qDebug() << "开始取消预订单，用户:" << client->getCurrentUser()->getUsername();

    // 回滚库存
    rollbackStock(stockBackup);

    // 清除预订单记录
    m_reservedOrders.remove(client);

    qDebug() << "预订单取消完成，库存已恢复";

    return createSuccessResponse(Protocol::SuccessMessage::ORDER_CANCELLED);
}

Response MessageDispatcher::handleGetOrders(const Message& msg, ClientHandler* client)
{
    if (!requireLogin(client)) {
        return createErrorResponse(Protocol::ErrorMessage::INVALID_CREDENTIALS, Protocol::Status::UNAUTHORIZED);
    }

    // 简化实现：返回空订单列表
    // 在实际实现中，这里应该从数据库或文件中读取用户的订单历史
    QJsonArray ordersArray;

    QJsonObject responseData;
    responseData["orders"] = ordersArray;

    return createSuccessResponse("获取订单列表成功", responseData);
}

bool MessageDispatcher::processOrderPayment(const QVector<QPair<int, int>>& itemsToProcess,
                                           double totalAmount, User* user, QString& errorMsg)
{
    // 检查用户余额
    if (user->getBalance() < totalAmount) {
        errorMsg = QString("余额不足！需要：￥%1，余额：￥%2")
            .arg(totalAmount, 0, 'f', 2).arg(user->getBalance(), 0, 'f', 2);
        return false;
    }

    // 扣除用户余额
    if (!m_userManager->deductBalance(user->getUsername(), totalAmount)) {
        errorMsg = "扣除余额失败";
        return false;
    }

    // 计算每个商家的收入，并充值到商家账户
    QMap<QString, double> sellerIncome;
    for (const auto& item : itemsToProcess) {
        Product* product = m_productManager->getProductById(item.first);
        QString sellerName = product->getOwner();
        double income = product->getDiscountedPrice() * item.second;
        sellerIncome[sellerName] += income;
    }

    // 给商家账户充值
    for (auto it = sellerIncome.constBegin(); it != sellerIncome.constEnd(); ++it) {
        if (!m_userManager->rechargeBalance(it.key(), it.value())) {
            // 如果给商家充值失败，记录错误但不回滚整个交易
            qWarning() << "Failed to recharge balance for seller:" << it.key() << "amount:" << it.value();
        }
    }

    return true;
}

void MessageDispatcher::rollbackStock(const QVector<QPair<int, int>>& stockBackup)
{
    for (const auto& backup : stockBackup) {
        m_productManager->updateProductStock(backup.first, backup.second);
        qDebug() << "库存回滚 - 商品ID:" << backup.first << "恢复库存:" << backup.second;
    }
}

void MessageDispatcher::cleanupClientData(ClientHandler* client)
{
    // 清理购物车数据
    if (m_carts.contains(client)) {
        m_carts.remove(client);
        qDebug() << "清理客户端购物车数据:" << client->getClientInfo();
    }

    // 清理预订单数据并回滚库存
    if (m_reservedOrders.contains(client)) {
        auto reservedOrder = m_reservedOrders[client];
        QVector<QPair<int, int>> stockBackup = reservedOrder.second;

        qDebug() << "客户端断开连接，回滚预订单库存:" << client->getClientInfo();
        rollbackStock(stockBackup);

        m_reservedOrders.remove(client);
        qDebug() << "清理客户端预订单数据完成";
    }
}

Response MessageDispatcher::handlePayOrder(const Message& msg, ClientHandler* client)
{
    if (!requireLogin(client)) {
        return createErrorResponse(Protocol::ErrorMessage::INVALID_CREDENTIALS, Protocol::Status::UNAUTHORIZED);
    }

    // 简化实现：直接返回支付成功
    // 在实际实现中，这里应该处理具体的支付逻辑
    return createSuccessResponse(Protocol::SuccessMessage::PAYMENT_SUCCESS);
}

Response MessageDispatcher::handleGetUserInfo(const Message& msg, ClientHandler* client)
{
    Q_UNUSED(msg)

    if (!requireLogin(client)) {
        return createErrorResponse(Protocol::ErrorMessage::INVALID_CREDENTIALS, Protocol::Status::UNAUTHORIZED);
    }

    if (!m_userManager) {
        return createErrorResponse(Protocol::ErrorMessage::SERVER_ERROR, Protocol::Status::INTERNAL_ERROR);
    }

    User* user = client->getCurrentUser();
    if (!user) {
        return createErrorResponse(Protocol::ErrorMessage::USER_NOT_FOUND, Protocol::Status::NOT_FOUND);
    }

    // 构建用户信息响应
    QJsonObject userInfo;
    userInfo["username"] = user->getUsername();
    userInfo["userType"] = user->getUserType() == User::ConsumerType ? "consumer" : "seller";
    userInfo["balance"] = user->getBalance();
    userInfo["signature"] = user->getSignature();
    userInfo["avatarPath"] = user->getAvatarPath();

    QJsonObject responseData;
    responseData["userInfo"] = userInfo;

    return createSuccessResponse("获取用户信息成功", responseData);
}
