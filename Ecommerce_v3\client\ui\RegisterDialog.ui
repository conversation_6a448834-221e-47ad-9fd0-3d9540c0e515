<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>RegisterDialog</class>
 <widget class="QDialog" name="RegisterDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>400</width>
    <height>250</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>注册新账户</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>8</number>
   </property>
   <property name="leftMargin">
    <number>12</number>
   </property>
   <property name="topMargin">
    <number>12</number>
   </property>
   <property name="rightMargin">
    <number>12</number>
   </property>
   <property name="bottomMargin">
    <number>12</number>
   </property>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_username">
     <property name="spacing">
      <number>6</number>
     </property>
     <item>
      <widget class="QLabel" name="labelUsername">
       <property name="text">
        <string>用户名：</string>
       </property>
       <property name="minimumSize">
        <size>
         <width>80</width>
         <height>0</height>
        </size>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="usernameLineEdit">
       <property name="placeholderText">
        <string>请输入用户名</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_password">
     <property name="spacing">
      <number>6</number>
     </property>
     <item>
      <widget class="QLabel" name="labelPassword">
       <property name="text">
        <string>密码：</string>
       </property>
       <property name="minimumSize">
        <size>
         <width>80</width>
         <height>0</height>
        </size>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="passwordLineEdit">
       <property name="placeholderText">
        <string>请输入密码（至少6位）</string>
       </property>
       <property name="echoMode">
        <enum>QLineEdit::Password</enum>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_confirm">
     <property name="spacing">
      <number>6</number>
     </property>
     <item>
      <widget class="QLabel" name="labelConfirm">
       <property name="text">
        <string>确认密码：</string>
       </property>
       <property name="minimumSize">
        <size>
         <width>80</width>
         <height>0</height>
        </size>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="confirmLineEdit">
       <property name="placeholderText">
        <string>请再次输入密码</string>
       </property>
       <property name="echoMode">
        <enum>QLineEdit::Password</enum>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_userType">
     <property name="spacing">
      <number>6</number>
     </property>
     <item>
      <widget class="QLabel" name="labelUserType">
       <property name="text">
        <string>用户类型：</string>
       </property>
       <property name="minimumSize">
        <size>
         <width>80</width>
         <height>0</height>
        </size>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="userTypeCombo"/>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_button">
     <property name="spacing">
      <number>12</number>
     </property>
     <item>
      <spacer name="horizontalSpacer_left">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeType">
        <enum>QSizePolicy::Expanding</enum>
       </property>
       <property name="sizeHint">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="registerButton">
       <property name="minimumSize">
        <size>
         <width>80</width>
         <height>30</height>
        </size>
       </property>
       <property name="text">
        <string>注册</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_right">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeType">
        <enum>QSizePolicy::Expanding</enum>
       </property>
       <property name="sizeHint">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
