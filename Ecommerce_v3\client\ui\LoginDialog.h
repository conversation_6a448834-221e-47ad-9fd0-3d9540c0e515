#ifndef LOGINDIALOG_H
#define LOGINDIALOG_H

#include <QDialog>

class ClientManager;

namespace Ui {
class LoginDialog;
}

class LoginDialog : public QDialog
{
    Q_OBJECT

public:
    explicit LoginDialog(ClientManager* clientMgr, QWidget *parent = nullptr);
    ~LoginDialog();

private slots:
    void on_loginButton_clicked();
    void on_registerButton_clicked();

private:
    void applyStyles();

    Ui::LoginDialog *ui;
    ClientManager* m_clientMgr;
};

#endif // LOGINDIALOG_H
