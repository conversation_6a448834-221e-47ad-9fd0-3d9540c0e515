#ifndef EDITPRODUCTDIALOG_H
#define EDITPRODUCTDIALOG_H

#include <QDialog>
#include "../network/ClientManager.h"

namespace Ui {
class EditProductDialog;
}

class EditProductDialog : public QDialog
{
    Q_OBJECT

public:
    explicit EditProductDialog(const ClientProduct& product, ClientManager* clientMgr, QWidget *parent = nullptr);
    ~EditProductDialog();

private slots:
    void on_updateButton_clicked();
    void on_cancelButton_clicked();

private:
    void setupUI();
    void loadProductData();

private:
    Ui::EditProductDialog *ui;
    ClientManager* m_clientMgr;
    ClientProduct m_product;
};

#endif // EDITPRODUCTDIALOG_H
