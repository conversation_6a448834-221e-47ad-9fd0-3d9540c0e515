#include "RegisterDialog.h"
#include "ui_RegisterDialog.h"
#include "../network/ClientManager.h"
#include <QMessageBox>

RegisterDialog::RegisterDialog(ClientManager* clientMgr, QWidget *parent) :
    QDialog(parent),
    ui(new Ui::RegisterDialog),
    m_clientMgr(clientMgr)
{
    ui->setupUi(this);
    setWindowTitle("注册新账户");

    // 设置密码输入框为密码模式
    ui->passwordLineEdit->setEchoMode(QLineEdit::Password);
    ui->confirmLineEdit->setEchoMode(QLineEdit::Password);

    // 用户类型选择
    ui->userTypeCombo->addItem("消费者", "consumer");
    ui->userTypeCombo->addItem("商家", "seller");
}

RegisterDialog::~RegisterDialog()
{
    delete ui;
}

void RegisterDialog::on_registerButton_clicked()
{
    QString username = ui->usernameLineEdit->text().trimmed();
    QString password = ui->passwordLineEdit->text();
    QString confirm = ui->confirmLineEdit->text();
    QString userType = ui->userTypeCombo->currentData().toString();

    if (username.isEmpty() || password.isEmpty()) {
        QMessageBox::warning(this, "输入错误", "用户名和密码不能为空。");
        return;
    }

    if (password != confirm) {
        QMessageBox::warning(this, "输入错误", "两次输入的密码不一致。");
        return;
    }

    if (password.length() < 6) {
        QMessageBox::warning(this, "输入错误", "密码长度至少6位。");
        return;
    }

    QString errorMsg;
    if (!m_clientMgr->registerUser(username, password, userType, errorMsg)) {
        QMessageBox::warning(this, "注册失败", errorMsg);
        return;
    }

    QMessageBox::information(this, "注册成功", "账户注册成功！请使用新账户登录。");
    accept();
}
