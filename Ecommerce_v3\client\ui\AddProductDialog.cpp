#include "AddProductDialog.h"
#include "ui_AddProductDialog.h"
#include "ImageManager.h"
#include "../network/ClientManager.h"
#include <QFileDialog>
#include <QPixmap>
#include <QMessageBox>

AddProductDialog::AddProductDialog(ClientManager* clientMgr, QWidget *parent) :
    QDialog(parent),
    ui(new Ui::AddProductDialog),
    m_clientMgr(clientMgr)
{
    ui->setupUi(this);
    setupUI();
}

AddProductDialog::~AddProductDialog()
{
    delete ui;
}

void AddProductDialog::setupUI()
{
    setWindowTitle("添加商品");
    setModal(true);
    
    // 设置商品类别
    ui->categoryCombo->addItem("图书", 0);
    ui->categoryCombo->addItem("服装", 1);
    ui->categoryCombo->addItem("食品", 2);
    
    // 设置默认值
    ui->discountSpinBox->setValue(1.0);
    ui->discountSpinBox->setRange(0.1, 1.0);
    ui->discountSpinBox->setSingleStep(0.1);
    
    ui->priceSpinBox->setRange(0.01, 999999.99);
    ui->priceSpinBox->setSingleStep(0.01);
    
    ui->stockSpinBox->setRange(0, 999999);
    
    // 初始化额外字段
    updateExtraFields();

    // 连接图片相关信号槽
    connect(ui->selectImageButton, &QPushButton::clicked, this, &AddProductDialog::onSelectImageClicked);
    connect(ui->clearImageButton, &QPushButton::clicked, this, &AddProductDialog::onClearImageClicked);

    // 初始化图片预览
    updateImagePreview();
}

void AddProductDialog::updateExtraFields()
{
    int category = ui->categoryCombo->currentData().toInt();
    
    switch (category) {
        case 0: // 图书
            ui->extra1Label->setText("作者：");
            ui->extra2Label->setText("");
            ui->extra1LineEdit->setVisible(true);
            ui->extra2LineEdit->setVisible(false);
            ui->extra2Label->setVisible(false);
            ui->extra1LineEdit->setPlaceholderText("请输入作者姓名");
            break;
            
        case 1: // 服装
            ui->extra1Label->setText("尺寸：");
            ui->extra2Label->setText("颜色：");
            ui->extra1LineEdit->setVisible(true);
            ui->extra2LineEdit->setVisible(true);
            ui->extra2Label->setVisible(true);
            ui->extra1LineEdit->setPlaceholderText("如：S/M/L/XL");
            ui->extra2LineEdit->setPlaceholderText("如：红色/蓝色");
            break;
            
        case 2: // 食品
            ui->extra1Label->setText("");
            ui->extra2Label->setText("");
            ui->extra1LineEdit->setVisible(false);
            ui->extra2LineEdit->setVisible(false);
            ui->extra2Label->setVisible(false);
            break;
    }
}

void AddProductDialog::on_categoryCombo_currentIndexChanged(int index)
{
    Q_UNUSED(index)
    updateExtraFields();
    updateImagePreview(); // 更新图片预览以显示新类别的默认图片
}

void AddProductDialog::on_addButton_clicked()
{
    QString name = ui->nameLineEdit->text().trimmed();
    double price = ui->priceSpinBox->value();
    int stock = ui->stockSpinBox->value();
    double discount = ui->discountSpinBox->value();
    int category = ui->categoryCombo->currentData().toInt();
    QString remark = ui->remarkTextEdit->toPlainText().trimmed();
    
    if (name.isEmpty()) {
        QMessageBox::warning(this, "输入错误", "商品名称不能为空");
        return;
    }
    
    if (price <= 0) {
        QMessageBox::warning(this, "输入错误", "商品价格必须大于0");
        return;
    }
    
    QString extra1 = ui->extra1LineEdit->text().trimmed();
    QString extra2 = ui->extra2LineEdit->text().trimmed();
    
    // 根据类别验证必填字段
    if (category == 0 && extra1.isEmpty()) { // 图书需要作者
        QMessageBox::warning(this, "输入错误", "图书类商品必须填写作者");
        return;
    }
    
    if (category == 1 && (extra1.isEmpty() || extra2.isEmpty())) { // 服装需要尺寸和颜色
        QMessageBox::warning(this, "输入错误", "服装类商品必须填写尺寸和颜色");
        return;
    }
    
    // 处理图片上传
    QString savedImagePath;
    if (!m_selectedImagePath.isEmpty()) {
        // 先添加商品获取ID，然后保存图片
        // 这里我们需要修改逻辑，先发送商品信息，获取商品ID后再保存图片
        // 暂时传递选择的图片路径，服务器端需要处理图片保存
        savedImagePath = m_selectedImagePath;
    }

    QString errorMsg;
    if (!m_clientMgr->addProduct(category, name, price, stock, discount, extra1, extra2, remark, savedImagePath, errorMsg)) {
        QMessageBox::warning(this, "添加失败", errorMsg);
        return;
    }

    QMessageBox::information(this, "成功", "商品添加成功！");
    accept();
}

void AddProductDialog::on_cancelButton_clicked()
{
    reject();
}

void AddProductDialog::onSelectImageClicked()
{
    QString fileName = QFileDialog::getOpenFileName(this,
        "选择商品图片", "",
        "图片文件 (*.png *.jpg *.jpeg *.bmp *.gif)");

    if (!fileName.isEmpty()) {
        m_selectedImagePath = fileName;
        updateImagePreview();
    }
}

void AddProductDialog::onClearImageClicked()
{
    m_selectedImagePath.clear();
    updateImagePreview();
}

void AddProductDialog::updateImagePreview()
{
    if (m_selectedImagePath.isEmpty()) {
        // 显示默认图片
        int category = ui->categoryCombo->currentData().toInt();
        ImageManager& imageMgr = ImageManager::instance();
        QPixmap defaultImage = imageMgr.getDefaultProductImage(category, QSize(64, 64));
        ui->imagePreviewLabel->setPixmap(defaultImage);
        ui->imagePreviewLabel->setText("");
    } else {
        // 显示选择的图片
        QPixmap image(m_selectedImagePath);
        if (!image.isNull()) {
            image = image.scaled(64, 64, Qt::KeepAspectRatio, Qt::SmoothTransformation);
            ui->imagePreviewLabel->setPixmap(image);
            ui->imagePreviewLabel->setText("");
        } else {
            ui->imagePreviewLabel->clear();
            ui->imagePreviewLabel->setText("无效图片");
        }
    }
}
