#include "NetworkUtils.h"
#include <QDateTime>
#include <QDebug>

// 由于这是一个工具类，主要用于序列化，但在当前的实现中
// 我们直接在MessageDispatcher和ClientManager中处理JSON转换
// 这里提供一些基础的工具函数

QString NetworkUtils::generateTimestamp()
{
    return QDateTime::currentDateTime().toString(Qt::ISODate);
}

bool NetworkUtils::validateJsonObject(const QJsonObject& obj, const QStringList& requiredFields)
{
    for (const QString& field : requiredFields) {
        if (!obj.contains(field)) {
            qWarning() << "Missing required field:" << field;
            return false;
        }
    }
    return true;
}

QJsonObject NetworkUtils::cartItemToJson(const CartItem& item)
{
    QJsonObject json;
    json["productId"] = item.productId;
    json["quantity"] = item.quantity;
    json["productName"] = item.productName;
    json["price"] = item.price;
    return json;
}

NetworkUtils::CartItem NetworkUtils::cartItemFromJson(const QJsonObject& json)
{
    CartItem item;
    item.productId = json["productId"].toInt();
    item.quantity = json["quantity"].toInt();
    item.productName = json["productName"].toString();
    item.price = json["price"].toDouble();
    return item;
}

QJsonArray NetworkUtils::cartToJson(const QVector<CartItem>& cart)
{
    QJsonArray array;
    for (const CartItem& item : cart) {
        array.append(cartItemToJson(item));
    }
    return array;
}

QVector<NetworkUtils::CartItem> NetworkUtils::cartFromJson(const QJsonArray& jsonArray)
{
    QVector<CartItem> cart;
    for (const QJsonValue& value : jsonArray) {
        cart.append(cartItemFromJson(value.toObject()));
    }
    return cart;
}

// 注意：由于User、Product、Order等类在server端定义，
// 而NetworkUtils在common中，为了避免循环依赖，
// 这些序列化函数暂时留空或返回默认值
// 实际的序列化逻辑在各自的使用场景中实现

QJsonObject NetworkUtils::userToJson(const User* user)
{
    Q_UNUSED(user)
    // 这个函数在实际使用中会在server端的MessageDispatcher中实现
    return QJsonObject();
}

User* NetworkUtils::userFromJson(const QJsonObject& json)
{
    Q_UNUSED(json)
    // 这个函数在实际使用中会在client端根据需要实现
    return nullptr;
}

QJsonObject NetworkUtils::productToJson(const Product* product)
{
    Q_UNUSED(product)
    // 这个函数在实际使用中会在server端的MessageDispatcher中实现
    return QJsonObject();
}

Product* NetworkUtils::productFromJson(const QJsonObject& json)
{
    Q_UNUSED(json)
    // 这个函数在实际使用中会在client端根据需要实现
    return nullptr;
}

QJsonObject NetworkUtils::orderToJson(const Order* order)
{
    Q_UNUSED(order)
    // 这个函数在实际使用中会在server端的MessageDispatcher中实现
    return QJsonObject();
}

Order* NetworkUtils::orderFromJson(const QJsonObject& json)
{
    Q_UNUSED(json)
    // 这个函数在实际使用中会在client端根据需要实现
    return nullptr;
}

QJsonArray NetworkUtils::productListToJson(const QVector<Product*>& products)
{
    Q_UNUSED(products)
    // 这个函数在实际使用中会在server端的MessageDispatcher中实现
    return QJsonArray();
}

QVector<Product*> NetworkUtils::productListFromJson(const QJsonArray& jsonArray)
{
    Q_UNUSED(jsonArray)
    // 这个函数在实际使用中会在client端根据需要实现
    return QVector<Product*>();
}
