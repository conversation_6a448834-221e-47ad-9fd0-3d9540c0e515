#include "ProfileDialog.h"
#include "ui_ProfileDialog.h"
#include "../network/ClientManager.h"
#include <QMessageBox>
#include <QFileDialog>
#include <QPixmap>
#include <QStandardPaths>

ProfileDialog::ProfileDialog(ClientManager* clientMgr, QWidget *parent) :
    QDialog(parent),
    ui(new Ui::ProfileDialog),
    m_clientMgr(clientMgr)
{
    ui->setupUi(this);
    setupUI();
    loadCurrentInfo();
}

ProfileDialog::~ProfileDialog()
{
    delete ui;
}

void ProfileDialog::setupUI()
{
    setWindowTitle("个人信息设置");
    setModal(true);
    
    // 设置头像显示区域
    ui->avatarLabel->setFixedSize(100, 100);
    ui->avatarLabel->setScaledContents(true);
    ui->avatarLabel->setStyleSheet("border: 1px solid gray;");
    
    // 设置签名输入框
    ui->signatureTextEdit->setMaximumHeight(80);
}

void ProfileDialog::loadCurrentInfo()
{
    ui->usernameLabel->setText(m_clientMgr->getCurrentUsername());
    ui->userTypeLabel->setText(m_clientMgr->getCurrentUserType() == "consumer" ? "消费者" : "商家");
    ui->balanceLabel->setText(QString("￥%1").arg(m_clientMgr->getCurrentBalance(), 0, 'f', 2));
    
    ui->signatureTextEdit->setPlainText(m_clientMgr->getCurrentSignature());
    
    m_selectedAvatarPath = m_clientMgr->getCurrentAvatarPath();
    updateAvatarDisplay();
}

void ProfileDialog::updateAvatarDisplay()
{
    QPixmap pixmap;

    // 尝试加载选择的头像
    if (!m_selectedAvatarPath.isEmpty() && QFile::exists(m_selectedAvatarPath)) {
        pixmap.load(m_selectedAvatarPath);
    }

    // 如果没有选择头像或加载失败，使用默认头像
    if (pixmap.isNull()) {
        pixmap.load(":/avatars/default_avatar.png");
    }

    if (!pixmap.isNull()) {
        // 缩放头像到合适大小
        QPixmap scaledPixmap = pixmap.scaled(100, 100, Qt::KeepAspectRatio, Qt::SmoothTransformation);
        ui->avatarLabel->setPixmap(scaledPixmap);
    } else {
        ui->avatarLabel->setText("头像加载失败");
        ui->avatarLabel->setAlignment(Qt::AlignCenter);
    }
}

void ProfileDialog::on_selectAvatarButton_clicked()
{
    QString fileName = QFileDialog::getOpenFileName(
        this,
        "选择头像图片",
        QStandardPaths::writableLocation(QStandardPaths::PicturesLocation),
        "图片文件 (*.png *.jpg *.jpeg *.bmp *.gif)"
    );
    
    if (!fileName.isEmpty()) {
        m_selectedAvatarPath = fileName;
        updateAvatarDisplay();
    }
}

void ProfileDialog::on_updateButton_clicked()
{
    QString signature = ui->signatureTextEdit->toPlainText().trimmed();
    QString avatarPath = m_selectedAvatarPath;
    
    // 检查是否有变化
    bool signatureChanged = (signature != m_clientMgr->getCurrentSignature());
    bool avatarChanged = (avatarPath != m_clientMgr->getCurrentAvatarPath());
    
    if (!signatureChanged && !avatarChanged) {
        QMessageBox::information(this, "提示", "没有需要更新的信息");
        return;
    }
    
    QString errorMsg;
    QString updateSignature = signatureChanged ? signature : QString();
    QString updateAvatar = avatarChanged ? avatarPath : QString();
    
    if (!m_clientMgr->updateProfile(updateSignature, updateAvatar, errorMsg)) {
        QMessageBox::warning(this, "更新失败", errorMsg);
        return;
    }
    
    QMessageBox::information(this, "成功", "个人信息更新成功！");
    accept();
}

void ProfileDialog::on_cancelButton_clicked()
{
    reject();
}
