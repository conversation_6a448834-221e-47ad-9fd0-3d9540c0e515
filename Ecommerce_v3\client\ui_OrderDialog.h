/********************************************************************************
** Form generated from reading UI file 'OrderDialog.ui'
**
** Created by: Qt User Interface Compiler version 6.8.3
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_ORDERDIALOG_H
#define UI_ORDERDIALOG_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QDialog>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QVBoxLayout>

QT_BEGIN_NAMESPACE

class Ui_OrderDialog
{
public:
    QVBoxLayout *verticalLayout;
    QLabel *titleLabel;
    QTableWidget *orderTable;
    QHBoxLayout *totalLayout;
    QSpacerItem *totalSpacer;
    QLabel *totalAmountLabel;
    QSpacerItem *verticalSpacer;
    QHBoxLayout *buttonLayout;
    QSpacerItem *buttonSpacer;
    QPushButton *payOrderButton;
    QPushButton *cancelButton;

    void setupUi(QDialog *OrderDialog)
    {
        if (OrderDialog->objectName().isEmpty())
            OrderDialog->setObjectName("OrderDialog");
        OrderDialog->resize(600, 500);
        verticalLayout = new QVBoxLayout(OrderDialog);
        verticalLayout->setSpacing(8);
        verticalLayout->setObjectName("verticalLayout");
        verticalLayout->setContentsMargins(12, 12, 12, 12);
        titleLabel = new QLabel(OrderDialog);
        titleLabel->setObjectName("titleLabel");
        QFont font;
        font.setPointSize(10);
        font.setBold(true);
        titleLabel->setFont(font);

        verticalLayout->addWidget(titleLabel);

        orderTable = new QTableWidget(OrderDialog);
        orderTable->setObjectName("orderTable");
        orderTable->setSelectionBehavior(QAbstractItemView::SelectRows);
        orderTable->setSortingEnabled(false);

        verticalLayout->addWidget(orderTable);

        totalLayout = new QHBoxLayout();
        totalLayout->setObjectName("totalLayout");
        totalSpacer = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        totalLayout->addItem(totalSpacer);

        totalAmountLabel = new QLabel(OrderDialog);
        totalAmountLabel->setObjectName("totalAmountLabel");
        QFont font1;
        font1.setPointSize(12);
        font1.setBold(true);
        totalAmountLabel->setFont(font1);

        totalLayout->addWidget(totalAmountLabel);


        verticalLayout->addLayout(totalLayout);

        verticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        verticalLayout->addItem(verticalSpacer);

        buttonLayout = new QHBoxLayout();
        buttonLayout->setObjectName("buttonLayout");
        buttonSpacer = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        buttonLayout->addItem(buttonSpacer);

        payOrderButton = new QPushButton(OrderDialog);
        payOrderButton->setObjectName("payOrderButton");
        payOrderButton->setMinimumSize(QSize(100, 35));
        QFont font2;
        font2.setBold(true);
        payOrderButton->setFont(font2);

        buttonLayout->addWidget(payOrderButton);

        cancelButton = new QPushButton(OrderDialog);
        cancelButton->setObjectName("cancelButton");
        cancelButton->setMinimumSize(QSize(80, 35));

        buttonLayout->addWidget(cancelButton);


        verticalLayout->addLayout(buttonLayout);


        retranslateUi(OrderDialog);

        QMetaObject::connectSlotsByName(OrderDialog);
    } // setupUi

    void retranslateUi(QDialog *OrderDialog)
    {
        OrderDialog->setWindowTitle(QCoreApplication::translate("OrderDialog", "\350\256\242\345\215\225\347\241\256\350\256\244", nullptr));
        titleLabel->setText(QCoreApplication::translate("OrderDialog", "\350\256\242\345\215\225\350\257\246\346\203\205\357\274\232", nullptr));
        totalAmountLabel->setText(QCoreApplication::translate("OrderDialog", "\346\200\273\350\256\241\357\274\232\357\277\2450.00", nullptr));
        totalAmountLabel->setStyleSheet(QCoreApplication::translate("OrderDialog", "color: red;", nullptr));
        payOrderButton->setText(QCoreApplication::translate("OrderDialog", "\346\224\257\344\273\230", nullptr));
        payOrderButton->setStyleSheet(QCoreApplication::translate("OrderDialog", "QPushButton { background-color: #4CAF50; color: white; }\n"
"QPushButton:hover { background-color: #45a049; }\n"
"QPushButton:disabled { background-color: #cccccc; }", nullptr));
        cancelButton->setText(QCoreApplication::translate("OrderDialog", "\345\217\226\346\266\210", nullptr));
    } // retranslateUi

};

namespace Ui {
    class OrderDialog: public Ui_OrderDialog {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_ORDERDIALOG_H
