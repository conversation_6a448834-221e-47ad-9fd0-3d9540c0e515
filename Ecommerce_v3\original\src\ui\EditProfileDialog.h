#ifndef EDITPROFILEDIALOG_H
#define EDITPROFILEDIALOG_H

#include <QDialog>
#include "User.h"

namespace Ui {
class EditProfileDialog;
}

class UserManager;

class EditProfileDialog : public QDialog
{
    Q_OBJECT

public:
    explicit EditProfileDialog(User* user, UserManager* userMgr, QWidget *parent = nullptr);
    ~EditProfileDialog();

private slots:
    void on_changeAvatarButton_clicked();
    void on_saveButton_clicked();
    void on_cancelButton_clicked();

private:
    Ui::EditProfileDialog *ui;
    User* m_user;
    UserManager* m_userMgr;

    QString m_newAvatarPath;
};

#endif // EDITPROFILEDIALOG_H
