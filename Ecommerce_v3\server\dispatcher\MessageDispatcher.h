#ifndef MESSAGEDISPATCHER_H
#define MESSAGEDISPATCHER_H

#include <QObject>
#include <QString>
#include <QJsonObject>
#include <QMap>

class UserManager;
class ProductManager;
class ClientHandler;
class Message;
class Response;
class User;

class MessageDispatcher : public QObject
{
    Q_OBJECT

public:
    explicit MessageDispatcher(QObject* parent = nullptr);
    
    // 处理客户端消息
    QString processMessage(const QString& messageStr, ClientHandler* client);
    
    // 设置管理器
    void setUserManager(UserManager* userMgr) { m_userManager = userMgr; }
    void setProductManager(ProductManager* prodMgr) { m_productManager = prodMgr; }

    // 客户端管理
    void cleanupClientData(ClientHandler* client);

private:
    // 用户相关处理
    Response handleLogin(const Message& msg, ClientHandler* client);
    Response handleRegister(const Message& msg, ClientHandler* client);
    Response handleLogout(const Message& msg, ClientHandler* client);
    Response handleChangePassword(const Message& msg, ClientHandler* client);
    Response handleRechargeBalance(const Message& msg, ClientHandler* client);
    Response handleUpdateProfile(const Message& msg, ClientHandler* client);
    Response handleGetUserInfo(const Message& msg, ClientHandler* client);
    
    // 商品相关处理
    Response handleGetAllProducts(const Message& msg, ClientHandler* client);
    Response handleSearchProducts(const Message& msg, ClientHandler* client);
    Response handleFilterProducts(const Message& msg, ClientHandler* client);
    Response handleAddProduct(const Message& msg, ClientHandler* client);
    Response handleUpdateProduct(const Message& msg, ClientHandler* client);
    Response handleUpdateProductStock(const Message& msg, ClientHandler* client);
    Response handleUpdateProductPrice(const Message& msg, ClientHandler* client);
    Response handleUpdateProductDiscount(const Message& msg, ClientHandler* client);
    Response handleBatchUpdateDiscount(const Message& msg, ClientHandler* client);
    
    // 购物车相关处理
    Response handleAddToCart(const Message& msg, ClientHandler* client);
    Response handleRemoveFromCart(const Message& msg, ClientHandler* client);
    Response handleGetCart(const Message& msg, ClientHandler* client);
    Response handleClearCart(const Message& msg, ClientHandler* client);
    
    // 订单相关处理
    Response handleReserveOrder(const Message& msg, ClientHandler* client);  // 预订单（立即扣减库存）
    Response handleCreateOrder(const Message& msg, ClientHandler* client);   // 确认订单（执行支付）
    Response handleCancelOrder(const Message& msg, ClientHandler* client);   // 取消订单（回滚库存）
    Response handleGetOrders(const Message& msg, ClientHandler* client);
    Response handlePayOrder(const Message& msg, ClientHandler* client);

    // 订单处理辅助方法
    bool processOrderPayment(const QVector<QPair<int, int>>& itemsToProcess,
                           double totalAmount, User* user, QString& errorMsg);
    void rollbackStock(const QVector<QPair<int, int>>& stockBackup);

    // 预订单管理
    QMap<ClientHandler*, QPair<QVector<QPair<int, int>>, QVector<QPair<int, int>>>> m_reservedOrders;
    // ClientHandler -> (orderItems, stockBackup)
    
    // 工具方法
    bool requireLogin(ClientHandler* client);
    bool requireSellerRole(ClientHandler* client);
    Response createErrorResponse(const QString& message, int status = 400);
    Response createSuccessResponse(const QString& message, const QJsonObject& data = QJsonObject());

private:
    UserManager* m_userManager;
    ProductManager* m_productManager;
    
    // 购物车存储 (clientHandler -> cart items)
    QMap<ClientHandler*, QVector<QPair<int, int>>> m_carts; // productId, quantity
};

#endif // MESSAGEDISPATCHER_H
