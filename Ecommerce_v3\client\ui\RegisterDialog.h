#ifndef REGISTERDIALOG_H
#define REGISTERDIALOG_H

#include <QDialog>

class ClientManager;

namespace Ui {
class RegisterDialog;
}

class RegisterDialog : public QDialog
{
    Q_OBJECT

public:
    explicit RegisterDialog(ClientManager* clientMgr, QWidget *parent = nullptr);
    ~RegisterDialog();

private slots:
    void on_registerButton_clicked();

private:
    Ui::RegisterDialog *ui;
    ClientManager* m_clientMgr;
};

#endif // REGISTERDIALOG_H
