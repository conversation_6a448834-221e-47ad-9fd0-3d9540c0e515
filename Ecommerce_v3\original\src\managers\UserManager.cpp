#include "UserManager.h"
#include <QFile>
#include <QTextStream>
#include <QDebug>

UserManager::UserManager(const QString& userFilePath)
    : m_userFilePath(userFilePath)
{
}

UserManager::~UserManager()
{
    qDeleteAll(m_users);
    m_users.clear();
}

QStringList UserManager::splitCsvLine(const QString& line) const
{
    QStringList result;
    QString current;
    bool escaping = false;
    for (int i = 0; i < line.length(); ++i) {
        QChar c = line.at(i);
        if (escaping) {
            current.append(c);
            escaping = false;
        }
        else if (c == '\\') {
            escaping = true; // 下一个字符原样
        }
        else if (c == ',') {
            result.append(current);
            current.clear();
        }
        else {
            current.append(c);
        }
    }
    // 最后一段
    result.append(current);
    return result;
}

bool UserManager::loadUsers()
{
    m_users.clear();
    QFile file(m_userFilePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        qWarning() << "Cannot open user file:" << m_userFilePath;
        return false;
    }
    QTextStream in(&file);
    while (!in.atEnd()) {
        QString line = in.readLine().trimmed();
        if (line.isEmpty()) continue;
        QStringList parts = splitCsvLine(line);
        // 至少要有 6 个字段：username, hash, balance, type, signature, avatarPath
        if (parts.size() < 6) continue;

        QString username   = parts[0];
        QString pwdHash    = parts[1];
        double  balance    = parts[2].toDouble();
        int     typeInt    = parts[3].toInt();
        QString signature  = parts[4];
        QString avatarPath = parts[5];

        User* user = nullptr;
        if (typeInt == User::ConsumerType) {
            // 第二个参数 password 填 ""，后面覆盖 hash
            user = new Consumer(username, QString(), balance);
        } else {
            user = new Seller(username, QString(), balance);
        }
        user->setPasswordHashFromFile(pwdHash);
        user->setSignature(signature);
        user->setAvatarPath(avatarPath);

        m_users.append(user);
    }
    file.close();
    return true;
}

bool UserManager::saveUsers()
{
    QFile file(m_userFilePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Truncate | QIODevice::Text)) {
        qWarning() << "Cannot write to user file:" << m_userFilePath;
        return false;
    }
    QTextStream out(&file);
    for (User* u : m_users) {
        out << u->toCsvString() << "\n";
    }
    file.close();
    return true;
}

bool UserManager::usernameExists(const QString& username) const
{
    for (User* u : m_users) {
        if (u->getUsername() == username) return true;
    }
    return false;
}

bool UserManager::registerUser(const QString& username,
                               const QString& password,
                               User::UserType type)
{
    if (usernameExists(username)) return false;
    User* newUser = nullptr;
    if (type == User::ConsumerType) {
        newUser = new Consumer(username, password, 0.0);
    } else {
        newUser = new Seller(username, password, 0.0);
    }
    // 注册时使用默认空签名和默认头像
    newUser->setSignature("");
    newUser->setAvatarPath(":/default_avatar.png");

    m_users.append(newUser);
    return saveUsers();
}

User* UserManager::loginUser(const QString& username, const QString& password)
{
    for (User* u : m_users) {
        if (u->getUsername() == username && u->checkPassword(password)) {
            return u;
        }
    }
    return nullptr;
}

bool UserManager::changePassword(const QString& username,
                                 const QString& oldPwd,
                                 const QString& newPwd)
{
    User* u = getUserByName(username);
    if (!u) return false;
    if (!u->checkPassword(oldPwd)) return false;
    u->setPassword(newPwd);
    return saveUsers();
}

bool UserManager::rechargeBalance(const QString& username, double amount)
{
    if (amount <= 0) return false;
    User* u = getUserByName(username);
    if (!u) return false;
    u->setBalance(u->getBalance() + amount);
    return saveUsers();
}

bool UserManager::deductBalance(const QString& username, double amount)
{
    if (amount <= 0) return false;
    User* u = getUserByName(username);
    if (!u) return false;
    if (u->getBalance() < amount) return false;
    u->setBalance(u->getBalance() - amount);
    return saveUsers();
}

User* UserManager::getUserByName(const QString& username)
{
    for (User* u : m_users) {
        if (u->getUsername() == username) return u;
    }
    return nullptr;
}

bool UserManager::updateSignature(const QString& username, const QString& newSignature)
{
    User* u = getUserByName(username);
    if (!u) return false;
    u->setSignature(newSignature);
    return saveUsers();
}

bool UserManager::updateAvatarPath(const QString& username, const QString& newAvatarPath)
{
    User* u = getUserByName(username);
    if (!u) return false;
    u->setAvatarPath(newAvatarPath);
    return saveUsers();
}
