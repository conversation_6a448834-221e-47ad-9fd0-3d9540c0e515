#include "IconManager.h"
#include <QApplication>
#include <QDir>
#include <QDebug>

IconManager& IconManager::instance()
{
    static IconManager instance;
    return instance;
}

IconManager::IconManager()
    : m_defaultIconSize(32, 32)
{
    initializeIconMap();
}

void IconManager::initializeIconMap()
{
    // 初始化图标路径映射
    m_iconPaths[Refresh] = ":/icons/refresh.svg";
    m_iconPaths[Add] = ":/icons/add.svg";
    m_iconPaths[Edit] = ":/icons/edit.svg";
    m_iconPaths[Delete] = ":/icons/delete.svg";
    m_iconPaths[Search] = ":/icons/search.png";
    m_iconPaths[Cart] = ":/icons/cart.png";
    m_iconPaths[Order] = ":/icons/order.svg";
    m_iconPaths[Logout] = ":/icons/logout.png";
    m_iconPaths[Profile] = ":/icons/profile.png";
    m_iconPaths[Recharge] = ":/icons/recharge.svg";
    
    m_iconPaths[Success] = ":/icons/success.svg";
    m_iconPaths[Error] = ":/icons/error.svg";
    m_iconPaths[Warning] = ":/icons/warning.svg";
    m_iconPaths[Info] = ":/icons/info.svg";
    
    m_iconPaths[Book] = ":/icons/book.svg";
    m_iconPaths[Clothing] = ":/icons/clothing.svg";
    m_iconPaths[Food] = ":/icons/food.svg";
    
    m_iconPaths[DefaultAvatar] = ":/avatars/default_avatar.png";

    m_iconPaths[Password] = ":/icons/password.png";
}

QIcon IconManager::getIcon(IconType type)
{
    // 检查缓存
    if (m_iconCache.contains(type)) {
        return m_iconCache[type];
    }
    
    // 从资源加载图标
    if (m_iconPaths.contains(type)) {
        QIcon icon(m_iconPaths[type]);
        if (!icon.isNull()) {
            m_iconCache[type] = icon;
            return icon;
        } else {
            qWarning() << "Failed to load icon:" << m_iconPaths[type];
        }
    }
    
    // 返回空图标
    return QIcon();
}

QPixmap IconManager::getPixmap(IconType type, const QSize& size)
{
    QIcon icon = getIcon(type);
    if (!icon.isNull()) {
        return icon.pixmap(size);
    }
    return QPixmap();
}

QIcon IconManager::getIconFromResource(const QString& resourcePath)
{
    return QIcon(resourcePath);
}

QIcon IconManager::getIconFromFile(const QString& filePath)
{
    if (QFile::exists(filePath)) {
        return QIcon(filePath);
    } else {
        qWarning() << "Icon file not found:" << filePath;
        return QIcon();
    }
}

void IconManager::setDefaultIconSize(const QSize& size)
{
    m_defaultIconSize = size;
}

QSize IconManager::getDefaultIconSize() const
{
    return m_defaultIconSize;
}
