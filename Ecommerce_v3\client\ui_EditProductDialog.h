/********************************************************************************
** Form generated from reading UI file 'EditProductDialog.ui'
**
** Created by: Qt User Interface Compiler version 6.8.3
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_EDITPRODUCTDIALOG_H
#define UI_EDITPRODUCTDIALOG_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QDialog>
#include <QtWidgets/QDoubleSpinBox>
#include <QtWidgets/QFormLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QSpinBox>
#include <QtWidgets/QTextEdit>
#include <QtWidgets/QVBoxLayout>

QT_BEGIN_NAMESPACE

class Ui_EditProductDialog
{
public:
    QVBoxLayout *verticalLayout;
    QFormLayout *formLayout;
    QLabel *idLabel;
    QLabel *productIdLabel;
    QLabel *nameLabel;
    QLineEdit *nameLineEdit;
    QLabel *categoryLabelText;
    QLabel *categoryLabel;
    QLabel *priceLabel;
    QDoubleSpinBox *priceSpinBox;
    QLabel *stockLabel;
    QSpinBox *stockSpinBox;
    QLabel *discountLabel;
    QDoubleSpinBox *discountSpinBox;
    QLabel *extra1Label;
    QLabel *extra1ValueLabel;
    QLabel *extra2Label;
    QLabel *extra2ValueLabel;
    QLabel *remarkLabel;
    QTextEdit *remarkTextEdit;
    QSpacerItem *verticalSpacer;
    QHBoxLayout *buttonLayout;
    QSpacerItem *horizontalSpacer;
    QPushButton *updateButton;
    QPushButton *cancelButton;

    void setupUi(QDialog *EditProductDialog)
    {
        if (EditProductDialog->objectName().isEmpty())
            EditProductDialog->setObjectName("EditProductDialog");
        EditProductDialog->resize(400, 350);
        verticalLayout = new QVBoxLayout(EditProductDialog);
        verticalLayout->setSpacing(8);
        verticalLayout->setObjectName("verticalLayout");
        verticalLayout->setContentsMargins(12, 12, 12, 12);
        formLayout = new QFormLayout();
        formLayout->setObjectName("formLayout");
        formLayout->setFieldGrowthPolicy(QFormLayout::ExpandingFieldsGrow);
        idLabel = new QLabel(EditProductDialog);
        idLabel->setObjectName("idLabel");

        formLayout->setWidget(0, QFormLayout::LabelRole, idLabel);

        productIdLabel = new QLabel(EditProductDialog);
        productIdLabel->setObjectName("productIdLabel");

        formLayout->setWidget(0, QFormLayout::FieldRole, productIdLabel);

        nameLabel = new QLabel(EditProductDialog);
        nameLabel->setObjectName("nameLabel");

        formLayout->setWidget(1, QFormLayout::LabelRole, nameLabel);

        nameLineEdit = new QLineEdit(EditProductDialog);
        nameLineEdit->setObjectName("nameLineEdit");
        nameLineEdit->setReadOnly(true);

        formLayout->setWidget(1, QFormLayout::FieldRole, nameLineEdit);

        categoryLabelText = new QLabel(EditProductDialog);
        categoryLabelText->setObjectName("categoryLabelText");

        formLayout->setWidget(2, QFormLayout::LabelRole, categoryLabelText);

        categoryLabel = new QLabel(EditProductDialog);
        categoryLabel->setObjectName("categoryLabel");

        formLayout->setWidget(2, QFormLayout::FieldRole, categoryLabel);

        priceLabel = new QLabel(EditProductDialog);
        priceLabel->setObjectName("priceLabel");

        formLayout->setWidget(3, QFormLayout::LabelRole, priceLabel);

        priceSpinBox = new QDoubleSpinBox(EditProductDialog);
        priceSpinBox->setObjectName("priceSpinBox");
        priceSpinBox->setDecimals(2);

        formLayout->setWidget(3, QFormLayout::FieldRole, priceSpinBox);

        stockLabel = new QLabel(EditProductDialog);
        stockLabel->setObjectName("stockLabel");

        formLayout->setWidget(4, QFormLayout::LabelRole, stockLabel);

        stockSpinBox = new QSpinBox(EditProductDialog);
        stockSpinBox->setObjectName("stockSpinBox");

        formLayout->setWidget(4, QFormLayout::FieldRole, stockSpinBox);

        discountLabel = new QLabel(EditProductDialog);
        discountLabel->setObjectName("discountLabel");

        formLayout->setWidget(5, QFormLayout::LabelRole, discountLabel);

        discountSpinBox = new QDoubleSpinBox(EditProductDialog);
        discountSpinBox->setObjectName("discountSpinBox");
        discountSpinBox->setDecimals(1);

        formLayout->setWidget(5, QFormLayout::FieldRole, discountSpinBox);

        extra1Label = new QLabel(EditProductDialog);
        extra1Label->setObjectName("extra1Label");

        formLayout->setWidget(6, QFormLayout::LabelRole, extra1Label);

        extra1ValueLabel = new QLabel(EditProductDialog);
        extra1ValueLabel->setObjectName("extra1ValueLabel");

        formLayout->setWidget(6, QFormLayout::FieldRole, extra1ValueLabel);

        extra2Label = new QLabel(EditProductDialog);
        extra2Label->setObjectName("extra2Label");

        formLayout->setWidget(7, QFormLayout::LabelRole, extra2Label);

        extra2ValueLabel = new QLabel(EditProductDialog);
        extra2ValueLabel->setObjectName("extra2ValueLabel");

        formLayout->setWidget(7, QFormLayout::FieldRole, extra2ValueLabel);

        remarkLabel = new QLabel(EditProductDialog);
        remarkLabel->setObjectName("remarkLabel");

        formLayout->setWidget(8, QFormLayout::LabelRole, remarkLabel);

        remarkTextEdit = new QTextEdit(EditProductDialog);
        remarkTextEdit->setObjectName("remarkTextEdit");
        remarkTextEdit->setMaximumSize(QSize(16777215, 60));
        remarkTextEdit->setReadOnly(true);

        formLayout->setWidget(8, QFormLayout::FieldRole, remarkTextEdit);


        verticalLayout->addLayout(formLayout);

        verticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        verticalLayout->addItem(verticalSpacer);

        buttonLayout = new QHBoxLayout();
        buttonLayout->setObjectName("buttonLayout");
        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        buttonLayout->addItem(horizontalSpacer);

        updateButton = new QPushButton(EditProductDialog);
        updateButton->setObjectName("updateButton");
        updateButton->setMinimumSize(QSize(80, 30));

        buttonLayout->addWidget(updateButton);

        cancelButton = new QPushButton(EditProductDialog);
        cancelButton->setObjectName("cancelButton");
        cancelButton->setMinimumSize(QSize(80, 30));

        buttonLayout->addWidget(cancelButton);


        verticalLayout->addLayout(buttonLayout);


        retranslateUi(EditProductDialog);

        QMetaObject::connectSlotsByName(EditProductDialog);
    } // setupUi

    void retranslateUi(QDialog *EditProductDialog)
    {
        EditProductDialog->setWindowTitle(QCoreApplication::translate("EditProductDialog", "\344\277\256\346\224\271\345\225\206\345\223\201\344\277\241\346\201\257", nullptr));
        idLabel->setText(QCoreApplication::translate("EditProductDialog", "\345\225\206\345\223\201ID\357\274\232", nullptr));
        productIdLabel->setText(QCoreApplication::translate("EditProductDialog", "-", nullptr));
        nameLabel->setText(QCoreApplication::translate("EditProductDialog", "\345\225\206\345\223\201\345\220\215\347\247\260\357\274\232", nullptr));
        categoryLabelText->setText(QCoreApplication::translate("EditProductDialog", "\345\225\206\345\223\201\347\261\273\345\210\253\357\274\232", nullptr));
        categoryLabel->setText(QCoreApplication::translate("EditProductDialog", "-", nullptr));
        priceLabel->setText(QCoreApplication::translate("EditProductDialog", "\345\225\206\345\223\201\344\273\267\346\240\274\357\274\232", nullptr));
        priceSpinBox->setSuffix(QCoreApplication::translate("EditProductDialog", " \345\205\203", nullptr));
        stockLabel->setText(QCoreApplication::translate("EditProductDialog", "\345\272\223\345\255\230\346\225\260\351\207\217\357\274\232", nullptr));
        stockSpinBox->setSuffix(QCoreApplication::translate("EditProductDialog", " \344\273\266", nullptr));
        discountLabel->setText(QCoreApplication::translate("EditProductDialog", "\346\212\230\346\211\243\357\274\232", nullptr));
        extra1Label->setText(QCoreApplication::translate("EditProductDialog", "\351\242\235\345\244\226\344\277\241\346\201\2571\357\274\232", nullptr));
        extra1ValueLabel->setText(QCoreApplication::translate("EditProductDialog", "-", nullptr));
        extra2Label->setText(QCoreApplication::translate("EditProductDialog", "\351\242\235\345\244\226\344\277\241\346\201\2572\357\274\232", nullptr));
        extra2ValueLabel->setText(QCoreApplication::translate("EditProductDialog", "-", nullptr));
        remarkLabel->setText(QCoreApplication::translate("EditProductDialog", "\345\225\206\345\223\201\345\244\207\346\263\250\357\274\232", nullptr));
        updateButton->setText(QCoreApplication::translate("EditProductDialog", "\346\233\264\346\226\260", nullptr));
        cancelButton->setText(QCoreApplication::translate("EditProductDialog", "\345\217\226\346\266\210", nullptr));
    } // retranslateUi

};

namespace Ui {
    class EditProductDialog: public Ui_EditProductDialog {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_EDITPRODUCTDIALOG_H
