以下内容将基于“实验(综合-电商交易平台设计)”文档中题目三（网络版）的要求，逐步展开对**题目三实现思路**、**代码目录结构**以及**各模块/文件之间的关系**，并重点说明“客户端（Qt GUI）与服务器端如何通过 socket 进行交互”。

---

## 一、题目三需求回顾与分析

题目三是在题目一、二的基础上，将单机版系统改为 C/S 架构，主要要求如下：

1. **功能与单机版保持一致**：

   * 用户登录（客户端输入账号/密码，服务器进行验证）。
   * 展示平台商品信息（客户端请求，服务器从文件/内存读取后返回）。
   * 搜索平台商品信息（基于商品名称的筛选）。
   * 购物车管理（客户端添加/删除/修改购物车中商品数量，服务器侧需维护对应状态并冻结库存）。
   * 订单生产（生成订单、计算总价，暂不结算）。
   * 订单支付（客户端提交支付请求，服务器检查余额、库存，完成支付：扣减消费者余额并将相应金额转入商家账户，同时更新商品库存并在文件中持久化）。

2. **技术要求**：

   * 必须采用“传统 C/S 架构”，客户端与服务器用 socket 通信，严禁使用 RPC 框架。
   * 客户端的界面必须使用 **Qt** 实现图形化（即 Qt Widgets 或 Qt Quick）。
   * 需考虑“错误场景处理”：例如登录失败、库存不足、文件读写错误、网络异常等，都需要在客户端和服务器之间进行报错提示和捕获。

3. **持久化**：

   * 服务器端数据（用户信息、商品信息、订单信息）要写入文件（或可选地使用轻量级数据库，但本实验说明中预期是**文件**读写）。
   * 客户端本地不做持久化，只保留会话状态（如当前登录用户、当前购物车内容等），所有最终数据都由服务器端负责。

4. **模块化与面向对象设计**：

   * 要在代码中保持“单机版”时的面向对象设计思路，包括用户、商品、订单、购物车等类及其继承体系。
   * 网络版的关键在于“在原有基础上拆分出服务端逻辑与客户端逻辑”，并在需要的地方通过 socket 把“方法调用”变成“消息传递”。

---

## 二、实现思路概述

下面从 **整体架构**、**功能分层**、**通信协议**、**流程示例** 四个角度说明实现思路。

### 1. 整体架构

```
                    +---------------------+
                    |       客户端        |
                    |   (Qt GUI + 网络)   |
                    +----------+----------+
                               | Socket（TCP）
                 请求消息/响应  |     交互
                               v
                    +----------+----------+
                    |       服务器端      |
                    | (网络监听 + 业务逻辑)|
                    +----------+----------+
                               |
                      文件／持久化（读写）
                               |
          +--------------------------------------------+
          |   用户数据文件  商品数据文件  订单数据文件   |
          +--------------------------------------------+
```

* **客户端**：

  * Qt GUI 层（窗口、按钮、输入框等）
  * 网络访问层（使用 `QTcpSocket` 进行连接、发送“请求消息”、接收“响应消息”）
  * 本地状态管理层（当前登录用户信息、购物车临时存储）

* **服务器端**：

  * 网络监听层（使用标准 BSD socket 或者 `QTcpServer` 进行多客户端监听/accept）
  * 消息解析与分发层（把从 socket 收到的“字节流”解析成“请求类型 + 参数”，并分发给相应的 Manager）
  * 业务逻辑层（各子系统 Manager：UserManager、ProductManager、CartManager、OrderManager）
  * 持久化层（基于文件的读/写接口，各 Manager 操作时通过统一接口读写文件）

### 2. 功能分层与类设计

#### （1）共用数据模型（`common` 模块）

* **数据结构类（Data Models）**

  * `User` 抽象基类 + `Consumer`（消费者）、`Merchant`（商家）子类
  * `Product` 抽象基类 + `Food`、`Clothing`、`Book` 等子类（与单机版一致）
  * `CartItem`、`OrderItem`、`Order` 等

* **网络协议定义**

  * 每种请求消息在“字节流”中的格式（可以用定长字段 + JSON 串，或更简单的“每条消息以特定分隔符结尾”+“JSON 格式”）

    * 建议：每个请求/响应都使用 JSON 对象串（UTF-8 编码），并在末尾追加“\n\n”作为分隔。
    * 例如：

      ```json
      {
        "action": "login",
        "username": "zhangsan",
        "password": "123456"
      }
      ```

      然后服务器解析后返回：

      ```json
      {
        "status": "ok",
        "userType": "consumer",
        "balance": 100.50
      }
      ```

* **协议枚举／常量**

  * `enum class Action { Login, FetchProductList, SearchProduct, AddToCart, RemoveFromCart, CreateOrder, PayOrder, ... };`
  * `enum class ResponseStatus { OK, Fail };`

#### （2）服务器端各子系统（`server` 模块）

* **Network/**

  * `Server.h/.cpp`

    * 启动 TCP 监听（选用底层 BSD socket 或 Qt 提供的 `QTcpServer`）。
    * 循环接受客户端连接，对于每个新连接 `socketFd`，创建一个 `ClientHandler`（若使用多线程，可为每个客户端新建线程；若使用单线程 Reactor 模式，使用非阻塞+`select/epoll` 或 Qt 信号槽）。

  * `ClientHandler.h/.cpp`

    * 负责和单个客户端 socket 的收发。
    * **接收**：不断读 socket，按“\n\n”拆包，得到完整 JSON 请求；
    * 解析后调用“MessageDispatcher::dispatch(request, thisClientFd)”；
    * **发送**：将“JSON 响应串 + \n\n”写回 socket。

* **Managers/**

  * `UserManager.h/.cpp`

    * 提供：`bool login(username, password, User& outUser)`、`bool register(...)`（题目三不一定需要注册接口，但可保留）
    * 用户信息存储在文件 `users.dat`（或 `.txt`、`.csv`）。每次查询/更新都要读/写文件（可以一次加载到内存、运行期间修改后再写回，也可每次都读写，看规模）。

  * `ProductManager.h/.cpp`

    * 提供：`std::vector<Product> getAllProducts()`、`std::vector<Product> searchByName(const std::string& key)`、`bool freezeStock(productId, qty)`、`bool deductStock(productId, qty)` 等。
    * 商品信息存储在文件 `products.dat`。
    * “冻结”可以通过维护一个“冻结数量”字段，或简单地在客户端“下单生成订单”环节就先扣减库存放入临时订单（题目三要求与题目二一致：当生成订单后、未支付前，需要“冻结”库存；支付成功后才真正“扣减”），因此可在内存里维护一个 map\<productId, frozenQty>，并在服务器关闭或定时将状态持久化。

  * `CartManager.h/.cpp`

    * 负责**会话级别**的“购物车”维护：对于每个已经登录的消费者，维护一个 `std::map<productId, qty>`。
    * 注意：当客户端断开，购物车信息可选择清空或者持久化（题目里没明说在网络版购物车是否要持久化，本方案可先不持久化，登录失败或断线就清空）。

  * `OrderManager.h/.cpp`

    * 提供：`Order createOrder(userId, cartItems)`、`bool payOrder(orderId, userId)` 等。
    * `createOrder` 调用 `ProductManager::freezeStock(...)` 并生成 `Order` 对象（状态 `PendingPayment`，写入 `orders.dat`）。
    * `payOrder` 需检查用户余额、库存是否足够（库存一般已冻结，若冻结数量大于库存，支付失败；否则扣减实际库存、扣减用户/商家余额、更新订单状态为 `Paid` 并写回文件）。

  * `MessageDispatcher.h/.cpp`

    * 负责将客户端发来的“action”解析后，调用对应的 Manager 方法，并将结果组装成 JSON 响应发回。例如：

      1. 收到 `{"action":"login", "username":"u", "password":"p"}`
      2. 调用 `UserManager::login("u", "p", outUser)`
      3. 根据返回状态组建响应 JSON，例如 `{"status":"ok","userType":"consumer","balance":100.5}` 或 `{"status":"fail","reason":"password wrong"}`
      4. 调用 `ClientHandler::send(responseJson)`

* **FileIO/**

  * 可以在每个 Manager 内部直接做文件读写，也可以抽象出一层“持久化工具类”（`FileUtil.h/.cpp`），提供序列化/反序列化：

    * `bool readUsers(std::vector<User>& out)`、`bool writeUsers(const std::vector<User>& in)`
    * `bool readProducts(std::vector<Product>& out)`、`bool writeProducts(const std::vector<Product>& in)`
    * `bool readOrders(std::vector<Order>& out)`、`bool writeOrders(const std::vector<Order>& in)`
  * 文件格式可选：

    * 纯文本 CSV／TSV
    * 二进制序列化（如果自行定义结构，写入固定字节流）
    * JSON 列表（依赖第三方 JSON 库，如 [nlohmann/json](https://github.com/nlohmann/json)，使用简单但可增大可执行体积）

#### （3）客户端各子系统（`client` 模块）

* **UI/**

  * Qt Widgets（或 Qt Quick）设计几大窗口（均继承自 `QWidget` 或 `QMainWindow`）：

    * `LoginWindow.h/.cpp`：用户输入用户名/密码，点击“登录”后调用网络层发送 login 请求；

      * 若登录成功，关闭自身并打开 `ProductWindow`／`MainWindow`；否则弹出错误提示。
    * `ProductWindow.h/.cpp`：展示商品列表（可使用 `QTableView`、`QListWidget` 或自定义 `QListView`），并提供搜索框；

      * 底部/侧边提供“购物车”入口，将当前用户的购物车状态传给 `CartWindow`；
      * 也可在每个商品条目上加“添加到购物车”按钮，点击后弹出输入数量对话框。
    * `CartWindow.h/.cpp`：展示购物车中商品（客户端本地数据），可修改数量或删除，点击“结算”生成订单（但不真正支付），调用网络层 `createOrder`；

      * 生成订单后弹出“订单确认窗口”（或直接在本窗口显示订单信息）。
    * `OrderWindow.h/.cpp`：展示用户所有订单列表（读取自服务器），以及每个订单的状态（Pending、Paid 等）；在“Pending”订单上提供“支付”按钮。点击“支付”后调用网络层 `payOrder`，并根据结果更新显示。

* **Network/**

  * `Client.h/.cpp`

    * 内部维护一个 `QTcpSocket* socket`，外部通过 `Client::connectToServer(host, port)` 建立连接；
    * 提供一系列“请求方法”：

      ```cpp
      bool login(const QString& username, const QString& password, QString& outError);
      bool fetchProductList(QJsonArray& outProducts, QString& outError);
      bool searchProduct(const QString& key, QJsonArray& outProducts, QString& outError);
      bool addToCart(int productId, int qty, QString& outError);
      bool removeFromCart(int productId, QString& outError);
      bool createOrder(QJsonObject& outOrderInfo, QString& outError);
      bool payOrder(int orderId, QString& outError);
      // …等等
      ```
    * 每个方法内部组装 JSON（例如 QJsonObject），序列化为 QByteArray，再 `socket->write(requestBytes + "\n\n")`，然后进入阻塞等待（或信号槽异步）`socket->readyRead()`，解析服务器端的响应。

* **Local Managers（客户端缓存层）**

  * `ClientUserManager.h/.cpp`

    * 登录后保存当前用户的 `userId`、`userType`、`balance`。
    * 提供取余额、更新余额（仅在客户端 UI 上显示），真正的余额更新由 `payOrder` 接口触发后再拉取最新余额。
  * `ClientCartManager.h/.cpp`

    * 维护本地 `std::map<productId, qty>`。客户端对“添加”或“删除”购物车时，先更新本地，再调用 `Client::addToCart` 或 `Client::removeFromCart`；如果接口返回失败，再回滚本地状态并弹窗告知。

* **DataModels（与服务器端共用）**

  * 可以把 header（数据模型类定义）放到一个 `common` 或 `shared` 目录里，客户端与服务器都引用相同结构（例如 `Product` 类、`Order` 类、`User` 类等），保证 JSON 序列化/反序列化时保持一致。
  * 注意：客户端一般不需要完整的服务器端逻辑，仅需要一些字段（如 productId、name、price、stock、stockFrozen 等）。

### 3. 通信协议与流程示例

下面以“登录”、“获取商品列表”、“购物车→生成订单→支付”三个场景分别举例，说明客户端与服务器端的交互流程。

#### （1）登录流程

1. **客户端（LoginWindow）**

   ```cpp
   // 用户点击“登录”按钮
   QString username = ui->usernameEdit->text();
   QString password = ui->passwordEdit->text();
   QString error;
   if (!client.login(username, password, error)) {
       QMessageBox::critical(this, "登录失败", error);
       return;
   }
   // 登录成功，ClientUserManager 内部已保存 userId、userType、balance
   this->close();
   auto mainWin = new ProductWindow(client);
   mainWin->show();
   ```

2. **Network: Client::login()**

   * 将参数封装为 QJsonObject：

     ```json
     {
       "action": "login",
       "username": "zhangsan",
       "password": "123456"
     }
     ```
   * `socket->write(jsonStr.toUtf8() + "\n\n");`
   * 等待 `readyRead()` 信号，读取完整响应，假设为：

     ```json
     {
       "status": "ok",
       "userId": 1001,
       "userType": "consumer",
       "balance": 200.75
     }
     ```
   * 解析 `status`，若 `"ok"`，则将 `userId`、`userType`、`balance` 保存到 `ClientUserManager`，返回 `true`。若 `"fail"`，则返回 `false` 并填充 error。

3. **服务器端**

   * `ClientHandler` 收到字节流，拆包得到 JSON，调用 `MessageDispatcher::dispatch(...)`。
   * `MessageDispatcher` 识别 `"action"=="login"`，调用 `UserManager::login(username, password, outUser)`：

     * 读取文件 `users.dat`，查找用户名，验证密码。
     * 若验证成功，把 `outUser` 填充（包含 `userId`、`userType`、`balance` 等）。
   * `MessageDispatcher` 根据返回状态，组装响应 JSON：

     * 成功：

       ```json
       {
         "status": "ok",
         "userId": 1001,
         "userType": "consumer",
         "balance": 200.75
       }
       ```
     * 失败：

       ```json
       {
         "status": "fail",
         "reason": "用户名或密码错误"
       }
       ```
   * 调用 `ClientHandler::send(responseJson)`，并在 JSON 尾部追加 `\n\n`。

#### （2）获取商品列表流程

1. **客户端（ProductWindow 构造时）**

   ```cpp
   // 构造 ProductWindow 时
   QJsonArray products;
   QString error;
   if (!client.fetchProductList(products, error)) {
       QMessageBox::warning(this, "获取商品失败", error);
       return;
   }
   // 将 products 转换为 TableModel/UI 展示
   this->populateTable(products);
   ```

2. **Network: Client::fetchProductList()**

   * 组装请求：

     ```json
     { "action": "fetchProductList" }
     ```
   * 写入 socket，等待响应（类似登录流程）。
   * 假设服务器返回：

     ```json
     {
       "status": "ok",
       "products": [
         { "productId":1, "name":"C++ Primer", "price":88.0, "stock":10 },
         { "productId":2, "name":"Efficient C++", "price":75.5, "stock":5 },
         …
       ]
     }
     ```
   * 解析后将 `products` 数组传回给 UI。

3. **服务器端**

   * `MessageDispatcher` 识别 `"action"=="fetchProductList"`，调用 `ProductManager::getAllProducts()`：

     * 读取本地文件 `products.dat`（或将文件内容预先加载到内存），返回 `std::vector<Product>`。
   * 将每个 `Product` 序列化为 JSON 对象数组，组装为：

     ```json
     {
       "status":"ok",
       "products":[ {…}, {…}, … ]
     }
     ```
   * 通过 `ClientHandler` 发回客户端。

#### （3）购物车→生成订单→支付流程

1. **客户端**

   * **添加到购物车**

     ```cpp
     void ProductWindow::onAddToCartClicked(int productId) {
         bool ok;
         int qty = QInputDialog::getInt(this, "数量", "请输入购买数量：", 1, 1, 100, 1, &ok);
         if (!ok) return;
         QString error;
         if (!client.addToCart(productId, qty, error)) {
             QMessageBox::warning(this, "加入购物车失败", error);
             return;
         }
         clientCartManager.addItem(productId, qty); // 本地维护
         QMessageBox::information(this, "成功", "已加入购物车");
     }
     ```

   * **查看购物车并生成订单**

     ```cpp
     void ProductWindow::onViewCartClicked() {
         CartWindow* cartWin = new CartWindow(client, clientCartManager);
         cartWin->show();
     }
     ```

     在 `CartWindow` 里：

     ```cpp
     void CartWindow::onCreateOrderClicked() {
         // 本地先把 cartItems 从 clientCartManager 拿来
         QJsonObject orderInfo;
         QString error;
         if (!client.createOrder(cartItems, orderInfo, error)) {
             QMessageBox::warning(this, "生成订单失败", error);
             return;
         }
         // orderInfo 包含：orderId、总金额等
         // 清空本地购物车
         clientCartManager.clear();
         // 弹出订单详情、并跳转到支付
         OrderWindow* orderWin = new OrderWindow(client, orderInfo);
         orderWin->show();
         this->close();
     }
     ```

   * **支付订单**
     在 `OrderWindow` 中，若订单状态为 `PendingPayment`，

     ```cpp
     void OrderWindow::onPayClicked() {
         int orderId = ui->orderIdLabel->text().toInt();
         QString error;
         if (!client.payOrder(orderId, error)) {
             QMessageBox::warning(this, "支付失败", error);
             return;
         }
         QMessageBox::information(this, "支付成功", "订单已支付");
         // 重新拉取用户余额、订单列表等并刷新 UI
     }
     ```

2. **Network: Client::createOrder / payOrder**

   * `createOrder`

     ```json
     {
       "action": "createOrder",
       "userId": 1001,
       "cart": [
         { "productId": 1, "quantity": 2 },
         { "productId": 3, "quantity": 1 }
       ]
     }
     ```

     服务器返回：

     ```json
     {
       "status": "ok",
       "orderId": 5001,
       "totalAmount": 251.5,
       "orderStatus": "PendingPayment"
     }
     ```

     或者

     ```json
     {
       "status": "fail",
       "reason": "库存不足，商品ID=3 剩余量不足"
     }
     ```

   * `payOrder`

     ```json
     {
       "action": "payOrder",
       "userId": 1001,
       "orderId": 5001
     }
     ```

     服务器返回：

     ```json
     {
       "status": "ok",
       "newBalance": 80.25
     }
     ```

     或

     ```json
     {
       "status": "fail",
       "reason": "余额不足"
     }
     ```

3. **服务器端**

   * **createOrder → OrderManager**

     1. 检查前端发送的 `cart` 列表中每个商品的库存是否足够：

        * 对每个项目调用 `ProductManager::freezeStock(productId, quantity)`
        * 如果任何一个 `freezeStock` 失败，循环回滚已经冻结的库存并返回“失败”。
     2. 若都成功，则构造 `Order` 对象（状态 `PendingPayment`，生成唯一 `orderId`，记录 `userId`、商品列表、单价、总价）。
     3. 把该 `Order` 写入 `orders.dat`；同时更新 `products.dat` 中对应商品的“已冻结库存”值。
     4. 返回成功并把 `orderId`、`totalAmount` 返回给客户端。

   * **payOrder → OrderManager**

     1. 从 `orders.dat` 中读取对应 `orderId`，检查状态是否为 `PendingPayment`；
     2. 检查用户余额（`UserManager::getBalance(userId)`）是否 ≥ 订单总价；
     3. 检查库存冻结状态是否满足（在正常设计中，冻结状态已经保证库存足够，所以这一环节可直接放行；但为了更严谨，可再检查库存）；
     4. 扣减消费者余额：`UserManager::updateBalance(userId, newBalance)`；
     5. 累加商家余额：`UserManager::updateBalance(merchantId, merchantBalance + orderTotal)`；
     6. 真正从库存中扣减 `ProductManager::deductStock(productId, orderedQty)`，并减少冻结数量；
     7. 修改订单状态为 `Paid`，写回 `orders.dat`；
     8. 返回成功响应：`{"status":"ok", "newBalance":xxx}`

   * 失败情况要一并回滚（例如扣减失败时要还原冻结库存、还原消费者余额／商家余额、还原订单状态，确保数据一致性）。

---

## 三、代码目录结构（按功能划分，.h/.cpp 放在一起）

下面给出一个较为完整的目录示例，整体采用 **“功能模块”** 的划分方式，每个模块下 `.h/.cpp` 放在一起，客户端和服务器端分开管理，共用的部分放在 `common`。

```
ecommerce-platform/                   ← 项目根目录
├── common/                           ← 客户端与服务器共用的头文件（数据模型、协议定义等）
│   ├── Protocol.h                    ← 请求/响应的 action 字符串常量、枚举
│   ├── DataModels.h                  ← User、Product、CartItem、OrderItem、Order 等类的声明
│   ├── DataModels.cpp                ← DataModels 类的序列化/反序列化（如 toJson()/fromJson()）
│   └── Utils.h                       ← 公共工具（如 JSON 转换工具、时间戳获取等）
│
├── server/                           ← 服务器端代码
│   ├── main.cpp                      ← 启动入口：创建 Server 对象并 `server.start()` 
│   │
│   ├── Network/                      ← 网络层
│   │   ├── Server.h                  ← 类 Server：监听端口、accept 客户端、管理 ClientHandler 列表
│   │   ├── Server.cpp                ← Server 方法实现
│   │   ├── ClientHandler.h           ← 类 ClientHandler：负责与单个客户端 socket 的读写
│   │   └── ClientHandler.cpp         ← 解析 JSON 请求并调用 MessageDispatcher
│   │
│   ├── Dispatcher/                   ← 消息分发层
│   │   ├── MessageDispatcher.h       ← 类 MessageDispatcher：根据 action 调用各 Manager
│   │   └── MessageDispatcher.cpp     ← 实现 dispatch 方法（if/else 或 switch 分发）
│   │
│   ├── Managers/                     ← 业务逻辑层
│   │   ├── UserManager.h             ← 登录/注册/余额查询/余额更新等接口
│   │   ├── UserManager.cpp
│   │   ├── ProductManager.h          ← 获取商品列表、搜索、冻结/扣减库存等接口
│   │   ├── ProductManager.cpp
│   │   ├── CartManager.h             ← 会话级别购物车管理（可选，若将购物车存至服务器内存）
│   │   ├── CartManager.cpp
│   │   ├── OrderManager.h            ← 生成订单、支付订单等接口
│   │   └── OrderManager.cpp
│   │
│   └── Storage/                      ← 持久化层（文件读写操作封装）
│       ├── FileUtil.h                ← 读写用户、商品、订单文件的工具函数声明
│       └── FileUtil.cpp              ← FileUtil 各方法实现
│
└── client/                           ← 客户端代码
    ├── main.cpp                      ← Qt 应用入口：创建 QApplication、LoginWindow 等
    │
    ├── UI/                           ← Qt 界面层（Qt Designer 生成或手写）
    │   ├── LoginWindow.h
    │   ├── LoginWindow.cpp
    │   ├── ProductWindow.h
    │   ├── ProductWindow.cpp
    │   ├── CartWindow.h
    │   ├── CartWindow.cpp
    │   ├── OrderWindow.h
    │   └── OrderWindow.cpp
    │
    ├── Network/                      ← 客户端网络层
    │   ├── Client.h                  ← 使用 QTcpSocket，与服务器交互的所有请求方法
    │   └── Client.cpp
    │
    ├── Managers/                     ← 客户端本地状态管理
    │   ├── ClientUserManager.h       ← 保存当前登录用户信息（userId、balance、userType）
    │   ├── ClientUserManager.cpp
    │   ├── ClientCartManager.h       ← 本地购物车状态维护 map<productId, qty>
    │   └── ClientCartManager.cpp
    │
    └── Resources/                    ← Qt 资源文件（.qrc）、图标、UI 样式等
        ├── images/
        │   └── logo.png
        └── styles.qss
```

> **说明**：
>
> 1. **common/**
>
>    * 放置所有客户端和服务器端都需要的“数据模型”和“协议常量”。
>    * 这样既保证双方使用一致的 Json 字段，又能减少代码重复。
> 2. **.h/.cpp 放在同一目录中**
>
>    * 每个类（如 `UserManager.h` 和 `UserManager.cpp`）放在功能对应的文件夹下（Managers、Network、UI），不分离 include 与 src。
>    * 方便查找每个类的声明与实现。
> 3. **客户端与服务器端分离**
>
>    * 互不干扰，各自独立编译。
>    * 共享 common/ 目录下的头文件（因此需要在各自的项目配置中，把 `../common` 添加到 include 路径）。
> 4. **持久化层与业务层分离**
>
>    * 在服务器端，所有对文件的直接操作应封装在 `Storage/FileUtil.*`，Manager 只需调用接口，不要关心文件格式细节。
> 5. **Qt 项目结构**
>
>    * `client/` 下可包含一个 `.pro` 或 CMakeLists.txt，用于 Qt 编译。
>    * `UI/` 下的 `*.ui`（若使用 QtDesigner）、`.h`、`.cpp` 三者相互配合。

---

## 四、各文件/模块之间的关系与调用顺序

下面以“服务器端登录→获取商品→生成订单→支付”为例，对关键文件/函数间的调用关系做说明。客户端相应地只是“调用 `Client.xxx()` → 网络层发送请求 → 服务器端处理 → 网络层返回 → 客户端 UI 刷新”。

### 1. 客户端侧调用链（以登录为例）

```
LoginWindow::onLoginButtonClicked()
    ↓
ClientUserManager  //（若需要先初始化 Manager）
Client::login(username, password, &error)
    ↓
QTcpSocket::write( loginJson + "\n\n" )
    ↓
（Qt 信号槽：socket readyRead）
Client::onReadyRead()
    ↓
Client::parseResponseJson()
    ↓
if (status == "ok") ClientUserManager::setCurrentUser(userId, userType, balance)
    ↓
返回给 LoginWindow
LoginWindow 判断并跳转到 ProductWindow
```

### 2. 服务器端侧调用链（以登录为例）

```
main.cpp
    Server server(port);
    server.start();  // 在内部 spawn 线程或进入事件循环，使用 accept() 监听新的客户端连接

Server::onNewConnection(socketFd)
    new ClientHandler(socketFd)

ClientHandler::run()  // 每个客户端一个实例
    while (socket 没关闭) {
        read buffer → 拆包得到 JSON 字符串 → parseJson → QJsonObject req
        MessageDispatcher::dispatch(req, this);
    }

MessageDispatcher::dispatch(req, this)
    if req["action"] == "login":
        User user;
        bool ok = UserManager::login(req["username"], req["password"], user);
        if (ok) {
            构造 resp = { "status":"ok", "userId":user.id, "userType":user.type, "balance":user.balance };
        } else {
            构造 resp = { "status":"fail", "reason":"用户名/密码错误" };
        }
        this->send(resp);  // 调用 ClientHandler::send()
        
ClientHandler::send(QJsonObject resp)
    QString respStr = QJsonDocument(resp).toJson(QJsonDocument::Compact) + "\n\n";
    write(socketFd, respStr.toUtf8().data(), respStr.size());
```

### 3. 服务器端其他场景

* **获取商品列表**

  1. `MessageDispatcher` 收到 `{"action":"fetchProductList"}`
  2. 调用 `ProductManager::getAllProducts(vector<Product>& out)`
  3. `ProductManager` 内部通过 `FileUtil::readProducts(...)` 读文件并填充 `vector<Product>`。
  4. 将 `vector<Product>` 转为 `QJsonArray`（或 `std::vector<QJsonObject>`），组装成 `{"status":"ok","products":[…]}` 给 `ClientHandler.send()`。

* **createOrder**

  1. `MessageDispatcher` 收到 `{"action":"createOrder", "userId":…, "cart": […]}`
  2. 解析 `cart` 数组为 `std::vector<CartItem>`，调用 `OrderManager::createOrder(userId, cartItems, outOrder, outReason)`
  3. `OrderManager`：

     * 遍历 `cartItems`，对每个 `item` 调用 `ProductManager::freezeStock(item.productId, item.quantity)`。若任意冻结失败，则回滚并返回失败。
     * 计算订单总价、生成唯一 `orderId`，构造 `Order` 对象，写入 `orders.dat`（调用 `FileUtil::writeOrders(...)` 或者单条 append）。
  4. 返回 `"orderId"`、`"totalAmount"` 给客户端。

* **payOrder**

  1. `MessageDispatcher` 收到 `{"action":"payOrder", "userId":…, "orderId":…}`
  2. `OrderManager::payOrder(userId, orderId, outNewBalance, outReason)`
  3. `OrderManager`：

     * 从 `FileUtil::readOrders(...)` 或内存缓存中读取该订单，确认状态是 `PendingPayment`。
     * 检查 `UserManager::getBalance(userId)` ≥ `order.totalAmount`。
     * 为订单中每个商品调用 `ProductManager::deductStock(productId, qty)` & 同时减少冻结数量。
     * 调用 `UserManager::updateBalance(userId, newBalance)`（扣消费者余额）、`UserManager::updateBalance(merchantId, merchantBalance + orderTotal)`。
     * 修改订单状态并写回 `orders.dat`。
  4. 返回 `"newBalance"` 给客户端；若失败，返回 `{"status":"fail","reason":…}`。

---

## 五、示例代码片段（示意，非完整）

下面列举几个关键文件的伪代码示例，帮助更清晰地理解各部分之间的耦合与调用逻辑。

> **注意**：以下仅为骨架代码示例，实际实现需补充错误检查、异常处理、注释，以及文件格式的具体实现。

---

### 1. common/DataModels.h

```cpp
#pragma once
#include <QString>
#include <QJsonObject>
#include <QJsonArray>
#include <vector>

// 用户类型枚举
enum class UserType {
    Consumer,
    Merchant
};

// 抽象基类 User
class User {
public:
    int userId;
    QString username;
    QString password;
    double balance;
    virtual UserType getUserType() const = 0;
    virtual void toJson(QJsonObject& obj) const;
    virtual void fromJson(const QJsonObject& obj);
    virtual ~User() {}
};

// Consumer（消费者）
class Consumer : public User {
public:
    UserType getUserType() const override { return UserType::Consumer; }
    void toJson(QJsonObject& obj) const override {
        obj["userId"] = userId;
        obj["username"] = username;
        obj["password"] = password;
        obj["balance"] = balance;
        obj["userType"] = "consumer";
    }
    void fromJson(const QJsonObject& obj) override {
        userId = obj["userId"].toInt();
        username = obj["username"].toString();
        password = obj["password"].toString();
        balance = obj["balance"].toDouble();
    }
};

// Merchant（商家）
class Merchant : public User {
public:
    UserType getUserType() const override { return UserType::Merchant; }
    void toJson(QJsonObject& obj) const override {
        obj["userId"] = userId;
        obj["username"] = username;
        obj["password"] = password;
        obj["balance"] = balance;
        obj["userType"] = "merchant";
    }
    void fromJson(const QJsonObject& obj) override {
        userId = obj["userId"].toInt();
        username = obj["username"].toString();
        password = obj["password"].toString();
        balance = obj["balance"].toDouble();
    }
};

// 商品基类
class Product {
public:
    int productId;
    QString name;
    QString description;
    double price;
    int stock;         // 实际库存
    int frozenStock;   // 已冻结的库存
    virtual QString getCategory() const = 0;
    virtual void toJson(QJsonObject& obj) const;
    virtual void fromJson(const QJsonObject& obj);
    virtual ~Product() {}
};

// Book（图书）示例
class Book : public Product {
public:
    QString author;
    QString publisher;
    QString getCategory() const override { return "book"; }
    void toJson(QJsonObject& obj) const override {
        Product::toJson(obj);
        obj["category"] = "book";
        obj["author"] = author;
        obj["publisher"] = publisher;
    }
    void fromJson(const QJsonObject& obj) override {
        Product::fromJson(obj);
        author = obj["author"].toString();
        publisher = obj["publisher"].toString();
    }
};

// 购物车条目
struct CartItem {
    int productId;
    int quantity;
    void toJson(QJsonObject& obj) const {
        obj["productId"] = productId;
        obj["quantity"] = quantity;
    }
    void fromJson(const QJsonObject& obj) {
        productId = obj["productId"].toInt();
        quantity = obj["quantity"].toInt();
    }
};

// 订单条目
struct OrderItem {
    int productId;
    int quantity;
    double unitPrice;
    void toJson(QJsonObject& obj) const {
        obj["productId"] = productId;
        obj["quantity"] = quantity;
        obj["unitPrice"] = unitPrice;
    }
    void fromJson(const QJsonObject& obj) {
        productId = obj["productId"].toInt();
        quantity = obj["quantity"].toInt();
        unitPrice = obj["unitPrice"].toDouble();
    }
};

// 订单
class Order {
public:
    int orderId;
    int userId;                 // 下单消费者
    int merchantId;             // 商家（简化：假设所有商品来自同一商家，或可扩展多商家）
    std::vector<OrderItem> items;
    double totalAmount;
    QString status;             // "PendingPayment" / "Paid"
    void toJson(QJsonObject& obj) const {
        obj["orderId"] = orderId;
        obj["userId"] = userId;
        obj["merchantId"] = merchantId;
        obj["totalAmount"] = totalAmount;
        obj["status"] = status;
        QJsonArray arr;
        for (auto& it : items) {
            QJsonObject o;
            it.toJson(o);
            arr.append(o);
        }
        obj["items"] = arr;
    }
    void fromJson(const QJsonObject& obj) {
        orderId = obj["orderId"].toInt();
        userId = obj["userId"].toInt();
        merchantId = obj["merchantId"].toInt();
        totalAmount = obj["totalAmount"].toDouble();
        status = obj["status"].toString();
        items.clear();
        auto arr = obj["items"].toArray();
        for (auto v : arr) {
            OrderItem it;
            it.fromJson(v.toObject());
            items.push_back(it);
        }
    }
};
```

---

### 2. server/Managers/UserManager.h

```cpp
#pragma once
#include <QString>
#include <vector>
#include "common/DataModels.h"

class UserManager {
public:
    // 加载所有用户到内存（可选）
    static bool loadAllUsers(std::vector<User*>& outUsers);

    // 登录：验证 username/password。若成功，用 outUser 返回具体子类指针 (Consumer* 或 Merchant*)。
    static bool login(const QString& username, const QString& password, User*& outUser, QString& outError);

    // 更新用户余额（传入 userId，新余额）。写回持久化存储。
    static bool updateBalance(int userId, double newBalance, QString& outError);

    // 获取用户余额
    static bool getBalance(int userId, double& outBalance, QString& outError);

    // …可选：注册新用户、修改密码等
};
```

对应的 `UserManager.cpp` 会调用 `FileUtil::readUsers` / `FileUtil::writeUsers`，在登录时遍历 `vector<User*>` 找到匹配，验证密码。

---

### 3. server/Managers/ProductManager.h

```cpp
#pragma once
#include <QString>
#include <vector>
#include "common/DataModels.h"

class ProductManager {
public:
    // 返回所有未冻结/全部商品（由客户端自行判断冻结量与库存剩余）
    static bool getAllProducts(std::vector<Product*>& outProducts, QString& outError);

    // 根据名称关键字搜索
    static bool searchByName(const QString& key, std::vector<Product*>& outProducts, QString& outError);

    // 冻结库存(商品ID、数量)，返回 false 表示库存不足
    static bool freezeStock(int productId, int qty, QString& outError);

    // 扣减库存（支付时调用）
    static bool deductStock(int productId, int qty, QString& outError);

    // …可选：增加/更新商品信息
};
```

`ProductManager.cpp` 中的方法都先调用 `FileUtil::readProducts` 取出 `std::vector<Product*>`，操作后调用 `FileUtil::writeProducts` 将整个列表写回。冻结库存时，只修改对应 `Product` 对象的 `frozenStock` 字段；扣减库存时同时减少 `stock` 和 `frozenStock`。

---

### 4. server/Managers/OrderManager.h

```cpp
#pragma once
#include <QString>
#include <vector>
#include "common/DataModels.h"

class OrderManager {
public:
    // 根据用户ID和购物车生成订单
    // 成功时返回 true，把 order 填充（包含 orderId、totalAmount 等）
    static bool createOrder(int userId, const std::vector<CartItem>& cartItems, Order& order, QString& outError);

    // 支付订单
    // 成功时返回 true，并把 newBalance(用户) 赋给 outNewBalance
    static bool payOrder(int userId, int orderId, double& outNewBalance, QString& outError);

    // 获取指定用户的所有订单
    static bool getOrdersByUser(int userId, std::vector<Order>& outOrders, QString& outError);
};
```

对应 `OrderManager.cpp` 会调用 `FileUtil::readOrders` 读取所有订单，再根据 `orderId` 或 `userId` 做筛选；`createOrder` 内部调用 `ProductManager::freezeStock`、然后写入新订单并持久化；`payOrder` 内部处理前文所述的扣款和库存扣减。

---

### 5. server/Network/MessageDispatcher.h

```cpp
#pragma once
#include <QString>
#include <QJsonObject>
#include "Network/ClientHandler.h"

class MessageDispatcher {
public:
    // 分发入口：收到客户端 JSON 请求、携带当前 ClientHandler 实例指针，便于回包
    static void dispatch(const QJsonObject& request, ClientHandler* client);
};
```

`MessageDispatcher.cpp` 中 `dispatch(...)` 可能结构类似：

```cpp
#include "MessageDispatcher.h"
#include "Managers/UserManager.h"
#include "Managers/ProductManager.h"
#include "Managers/OrderManager.h"
#include <QJsonArray>

void MessageDispatcher::dispatch(const QJsonObject& req, ClientHandler* client) {
    QString action = req["action"].toString();
    if (action == "login") {
        QString username = req["username"].toString();
        QString password = req["password"].toString();
        User* user = nullptr;
        QString err;
        bool ok = UserManager::login(username, password, user, err);
        QJsonObject resp;
        if (ok) {
            resp["status"] = "ok";
            resp["userId"] = user->userId;
            resp["userType"] = (user->getUserType() == UserType::Consumer ? "consumer" : "merchant");
            resp["balance"] = user->balance;
        } else {
            resp["status"] = "fail";
            resp["reason"] = err;
        }
        client->send(resp);
        delete user; // 如果 login 中 new 了对象
    }
    else if (action == "fetchProductList") {
        std::vector<Product*> products;
        QString err;
        bool ok = ProductManager::getAllProducts(products, err);
        QJsonObject resp;
        if (ok) {
            resp["status"] = "ok";
            QJsonArray arr;
            for (auto p : products) {
                QJsonObject po;
                p->toJson(po);
                arr.append(po);
                delete p;
            }
            resp["products"] = arr;
        } else {
            resp["status"] = "fail";
            resp["reason"] = err;
        }
        client->send(resp);
    }
    else if (action == "createOrder") {
        int userId = req["userId"].toInt();
        QJsonArray cartArr = req["cart"].toArray();
        std::vector<CartItem> cartItems;
        for (auto v : cartArr) {
            CartItem it;
            it.fromJson(v.toObject());
            cartItems.push_back(it);
        }
        Order order;
        QString err;
        bool ok = OrderManager::createOrder(userId, cartItems, order, err);
        QJsonObject resp;
        if (ok) {
            resp["status"] = "ok";
            resp["orderId"] = order.orderId;
            resp["totalAmount"] = order.totalAmount;
            resp["orderStatus"] = order.status;
        } else {
            resp["status"] = "fail";
            resp["reason"] = err;
        }
        client->send(resp);
    }
    else if (action == "payOrder") {
        int userId = req["userId"].toInt();
        int orderId = req["orderId"].toInt();
        double newBalance = 0.0;
        QString err;
        bool ok = OrderManager::payOrder(userId, orderId, newBalance, err);
        QJsonObject resp;
        if (ok) {
            resp["status"] = "ok";
            resp["newBalance"] = newBalance;
        } else {
            resp["status"] = "fail";
            resp["reason"] = err;
        }
        client->send(resp);
    }
    // … 其他 action: searchProduct, addToCart（可直接由 OrderManager 处理或由 CartManager 记录会话级别购物车）、getOrdersByUser 等
    else {
        QJsonObject resp;
        resp["status"] = "fail";
        resp["reason"] = "未知的请求类型: " + action;
        client->send(resp);
    }
}
```

---

### 6. client/Network/Client.h

```cpp
#pragma once
#include <QObject>
#include <QTcpSocket>
#include <QJsonObject>
#include <QJsonArray>
#include <QString>

class Client : public QObject {
    Q_OBJECT
public:
    explicit Client(QObject* parent = nullptr);
    ~Client();

    bool connectToServer(const QString& host, quint16 port, QString& outError);

    bool login(const QString& username, const QString& password, QString& outError);
    bool fetchProductList(QJsonArray& outProducts, QString& outError);
    bool searchProduct(const QString& key, QJsonArray& outProducts, QString& outError);
    bool addToCart(int productId, int qty, QString& outError);
    bool removeFromCart(int productId, QString& outError);
    bool createOrder(const QList<QPair<int,int>>& cartItems, QJsonObject& outOrderInfo, QString& outError);
    bool payOrder(int orderId, double& outNewBalance, QString& outError);
    bool getOrdersByUser(int userId, QJsonArray& outOrders, QString& outError);

signals:
    void connected();
    void disconnected();
    void errorOccurred(const QString& err);

private slots:
    void onReadyRead();

private:
    QTcpSocket* socket;
    QByteArray buffer; // 缓存未完整接收到的数据

    bool sendRequest(const QJsonObject& req, QJsonObject& resp, QString& outError);
    bool waitForResponse(QJsonObject& resp, QString& outError);
};
```

关键思路：

* `sendRequest()`：调用 `socket->write(json.toJson(...) + "\n\n")`，然后调用 `waitForResponse()` 阻塞或在 `onReadyRead()` 收到信号后解除阻塞并 parse JSON。
* `waitForResponse()`：持续读取 `socket->readAll()`，拼接到 `buffer`，检测是否包含了分隔符 `\n\n`。若检测到完整消息，提取一份 JSON 字符串，调用 `QJsonDocument::fromJson` 解析并返回，将多余数据保留在 `buffer` 中供下一条消息使用。

---

### 7. client/UI/LoginWindow\.h

```cpp
#pragma once
#include <QWidget>
#include "Network/Client.h"
#include "Managers/ClientUserManager.h"

namespace Ui {
class LoginWindow;
}

class LoginWindow : public QWidget {
    Q_OBJECT
public:
    explicit LoginWindow(QWidget* parent = nullptr);
    ~LoginWindow();

private slots:
    void on_loginButton_clicked();

private:
    Ui::LoginWindow* ui;
    Client* client;
    ClientUserManager* userMgr;
};
```

对应的 `LoginWindow.cpp` 里，在 `on_loginButton_clicked()` 中调用 `client->login(...)`，并把成功后得到的 `userId`、`userType`、`balance` 传给 `userMgr->setCurrentUser(...)`，然后跳转到下一窗口。

---

## 六、客户端与服务器端如何交互（总结）

1. **连接建立**

   * 客户端（`Client::connectToServer(host, port)`）调用 `socket->connectToHost(host, port)`，等待 `connected()` 信号。
   * 服务器端 `Server` 在指定端口 `listen()`，当有新的 TCP 连接时，用 `accept()`（或 Qt 的信号 `newConnection()`）获得一个新的 `socketFd`，并为其创建一个 `ClientHandler` 或将其纳入 Reactor 循环。

2. **请求发起（客户端 → 服务器）**

   * 客户端某个操作（登录/获取商品等）触发对应的 `Client::xxx()` 方法。
   * 将 action 与参数封装为 JSON 字符串，调用 `socket->write(jsonStr + "\n\n")`。

3. **请求接收（服务器端）**

   * `ClientHandler` 关联该客户端 socket 的读事件（阻塞或使用 `select`、或者用 Qt 的 `readyRead()` 信号）。
   * 当收到数据时，将字节读入到一个缓存 `QByteArray buffer`。检测 `"\n\n"` 字符串：

     * 如果包含，就按出现位置截取出完整的 JSON 请求（中间若有残留字符串，则保存在 buffer 里以备下一次拼接）。
   * `QJsonDocument::fromJson()` 将字符串解析成 `QJsonObject request`，调用 `MessageDispatcher::dispatch(request, thisClientHandler)`。

4. **服务器端处理（业务逻辑）**

   * `MessageDispatcher` 根据 `request["action"]` 字段，调用对应的 Manager（如 `UserManager`、`ProductManager`、`OrderManager` 等）。
   * Manager 内部可能调用 `FileUtil` 从文件中读取/写入，同时应做好并发控制（若多线程，需保证文件读写互斥；如果单线程 Reactor，可减少并发问题）。
   * 业务逻辑处理结束后，返回一个 `QJsonObject response`，并由 `ClientHandler::send(response)` 把它串化后追加 `\n\n`，写回到客户端 socket。

5. **服务器响应（客户端接收）**

   * 客户端 `Client` 类在 `socket->readyRead()` 信号里读取数据，将字节追加到本地 `buffer`。
   * 同样检测 `"\n\n"` 分隔符，若有完整 JSON 串，就解析 `QJsonDocument::fromJson()`，并返回给发起该请求的函数（使用条件变量、事件循环或回调机制）。
   * 根据 `response["status"]` 做判断：如果 `"ok"`，则返回成功并把必要数据（如商品列表、余额、订单信息等）解析给上层 UI；如果 `"fail"`，则把 `response["reason"]` 提示给用户。

6. **断线与重连**

   * 若客户端发现 `socket->disconnected()`，应弹出“与服务器断开连接”提示，并在 UI 层处理（禁用所有操作按钮，只允许“重新连接”按钮）。
   * 服务器若因异常关闭与客户端断开，客户端需捕获错误并提示。重连成功后，客户端要重新登录或使用 Token（本实验可简化为重新登录）。

---

## 七、错误场景与处理建议

1. **网络故障**

   * 客户端发送请求后若 `socket->state() != ConnectedState`，需立即提示“未连接服务器，请检查网络”并禁用后续所有操作。
   * `Client` 里对 `socket->errorOccurred(QAbstractSocket::SocketError)` 信号做槽函数处理，弹窗提示并自动尝试重连或退出。

2. **登录失败**

   * 服务器返回 `{"status":"fail","reason":"用户名或密码错误"}` → 客户端 `login()` 返回 `false`，UI 弹出“用户名或密码错误”。

3. **库存不足**

   * 在 `addToCart`、`createOrder` 步骤中，若调用 `ProductManager::freezeStock()` 返回失败，服务器响应 `{"status":"fail","reason":"库存不足, xxxx"}`，客户端按原计划回滚本地购物车并提示错误。

4. **并发下单冲突**

   * 多个客户端同时下同一商品：各自调用 `createOrder`，在服务器端 `freezeStock` 时会依次锁定文件或使用内存中锁（如 `std::mutex`），若库存扣减后不足就返回失败，客户端收到相应提示。

5. **支付失败**

   * 可能因为余额不足或库存被他人抢占并释放了冻结库存（跨时间窗口极小概率）；此时服务器返回 `status=fail`，`reason` 说明具体原因，客户端在 `OrderWindow` 中提示“余额不足”或“库存不足”，并可刷新列表后重试。

6. **文件 I/O 错误**

   * 读取/写入文件失败（磁盘空间满、文件被删除、权限问题等），所有 IO 操作都要检查返回值，失败时向客户端返回“服务器内部错误，请稍后重试”，并在服务器日志中记录具体错误以便排查。

---

## 八、总结

* **题目三的核心在于**：

  1. **将单机逻辑拆分为“客户端请求→服务器处理→客户端响应”**。
  2. **统一 JSON 协议**，保证客户端与服务器使用相同 “action + 参数 / status + 数据” 规范。
  3. **Qt 用于客户端 GUI 与网络层（QTcpSocket）**，服务器端可选用底层 BSD socket 或同样使用 Qt 提供的 `QTcpServer/QTcpSocket`。
  4. **目录结构按模块（common/server/client）组织**，`.h/.cpp` 放在一起，避免 include/src 分离。
  5. **强调数据持久化与错误处理**：服务器端要对用户、商品、订单文件进行稳定的读写接口，各 Manager 对操作异常情况要有完善处理。

* **目录示例**（再次简要列出）：

  ```
  ecommerce-platform/
  ├── common/
  │   ├── Protocol.h
  │   ├── DataModels.h
  │   ├── DataModels.cpp
  │   └── Utils.h
  │
  ├── server/
  │   ├── main.cpp
  │   ├── Network/
  │   │   ├── Server.h
  │   │   ├── Server.cpp
  │   │   ├── ClientHandler.h
  │   │   └── ClientHandler.cpp
  │   ├── Dispatcher/
  │   │   ├── MessageDispatcher.h
  │   │   └── MessageDispatcher.cpp
  │   ├── Managers/
  │   │   ├── UserManager.h
  │   │   ├── UserManager.cpp
  │   │   ├── ProductManager.h
  │   │   ├── ProductManager.cpp
  │   │   ├── CartManager.h
  │   │   ├── CartManager.cpp
  │   │   ├── OrderManager.h
  │   │   └── OrderManager.cpp
  │   └── Storage/
  │       ├── FileUtil.h
  │       └── FileUtil.cpp
  │
  └── client/
      ├── main.cpp
      ├── UI/
      │   ├── LoginWindow.h
      │   ├── LoginWindow.cpp
      │   ├── ProductWindow.h
      │   ├── ProductWindow.cpp
      │   ├── CartWindow.h
      │   ├── CartWindow.cpp
      │   ├── OrderWindow.h
      │   └── OrderWindow.cpp
      ├── Network/
      │   ├── Client.h
      │   └── Client.cpp
      ├── Managers/
      │   ├── ClientUserManager.h
      │   ├── ClientUserManager.cpp
      │   ├── ClientCartManager.h
      │   └── ClientCartManager.cpp
      └── Resources/
          ├── images/
          │   └── logo.png
          └── styles.qss
  ```

这样，整个题目三的 **实现思路**、**目录结构** 都已给出；各模块之间的调用关系与客户端—服务器端交互流程也做了详细说明，能够帮助你按照“先整体设计、再逐阶段实现”的思路，完成基于 Qt GUI + C++ socket 的电商交易平台网络版。祝开发顺利！
