#include "ClientManager.h"
#include "Client.h"
#include "../../common/Protocol.h"
#include <QDebug>

ClientManager::ClientManager(QObject* parent)
    : QObject(parent)
    , m_client(new Client(this))
    , m_currentBalance(0.0)
{
    connect(m_client, &Client::connected, this, &ClientManager::onConnected);
    connect(m_client, &Client::disconnected, this, &ClientManager::onDisconnected);
}

ClientManager::~ClientManager()
{
    disconnectFromServer();
}

bool ClientManager::connectToServer(const QString& host, quint16 port)
{
    return m_client->connectToServer(host, port);
}

void ClientManager::disconnectFromServer()
{
    if (isLoggedIn()) {
        QString errorMsg;
        logout(errorMsg);
    }
    m_client->disconnectFromServer();
}

bool ClientManager::isConnected() const
{
    return m_client->isConnected();
}

bool ClientManager::login(const QString& username, const QString& password, QString& errorMsg)
{
    if (!isConnected()) {
        errorMsg = "未连接到服务器";
        return false;
    }
    
    QJsonObject data;
    data["username"] = username;
    data["password"] = password;
    
    QJsonObject response = m_client->sendRequest(Protocol::Request::LOGIN, data);
    
    if (!response["success"].toBool()) {
        errorMsg = response["message"].toString();
        return false;
    }
    
    // 保存用户信息
    QJsonObject userData = response["data"].toObject();
    m_currentUsername = userData["username"].toString();
    m_currentUserType = userData["userType"].toString();
    m_currentBalance = userData["balance"].toDouble();
    m_currentSignature = userData["signature"].toString();
    m_currentAvatarPath = userData["avatarPath"].toString();
    
    emit loginStatusChanged(true);
    emit balanceChanged(m_currentBalance);
    
    return true;
}

bool ClientManager::registerUser(const QString& username, const QString& password, const QString& userType, QString& errorMsg)
{
    if (!isConnected()) {
        errorMsg = "未连接到服务器";
        return false;
    }
    
    QJsonObject data;
    data["username"] = username;
    data["password"] = password;
    data["userType"] = userType;
    
    QJsonObject response = m_client->sendRequest(Protocol::Request::REGISTER, data);
    
    if (!response["success"].toBool()) {
        errorMsg = response["message"].toString();
        return false;
    }
    
    return true;
}

bool ClientManager::logout(QString& errorMsg)
{
    // 发送退出登录请求到服务器（如果连接的话）
    if (isConnected()) {
        QJsonObject response = m_client->sendRequest(Protocol::Request::LOGOUT);

        if (!response["success"].toBool()) {
            errorMsg = response["message"].toString();
            return false;
        }
    }

    // 清除用户信息，但不断开连接，也不发出disconnected信号
    m_currentUsername.clear();
    m_currentUserType.clear();
    m_currentBalance = 0.0;
    m_currentSignature.clear();
    m_currentAvatarPath.clear();

    return true;
}

bool ClientManager::changePassword(const QString& oldPassword, const QString& newPassword, QString& errorMsg)
{
    if (!isConnected()) {
        errorMsg = "未连接到服务器";
        return false;
    }
    
    QJsonObject data;
    data["oldPassword"] = oldPassword;
    data["newPassword"] = newPassword;
    
    QJsonObject response = m_client->sendRequest(Protocol::Request::CHANGE_PASSWORD, data);
    
    if (!response["success"].toBool()) {
        errorMsg = response["message"].toString();
        return false;
    }
    
    return true;
}

bool ClientManager::rechargeBalance(double amount, QString& errorMsg)
{
    if (!isConnected()) {
        errorMsg = "未连接到服务器";
        return false;
    }
    
    QJsonObject data;
    data["amount"] = amount;
    
    QJsonObject response = m_client->sendRequest(Protocol::Request::RECHARGE_BALANCE, data);
    
    if (!response["success"].toBool()) {
        errorMsg = response["message"].toString();
        return false;
    }
    
    // 更新余额
    QJsonObject responseData = response["data"].toObject();
    m_currentBalance = responseData["balance"].toDouble();
    emit balanceChanged(m_currentBalance);
    
    return true;
}

bool ClientManager::updateProfile(const QString& signature, const QString& avatarPath, QString& errorMsg)
{
    if (!isConnected()) {
        errorMsg = "未连接到服务器";
        return false;
    }
    
    QJsonObject data;
    if (!signature.isNull()) {
        data["signature"] = signature;
    }
    if (!avatarPath.isNull()) {
        data["avatarPath"] = avatarPath;
    }
    
    QJsonObject response = m_client->sendRequest(Protocol::Request::UPDATE_PROFILE, data);
    
    if (!response["success"].toBool()) {
        errorMsg = response["message"].toString();
        return false;
    }
    
    // 更新本地信息
    if (!signature.isNull()) {
        m_currentSignature = signature;
    }
    if (!avatarPath.isNull()) {
        m_currentAvatarPath = avatarPath;
    }
    
    return true;
}

bool ClientManager::refreshUserInfo(QString& errorMsg)
{
    if (!isConnected()) {
        errorMsg = "未连接到服务器";
        return false;
    }

    QJsonObject response = m_client->sendRequest(Protocol::Request::GET_USER_INFO);

    if (!response["success"].toBool()) {
        errorMsg = response["message"].toString();
        return false;
    }

    // 解析用户信息
    QJsonObject userInfo = response["data"].toObject()["userInfo"].toObject();

    // 更新本地缓存的用户信息
    double oldBalance = m_currentBalance;
    m_currentBalance = userInfo["balance"].toDouble();
    m_currentSignature = userInfo["signature"].toString();
    m_currentAvatarPath = userInfo["avatarPath"].toString();

    // 如果余额发生变化，发出信号
    if (oldBalance != m_currentBalance) {
        emit balanceChanged(m_currentBalance);
    }

    return true;
}

QVector<ClientProduct> ClientManager::getAllProducts(QString& errorMsg)
{
    QVector<ClientProduct> products;
    
    if (!isConnected()) {
        errorMsg = "未连接到服务器";
        return products;
    }
    
    QJsonObject response = m_client->sendRequest(Protocol::Request::GET_ALL_PRODUCTS);
    
    if (!response["success"].toBool()) {
        errorMsg = response["message"].toString();
        return products;
    }
    
    QJsonArray productArray = response["data"].toObject()["products"].toArray();
    for (const QJsonValue& value : productArray) {
        products.append(jsonToProduct(value.toObject()));
    }
    
    return products;
}

QVector<ClientProduct> ClientManager::searchProducts(const QString& name, QString& errorMsg)
{
    QVector<ClientProduct> products;
    
    if (!isConnected()) {
        errorMsg = "未连接到服务器";
        return products;
    }
    
    QJsonObject data;
    data["name"] = name;
    
    QJsonObject response = m_client->sendRequest(Protocol::Request::SEARCH_PRODUCTS, data);
    
    if (!response["success"].toBool()) {
        errorMsg = response["message"].toString();
        return products;
    }
    
    QJsonArray productArray = response["data"].toObject()["products"].toArray();
    for (const QJsonValue& value : productArray) {
        products.append(jsonToProduct(value.toObject()));
    }
    
    return products;
}

QVector<ClientProduct> ClientManager::filterProducts(const QString& keyword, int category, QString& errorMsg)
{
    QVector<ClientProduct> products;

    if (!isConnected()) {
        errorMsg = "未连接到服务器";
        return products;
    }

    QJsonObject data;
    data["keyword"] = keyword;
    data["category"] = category;

    QJsonObject response = m_client->sendRequest(Protocol::Request::FILTER_PRODUCTS, data);

    if (!response["success"].toBool()) {
        errorMsg = response["message"].toString();
        return products;
    }

    QJsonArray productArray = response["data"].toObject()["products"].toArray();
    for (const QJsonValue& value : productArray) {
        products.append(jsonToProduct(value.toObject()));
    }

    return products;
}

void ClientManager::onConnected()
{
    emit connected();
}

void ClientManager::onDisconnected()
{
    clearUserInfo();
    emit disconnected();
}

void ClientManager::clearUserInfo()
{
    bool wasLoggedIn = isLoggedIn();
    
    m_currentUsername.clear();
    m_currentUserType.clear();
    m_currentBalance = 0.0;
    m_currentSignature.clear();
    m_currentAvatarPath.clear();
    
    if (wasLoggedIn) {
        emit loginStatusChanged(false);
        emit balanceChanged(0.0);
    }
}

ClientProduct ClientManager::jsonToProduct(const QJsonObject& json)
{
    ClientProduct product;
    product.id = json["id"].toInt();
    product.name = json["name"].toString();
    product.originalPrice = json["originalPrice"].toDouble();
    product.discountedPrice = json["discountedPrice"].toDouble();
    product.stock = json["stock"].toInt();
    product.owner = json["owner"].toString();
    product.discount = json["discount"].toDouble();
    product.category = json["category"].toInt();
    product.remark = json["remark"].toString();
    product.author = json["author"].toString();
    product.size = json["size"].toString();
    product.color = json["color"].toString();
    product.imagePath = json["imagePath"].toString();

    return product;
}

CartItem ClientManager::jsonToCartItem(const QJsonObject& json)
{
    CartItem item;
    item.productId = json["productId"].toInt();
    item.quantity = json["quantity"].toInt();
    item.productName = json["productName"].toString();
    item.price = json["price"].toDouble();
    item.totalPrice = json["totalPrice"].toDouble();

    return item;
}

bool ClientManager::addProduct(int category, const QString& name, double price, int stock, double discount,
                              const QString& extra1, const QString& extra2, const QString& remark,
                              const QString& imagePath, QString& errorMsg)
{
    if (!isConnected()) {
        errorMsg = "未连接到服务器";
        return false;
    }

    QJsonObject data;
    data["name"] = name;
    data["price"] = price;
    data["stock"] = stock;
    data["discount"] = discount;
    data["category"] = category;
    data["extra1"] = extra1;
    data["extra2"] = extra2;
    data["remark"] = remark;
    data["imagePath"] = imagePath;

    QJsonObject response = m_client->sendRequest(Protocol::Request::ADD_PRODUCT, data);

    if (!response["success"].toBool()) {
        errorMsg = response["message"].toString();
        return false;
    }

    return true;
}

bool ClientManager::updateProductStock(int productId, int newStock, QString& errorMsg)
{
    if (!isConnected()) {
        errorMsg = "未连接到服务器";
        return false;
    }

    QJsonObject data;
    data["productId"] = productId;
    data["stock"] = newStock;

    QJsonObject response = m_client->sendRequest(Protocol::Request::UPDATE_PRODUCT_STOCK, data);

    if (!response["success"].toBool()) {
        errorMsg = response["message"].toString();
        return false;
    }

    return true;
}

bool ClientManager::updateProductPrice(int productId, double newPrice, QString& errorMsg)
{
    if (!isConnected()) {
        errorMsg = "未连接到服务器";
        return false;
    }

    QJsonObject data;
    data["productId"] = productId;
    data["price"] = newPrice;

    QJsonObject response = m_client->sendRequest(Protocol::Request::UPDATE_PRODUCT_PRICE, data);

    if (!response["success"].toBool()) {
        errorMsg = response["message"].toString();
        return false;
    }

    return true;
}

bool ClientManager::updateProductDiscount(int productId, double newDiscount, QString& errorMsg)
{
    if (!isConnected()) {
        errorMsg = "未连接到服务器";
        return false;
    }

    QJsonObject data;
    data["productId"] = productId;
    data["discount"] = newDiscount;

    QJsonObject response = m_client->sendRequest(Protocol::Request::UPDATE_PRODUCT_DISCOUNT, data);

    if (!response["success"].toBool()) {
        errorMsg = response["message"].toString();
        return false;
    }

    return true;
}

bool ClientManager::batchUpdateDiscount(int category, double newDiscount, QString& errorMsg)
{
    if (!isConnected()) {
        errorMsg = "未连接到服务器";
        return false;
    }

    QJsonObject data;
    data["category"] = category;
    data["discount"] = newDiscount;

    QJsonObject response = m_client->sendRequest(Protocol::Request::BATCH_UPDATE_DISCOUNT, data);

    if (!response["success"].toBool()) {
        errorMsg = response["message"].toString();
        return false;
    }

    return true;
}

bool ClientManager::addToCart(int productId, int quantity, QString& errorMsg)
{
    if (!isConnected()) {
        errorMsg = "未连接到服务器";
        return false;
    }

    QJsonObject data;
    data["productId"] = productId;
    data["quantity"] = quantity;

    QJsonObject response = m_client->sendRequest(Protocol::Request::ADD_TO_CART, data);

    if (!response["success"].toBool()) {
        errorMsg = response["message"].toString();
        return false;
    }

    return true;
}

bool ClientManager::removeFromCart(int productId, QString& errorMsg)
{
    if (!isConnected()) {
        errorMsg = "未连接到服务器";
        return false;
    }

    QJsonObject data;
    data["productId"] = productId;

    QJsonObject response = m_client->sendRequest(Protocol::Request::REMOVE_FROM_CART, data);

    if (!response["success"].toBool()) {
        errorMsg = response["message"].toString();
        return false;
    }

    return true;
}

QVector<CartItem> ClientManager::getCart(QString& errorMsg)
{
    QVector<CartItem> cart;

    if (!isConnected()) {
        errorMsg = "未连接到服务器";
        return cart;
    }

    QJsonObject response = m_client->sendRequest(Protocol::Request::GET_CART);

    if (!response["success"].toBool()) {
        errorMsg = response["message"].toString();
        return cart;
    }

    QJsonArray cartArray = response["data"].toObject()["cartItems"].toArray();
    for (const QJsonValue& value : cartArray) {
        cart.append(jsonToCartItem(value.toObject()));
    }

    return cart;
}

bool ClientManager::clearCart(QString& errorMsg)
{
    if (!isConnected()) {
        errorMsg = "未连接到服务器";
        return false;
    }

    QJsonObject response = m_client->sendRequest(Protocol::Request::CLEAR_CART);

    if (!response["success"].toBool()) {
        errorMsg = response["message"].toString();
        return false;
    }

    return true;
}

bool ClientManager::reserveOrder(const QVector<QPair<int, int>>& orderItems, QString& errorMsg, double& totalAmount)
{
    if (!isConnected()) {
        errorMsg = "未连接到服务器";
        return false;
    }

    // 构造订单项数据
    QJsonArray orderItemsArray;
    for (const auto& item : orderItems) {
        QJsonObject itemObj;
        itemObj["productId"] = item.first;
        itemObj["quantity"] = item.second;
        orderItemsArray.append(itemObj);
    }

    QJsonObject data;
    data["orderItems"] = orderItemsArray;

    QJsonObject response = m_client->sendRequest(Protocol::Request::RESERVE_ORDER, data);

    if (!response["success"].toBool()) {
        errorMsg = response["message"].toString();
        return false;
    }

    // 获取预订单信息
    QJsonObject responseData = response["data"].toObject();
    totalAmount = responseData["totalAmount"].toDouble();

    return true;
}

bool ClientManager::createOrder(QString& errorMsg)
{
    if (!isConnected()) {
        errorMsg = "未连接到服务器";
        return false;
    }

    QJsonObject response = m_client->sendRequest(Protocol::Request::CREATE_ORDER);

    if (!response["success"].toBool()) {
        errorMsg = response["message"].toString();
        return false;
    }

    // 更新余额（如果服务器返回了新余额）
    QJsonObject responseData = response["data"].toObject();
    if (responseData.contains("newBalance")) {
        m_currentBalance = responseData["newBalance"].toDouble();
        emit balanceChanged(m_currentBalance);
    }

    return true;
}

bool ClientManager::cancelOrder(QString& errorMsg)
{
    if (!isConnected()) {
        errorMsg = "未连接到服务器";
        return false;
    }

    QJsonObject response = m_client->sendRequest(Protocol::Request::CANCEL_ORDER);

    if (!response["success"].toBool()) {
        errorMsg = response["message"].toString();
        return false;
    }

    return true;
}

bool ClientManager::createOrderWithItems(const QVector<QPair<int, int>>& orderItems, QString& errorMsg)
{
    if (!isConnected()) {
        errorMsg = "未连接到服务器";
        return false;
    }

    // 构造订单项数据
    QJsonArray orderItemsArray;
    for (const auto& item : orderItems) {
        QJsonObject itemObj;
        itemObj["productId"] = item.first;
        itemObj["quantity"] = item.second;
        orderItemsArray.append(itemObj);
    }

    QJsonObject data;
    data["orderItems"] = orderItemsArray;

    QJsonObject response = m_client->sendRequest(Protocol::Request::CREATE_ORDER, data);

    if (!response["success"].toBool()) {
        errorMsg = response["message"].toString();
        return false;
    }

    // 更新余额
    QJsonObject responseData = response["data"].toObject();
    if (responseData.contains("newBalance")) {
        m_currentBalance = responseData["newBalance"].toDouble();
        emit balanceChanged(m_currentBalance);
    }

    return true;
}
