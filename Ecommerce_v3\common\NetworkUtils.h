#ifndef NETWORKUTILS_H
#define NETWORKUTILS_H

#include <QString>
#include <QJsonObject>
#include <QJsonArray>

// 前向声明
class User;
class Product;
class Order;

class NetworkUtils
{
public:
    // 用户对象序列化
    static QJsonObject userToJson(const User* user);
    static User* userFromJson(const QJsonObject& json);
    
    // 商品对象序列化
    static QJsonObject productToJson(const Product* product);
    static Product* productFromJson(const QJsonObject& json);
    
    // 订单对象序列化
    static QJsonObject orderToJson(const Order* order);
    static Order* orderFromJson(const QJsonObject& json);
    
    // 商品列表序列化
    static QJsonArray productListToJson(const QVector<Product*>& products);
    static QVector<Product*> productListFromJson(const QJsonArray& jsonArray);
    
    // 购物车项序列化
    struct CartItem {
        int productId;
        int quantity;
        QString productName;
        double price;
        
        CartItem() : productId(0), quantity(0), price(0.0) {}
        CartItem(int id, int qty, const QString& name, double p) 
            : productId(id), quantity(qty), productName(name), price(p) {}
    };
    
    static QJsonObject cartItemToJson(const CartItem& item);
    static CartItem cartItemFromJson(const QJsonObject& json);
    static QJsonArray cartToJson(const QVector<CartItem>& cart);
    static QVector<CartItem> cartFromJson(const QJsonArray& jsonArray);
    
    // 工具函数
    static QString generateTimestamp();
    static bool validateJsonObject(const QJsonObject& obj, const QStringList& requiredFields);
    
private:
    NetworkUtils() = delete; // 工具类，禁止实例化
};

#endif // NETWORKUTILS_H
