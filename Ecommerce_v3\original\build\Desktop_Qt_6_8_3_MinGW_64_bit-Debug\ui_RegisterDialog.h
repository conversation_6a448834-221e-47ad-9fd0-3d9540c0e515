/********************************************************************************
** Form generated from reading UI file 'RegisterDialog.ui'
**
** Created by: Qt User Interface Compiler version 6.8.3
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_REGISTERDIALOG_H
#define UI_REGISTERDIALOG_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QDialog>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QVBoxLayout>

QT_BEGIN_NAMESPACE

class Ui_RegisterDialog
{
public:
    QVBoxLayout *verticalLayout;
    QHBoxLayout *horizontalLayout_username;
    QLabel *labelUsername;
    QLineEdit *usernameLineEdit;
    QHBoxLayout *horizontalLayout_password;
    QLabel *labelPassword;
    QLineEdit *passwordLineEdit;
    QHBoxLayout *horizontalLayout_confirm;
    QLabel *labelConfirm;
    QLineEdit *confirmLineEdit;
    QHBoxLayout *horizontalLayout_userType;
    QLabel *labelUserType;
    QComboBox *userTypeCombo;
    QHBoxLayout *horizontalLayout_button;
    QSpacerItem *horizontalSpacer_left;
    QPushButton *registerButton;
    QSpacerItem *horizontalSpacer_right;

    void setupUi(QDialog *RegisterDialog)
    {
        if (RegisterDialog->objectName().isEmpty())
            RegisterDialog->setObjectName("RegisterDialog");
        RegisterDialog->resize(450, 260);
        verticalLayout = new QVBoxLayout(RegisterDialog);
        verticalLayout->setSpacing(8);
        verticalLayout->setContentsMargins(12, 12, 12, 12);
        verticalLayout->setObjectName("verticalLayout");
        horizontalLayout_username = new QHBoxLayout();
        horizontalLayout_username->setSpacing(6);
        horizontalLayout_username->setObjectName("horizontalLayout_username");
        labelUsername = new QLabel(RegisterDialog);
        labelUsername->setObjectName("labelUsername");
        labelUsername->setMinimumSize(QSize(80, 0));

        horizontalLayout_username->addWidget(labelUsername);

        usernameLineEdit = new QLineEdit(RegisterDialog);
        usernameLineEdit->setObjectName("usernameLineEdit");

        horizontalLayout_username->addWidget(usernameLineEdit);


        verticalLayout->addLayout(horizontalLayout_username);

        horizontalLayout_password = new QHBoxLayout();
        horizontalLayout_password->setSpacing(6);
        horizontalLayout_password->setObjectName("horizontalLayout_password");
        labelPassword = new QLabel(RegisterDialog);
        labelPassword->setObjectName("labelPassword");
        labelPassword->setMinimumSize(QSize(80, 0));

        horizontalLayout_password->addWidget(labelPassword);

        passwordLineEdit = new QLineEdit(RegisterDialog);
        passwordLineEdit->setObjectName("passwordLineEdit");
        passwordLineEdit->setEchoMode(QLineEdit::Password);

        horizontalLayout_password->addWidget(passwordLineEdit);


        verticalLayout->addLayout(horizontalLayout_password);

        horizontalLayout_confirm = new QHBoxLayout();
        horizontalLayout_confirm->setSpacing(6);
        horizontalLayout_confirm->setObjectName("horizontalLayout_confirm");
        labelConfirm = new QLabel(RegisterDialog);
        labelConfirm->setObjectName("labelConfirm");
        labelConfirm->setMinimumSize(QSize(80, 0));

        horizontalLayout_confirm->addWidget(labelConfirm);

        confirmLineEdit = new QLineEdit(RegisterDialog);
        confirmLineEdit->setObjectName("confirmLineEdit");
        confirmLineEdit->setEchoMode(QLineEdit::Password);

        horizontalLayout_confirm->addWidget(confirmLineEdit);


        verticalLayout->addLayout(horizontalLayout_confirm);

        horizontalLayout_userType = new QHBoxLayout();
        horizontalLayout_userType->setSpacing(6);
        horizontalLayout_userType->setObjectName("horizontalLayout_userType");
        labelUserType = new QLabel(RegisterDialog);
        labelUserType->setObjectName("labelUserType");
        labelUserType->setMinimumSize(QSize(80, 0));

        horizontalLayout_userType->addWidget(labelUserType);

        userTypeCombo = new QComboBox(RegisterDialog);
        userTypeCombo->setObjectName("userTypeCombo");

        horizontalLayout_userType->addWidget(userTypeCombo);


        verticalLayout->addLayout(horizontalLayout_userType);

        horizontalLayout_button = new QHBoxLayout();
        horizontalLayout_button->setSpacing(12);
        horizontalLayout_button->setObjectName("horizontalLayout_button");
        horizontalSpacer_left = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_button->addItem(horizontalSpacer_left);

        registerButton = new QPushButton(RegisterDialog);
        registerButton->setObjectName("registerButton");
        registerButton->setMinimumSize(QSize(100, 32));

        horizontalLayout_button->addWidget(registerButton);

        horizontalSpacer_right = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_button->addItem(horizontalSpacer_right);


        verticalLayout->addLayout(horizontalLayout_button);


        retranslateUi(RegisterDialog);

        QMetaObject::connectSlotsByName(RegisterDialog);
    } // setupUi

    void retranslateUi(QDialog *RegisterDialog)
    {
        RegisterDialog->setWindowTitle(QCoreApplication::translate("RegisterDialog", "\347\224\250\346\210\267\346\263\250\345\206\214", nullptr));
        labelUsername->setText(QCoreApplication::translate("RegisterDialog", "\347\224\250\346\210\267\345\220\215\357\274\232", nullptr));
        usernameLineEdit->setPlaceholderText(QCoreApplication::translate("RegisterDialog", "2-16\344\275\215\345\255\227\347\254\246", nullptr));
        labelPassword->setText(QCoreApplication::translate("RegisterDialog", "\345\257\206\347\240\201\357\274\232", nullptr));
        passwordLineEdit->setPlaceholderText(QCoreApplication::translate("RegisterDialog", "6-20\344\275\215\345\255\227\347\254\246", nullptr));
        labelConfirm->setText(QCoreApplication::translate("RegisterDialog", "\347\241\256\350\256\244\345\257\206\347\240\201\357\274\232", nullptr));
        confirmLineEdit->setPlaceholderText(QCoreApplication::translate("RegisterDialog", "\345\206\215\346\254\241\350\276\223\345\205\245\345\257\206\347\240\201", nullptr));
        labelUserType->setText(QCoreApplication::translate("RegisterDialog", "\347\224\250\346\210\267\347\261\273\345\236\213\357\274\232", nullptr));
        registerButton->setText(QCoreApplication::translate("RegisterDialog", "\347\253\213\345\215\263\346\263\250\345\206\214", nullptr));
    } // retranslateUi

};

namespace Ui {
    class RegisterDialog: public Ui_RegisterDialog {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_REGISTERDIALOG_H
