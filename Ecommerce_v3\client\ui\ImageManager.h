#ifndef IMAGEMANAGER_H
#define IMAGEMANAGER_H

#include <QString>
#include <QPixmap>
#include <QSize>
#include <QHash>

/**
 * 图片管理类
 * 统一管理商品图片的加载、缓存和默认图片
 */
class ImageManager
{
public:
    // 获取单例实例
    static ImageManager& instance();
    
    // 获取商品图片
    QPixmap getProductImage(const QString& imagePath, int category, const QSize& size = QSize(64, 64));
    
    // 获取默认商品图片
    QPixmap getDefaultProductImage(int category, const QSize& size = QSize(64, 64));
    
    // 保存上传的图片
    QString saveUploadedImage(const QString& sourceFilePath, int productId);
    
    // 清理图片缓存
    void clearCache();
    
    // 商品类别枚举
    enum ProductCategory {
        BookCategory = 0,
        ClothingCategory = 1,
        FoodCategory = 2
    };

private:
    ImageManager() = default;
    ~ImageManager() = default;
    
    // 禁用拷贝构造和赋值
    ImageManager(const ImageManager&) = delete;
    ImageManager& operator=(const ImageManager&) = delete;
    
    // 获取默认图片路径
    QString getDefaultImagePath(int category);
    
    // 图片缓存
    QHash<QString, QPixmap> m_imageCache;
    
    // 默认图片路径
    static const QString DEFAULT_BOOK_IMAGE;
    static const QString DEFAULT_CLOTHING_IMAGE;
    static const QString DEFAULT_FOOD_IMAGE;
};

#endif // IMAGEMANAGER_H
