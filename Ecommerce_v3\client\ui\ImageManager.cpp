#include "ImageManager.h"
#include <QFile>
#include <QDir>
#include <QStandardPaths>
#include <QDebug>

// 默认图片路径常量
const QString ImageManager::DEFAULT_BOOK_IMAGE = ":/images/default_book.jpg";
const QString ImageManager::DEFAULT_CLOTHING_IMAGE = ":/images/default_clothing.jpg";
const QString ImageManager::DEFAULT_FOOD_IMAGE = ":/images/default_food.jpg";

ImageManager& ImageManager::instance()
{
    static ImageManager instance;
    return instance;
}

QPixmap ImageManager::getProductImage(const QString& imagePath, int category, const QSize& size)
{
    // 如果没有指定图片路径，使用默认图片
    if (imagePath.isEmpty()) {
        return getDefaultProductImage(category, size);
    }
    
    // 生成缓存键
    QString cacheKey = QString("%1_%2x%3").arg(imagePath).arg(size.width()).arg(size.height());
    
    // 检查缓存
    if (m_imageCache.contains(cacheKey)) {
        return m_imageCache[cacheKey];
    }
    
    QPixmap pixmap;
    
    // 尝试加载图片
    if (QFile::exists(imagePath)) {
        pixmap.load(imagePath);
    } else {
        // 如果文件不存在，使用默认图片
        pixmap = getDefaultProductImage(category, size);
        m_imageCache[cacheKey] = pixmap;
        return pixmap;
    }
    
    // 如果加载失败，使用默认图片
    if (pixmap.isNull()) {
        pixmap = getDefaultProductImage(category, size);
    } else {
        // 缩放到指定尺寸
        pixmap = pixmap.scaled(size, Qt::KeepAspectRatio, Qt::SmoothTransformation);
    }
    
    // 缓存图片
    m_imageCache[cacheKey] = pixmap;
    return pixmap;
}

QPixmap ImageManager::getDefaultProductImage(int category, const QSize& size)
{
    QString defaultPath = getDefaultImagePath(category);
    QString cacheKey = QString("default_%1_%2x%3").arg(category).arg(size.width()).arg(size.height());
    
    // 检查缓存
    if (m_imageCache.contains(cacheKey)) {
        return m_imageCache[cacheKey];
    }
    
    QPixmap pixmap(defaultPath);
    if (!pixmap.isNull()) {
        pixmap = pixmap.scaled(size, Qt::KeepAspectRatio, Qt::SmoothTransformation);
    }
    
    // 缓存默认图片
    m_imageCache[cacheKey] = pixmap;
    return pixmap;
}

QString ImageManager::saveUploadedImage(const QString& sourceFilePath, int productId)
{
    if (sourceFilePath.isEmpty() || !QFile::exists(sourceFilePath)) {
        return QString();
    }
    
    // 创建产品图片目录
    QString appDataPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QDir appDir(appDataPath);
    if (!appDir.exists()) {
        appDir.mkpath(".");
    }
    
    QDir imageDir(appDataPath + "/product_images");
    if (!imageDir.exists()) {
        imageDir.mkpath(".");
    }
    
    // 生成目标文件名
    QFileInfo sourceInfo(sourceFilePath);
    QString extension = sourceInfo.suffix().toLower();
    if (extension.isEmpty()) {
        extension = "jpg";
    }
    
    QString targetFileName = QString("product_%1.%2").arg(productId).arg(extension);
    QString targetFilePath = imageDir.absoluteFilePath(targetFileName);
    
    // 如果目标文件已存在，先删除
    if (QFile::exists(targetFilePath)) {
        QFile::remove(targetFilePath);
    }
    
    // 复制文件
    if (QFile::copy(sourceFilePath, targetFilePath)) {
        return targetFilePath;
    }
    
    qWarning() << "Failed to save uploaded image:" << sourceFilePath << "to" << targetFilePath;
    return QString();
}

void ImageManager::clearCache()
{
    m_imageCache.clear();
}

QString ImageManager::getDefaultImagePath(int category)
{
    switch (category) {
        case BookCategory:
            return DEFAULT_BOOK_IMAGE;
        case ClothingCategory:
            return DEFAULT_CLOTHING_IMAGE;
        case FoodCategory:
            return DEFAULT_FOOD_IMAGE;
        default:
            return DEFAULT_BOOK_IMAGE; // 默认使用图书图片
    }
}
