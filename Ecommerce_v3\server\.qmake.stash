QMAKE_CXX.QT_COMPILER_STDCXX = 201703L
QMAKE_CXX.QMAKE_GCC_MAJOR_VERSION = 13
QMAKE_CXX.QMAKE_GCC_MINOR_VERSION = 2
QMAKE_CXX.QMAKE_GCC_PATCH_VERSION = 0
QMAKE_CXX.COMPILER_MACROS = \
    QT_COMPILER_STDCXX \
    QMAKE_GCC_MAJOR_VERSION \
    QMAKE_GCC_MINOR_VERSION \
    QMAKE_GCC_PATCH_VERSION
QMAKE_CXX.INCDIRS = \
    "D:/VS Code/mingw/include/c++/13.2.0" \
    "D:/VS Code/mingw/include/c++/13.2.0/x86_64-w64-mingw32" \
    "D:/VS Code/mingw/include/c++/13.2.0/backward" \
    "D:/VS Code/mingw/lib/gcc/x86_64-w64-mingw32/13.2.0/include" \
    "D:/VS Code/mingw/lib/gcc/x86_64-w64-mingw32/13.2.0/include-fixed" \
    "D:/VS Code/mingw/x86_64-w64-mingw32/include"
QMAKE_CXX.LIBDIRS = \
    "D:/VS Code/mingw/lib/gcc/x86_64-w64-mingw32/13.2.0" \
    "D:/VS Code/mingw/lib/gcc" \
    "D:/VS Code/mingw/x86_64-w64-mingw32/lib" \
    "D:/VS Code/mingw/lib"
