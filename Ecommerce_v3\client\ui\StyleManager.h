#ifndef STYLEMANAGER_H
#define STYLEMANAGER_H

#include <QString>

/**
 * 样式管理类
 * 统一管理应用程序的样式表
 */
class StyleManager
{
public:
    // 获取单例实例
    static StyleManager& instance();
    
    // 获取各种样式
    QString getTableStyle();
    QString getButtonStyle(const QString& color = "#4A90E2");
    QString getTabWidgetStyle();
    QString getFormStyle();
    QString getMainWindowStyle();
    
    // 预定义的颜色
    static const QString PRIMARY_COLOR;
    static const QString SUCCESS_COLOR;
    static const QString WARNING_COLOR;
    static const QString DANGER_COLOR;
    static const QString INFO_COLOR;

private:
    StyleManager() = default;
    ~StyleManager() = default;

    // 禁用拷贝构造和赋值
    StyleManager(const StyleManager&) = delete;
    StyleManager& operator=(const StyleManager&) = delete;

    // 辅助方法
    QString adjustColor(const QString& color, int adjustment);
};

#endif // STYLEMANAGER_H
