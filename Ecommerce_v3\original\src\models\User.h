// User.h
#ifndef USER_H
#define USER_H

#include <QString>

class User
{
public:
    enum UserType { ConsumerType, SellerType };

    // 构造函数
    User(const QString& username,
         const QString& password,
         double balance,
         UserType type,
         const QString& signature = "",
         const QString& avatarPath = ":/default_avatar.png");
    virtual ~User() = default;

    const QString& getUsername() const;
    void setUsername(const QString& newName);         

    bool checkPassword(const QString& pwd) const;
    void setPassword(const QString& newPwd);

    double getBalance() const;
    void setBalance(double amount);

    // 文件中读取时直接传入已经保存的 hash
    void setPasswordHashFromFile(const QString& hash);

    // 个性签名 & 头像路径
    QString getSignature() const;
    void setSignature(const QString& signature);

    QString getAvatarPath() const;
    void setAvatarPath(const QString& path);

    virtual UserType getUserType() const = 0;
    QString toCsvString() const; // 序列化：username,hash,balance,type,signature,avatarPath

protected:
    QString m_username;
    QString m_passwordHash; // 存储 SHA-256 hash
    double  m_balance;
    UserType m_type;

    QString m_signature;    // 个性签名
    QString m_avatarPath;   // 头像路径
};

#endif // USER_H
