#ifndef USERMANAGER_H
#define USERMANAGER_H

#include "../models/User.h"
#include "../models/Seller.h"
#include "../models/Consumer.h"
#include <QVector>
#include <QString>

class UserManager
{
public:
    UserManager(const QString& userFilePath);
    ~UserManager();

    bool loadUsers();   // 从文件读取至 m_users
    bool saveUsers();   // 将 m_users 写回文件

    bool registerUser(const QString& username,
                      const QString& password,
                      User::UserType type);
    User* loginUser(const QString& username, const QString& password);
    bool changePassword(const QString& username,
                        const QString& oldPwd,
                        const QString& newPwd);
    bool rechargeBalance(const QString& username, double amount);
    bool deductBalance(const QString& username, double amount);

    // 新增：修改签名/头像
    bool updateSignature(const QString& username, const QString& newSignature);
    bool updateAvatarPath(const QString& username, const QString& newAvatarPath);

    User* getUserByName(const QString& username);

private:
    QString m_userFilePath;
    QVector<User*> m_users;

    bool usernameExists(const QString& username) const;

    // 内部：按行分割 CSV，处理 "\," 转义
    QStringList splitCsvLine(const QString& line) const;
};

#endif // USERMANAGER_H
