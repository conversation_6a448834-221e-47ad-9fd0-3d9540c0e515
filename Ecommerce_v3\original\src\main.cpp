#include <QApplication>
#include "ui/LoginDialog.h"
#include "ui/MainWindow.h"
#include "managers/UserManager.h"
#include "managers/ProductManager.h"

int main(int argc, char *argv[])
{
    QApplication a(argc, argv);

    // 数据文件路径
    QString userFilePath    = "users.csv";
    QString productFilePath = "products.csv";

    // 初始化用户管理器
    UserManager userMgr(userFilePath);
    if (!userMgr.loadUsers()) {
        qWarning("无法加载用户文件，将以空用户列表启动。");
    }

    // 初始化商品管理器（单例）
    ProductManager::init(productFilePath);

    // 登录对话框
    LoginDialog loginDlg(&userMgr);
    User* loggedInUser = nullptr;
    QObject::connect(&loginDlg, &LoginDialog::loginSuccess,
                     [&](User* u){ loggedInUser = u; });

    if (loginDlg.exec() != QDialog::Accepted || !loggedInUser) {
        ProductManager::cleanup();
        return 0;
    }

    // 登录成功，打开主窗口
    MainWindow w(loggedInUser, &userMgr, ProductManager::instance());
    w.show();

    int ret = a.exec();

    // 退出前清理商品单例
    ProductManager::cleanup();
    return ret;
}
