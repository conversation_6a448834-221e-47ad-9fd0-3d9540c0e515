#ifndef ICONMANAGER_H
#define ICONMANAGER_H

#include <QIcon>
#include <QPixmap>
#include <QString>
#include <QHash>

/**
 * 图标管理类
 * 统一管理应用程序中使用的所有图标
 */
class IconManager
{
public:
    // 图标类型枚举
    enum IconType {
        // 按钮图标
        Refresh,
        Add,
        Edit,
        Delete,
        Search,
        Cart,
        Order,
        Logout,
        Profile,
        Recharge,
        
        // 状态图标
        Success,
        Error,
        Warning,
        Info,
        
        // 商品类别图标
        Book,
        Clothing,
        Food,
        
        // 其他图标
        DefaultAvatar,
        Password
    };

    // 获取单例实例
    static IconManager& instance();
    
    // 获取图标
    QIcon getIcon(IconType type);
    QPixmap getPixmap(IconType type, const QSize& size = QSize(32, 32));
    
    // 从资源路径获取图标
    QIcon getIconFromResource(const QString& resourcePath);
    
    // 从文件路径获取图标
    QIcon getIconFromFile(const QString& filePath);
    
    // 设置图标大小
    void setDefaultIconSize(const QSize& size);
    QSize getDefaultIconSize() const;

private:
    IconManager();
    ~IconManager() = default;
    
    // 禁用拷贝构造和赋值
    IconManager(const IconManager&) = delete;
    IconManager& operator=(const IconManager&) = delete;
    
    // 初始化图标映射
    void initializeIconMap();
    
    // 图标缓存
    QHash<IconType, QIcon> m_iconCache;
    QHash<IconType, QString> m_iconPaths;
    QSize m_defaultIconSize;
};

#endif // ICONMANAGER_H
