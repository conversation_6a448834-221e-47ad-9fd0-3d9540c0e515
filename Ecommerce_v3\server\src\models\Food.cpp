#include "Food.h"
#include <QStringList>

Food::Food(int id, const QString& name, double price, int stock,
           const QString& owner, double discount, const QString& imagePath)
    : Product(id, name, price, stock, owner, discount, imagePath)
{
}

QString Food::toCsvString() const
{
    // 基类 toCsvString 包含：id,name,price,stock,category,owner,discount,imagePath
    return Product::toCsvString();
}
