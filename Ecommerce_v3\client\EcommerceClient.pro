QT += core gui widgets network

CONFIG += c++17
CONFIG -= app_bundle

TARGET = EcommerceClient
TEMPLATE = app

# Source files
SOURCES += \
    main.cpp \
    network/Client.cpp \
    network/ClientManager.cpp \
    ui/LoginDialog.cpp \
    ui/RegisterDialog.cpp \
    ui/MainWindow.cpp \
    ui/AddProductDialog.cpp \
    ui/EditProductDialog.cpp \
    ui/ProfileDialog.cpp \
    ui/OrderDialog.cpp \
    ui/IconManager.cpp \
    ui/StyleManager.cpp \
    ui/ImageManager.cpp \
    ../common/Message.cpp \
    ../common/NetworkUtils.cpp

# Header files
HEADERS += \
    network/Client.h \
    network/ClientManager.h \
    ui/LoginDialog.h \
    ui/RegisterDialog.h \
    ui/MainWindow.h \
    ui/AddProductDialog.h \
    ui/EditProductDialog.h \
    ui/ProfileDialog.h \
    ui/OrderDialog.h \
    ui/IconManager.h \
    ui/StyleManager.h \
    ui/ImageManager.h \
    ../common/Protocol.h \
    ../common/Message.h \
    ../common/NetworkUtils.h \
    ../common/ClientProduct.h \
    ../common/CartItem.h

# UI files
FORMS += \
    ui/LoginDialog.ui \
    ui/RegisterDialog.ui \
    ui/MainWindow.ui \
    ui/AddProductDialog.ui \
    ui/EditProductDialog.ui \
    ui/ProfileDialog.ui \
    ui/OrderDialog.ui

RESOURCES += \
    resources.qrc
