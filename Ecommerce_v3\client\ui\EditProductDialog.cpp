#include "EditProductDialog.h"
#include "ui_EditProductDialog.h"
#include <QMessageBox>

EditProductDialog::EditProductDialog(const ClientProduct& product, ClientManager* clientMgr, QWidget *parent) :
    QDialog(parent),
    ui(new Ui::EditProductDialog),
    m_clientMgr(clientMgr),
    m_product(product)
{
    ui->setupUi(this);
    setupUI();
    loadProductData();
}

EditProductDialog::~EditProductDialog()
{
    delete ui;
}

void EditProductDialog::setupUI()
{
    setWindowTitle("修改商品信息");
    setModal(true);
    
    // 设置数值范围
    ui->priceSpinBox->setRange(0.01, 999999.99);
    ui->priceSpinBox->setSingleStep(0.01);
    
    ui->stockSpinBox->setRange(0, 999999);
    
    ui->discountSpinBox->setValue(1.0);
    ui->discountSpinBox->setRange(0.1, 1.0);
    ui->discountSpinBox->setSingleStep(0.1);
}

void EditProductDialog::loadProductData()
{
    ui->productIdLabel->setText(QString::number(m_product.id));
    ui->nameLineEdit->setText(m_product.name);
    ui->priceSpinBox->setValue(m_product.originalPrice);
    ui->stockSpinBox->setValue(m_product.stock);
    ui->discountSpinBox->setValue(m_product.discount);
    ui->remarkTextEdit->setPlainText(m_product.remark);
    
    // 显示类别
    QString categoryName;
    switch (m_product.category) {
        case 0: categoryName = "图书"; break;
        case 1: categoryName = "服装"; break;
        case 2: categoryName = "食品"; break;
        default: categoryName = "未知"; break;
    }
    ui->categoryLabel->setText(categoryName);
    
    // 显示额外信息
    if (m_product.category == 0) { // 图书
        ui->extra1Label->setText("作者：");
        ui->extra1ValueLabel->setText(m_product.author);
        ui->extra2Label->setVisible(false);
        ui->extra2ValueLabel->setVisible(false);
    } else if (m_product.category == 1) { // 服装
        ui->extra1Label->setText("尺寸：");
        ui->extra1ValueLabel->setText(m_product.size);
        ui->extra2Label->setText("颜色：");
        ui->extra2ValueLabel->setText(m_product.color);
        ui->extra2Label->setVisible(true);
        ui->extra2ValueLabel->setVisible(true);
    } else { // 食品
        ui->extra1Label->setVisible(false);
        ui->extra1ValueLabel->setVisible(false);
        ui->extra2Label->setVisible(false);
        ui->extra2ValueLabel->setVisible(false);
    }
}

void EditProductDialog::on_updateButton_clicked()
{
    QString name = ui->nameLineEdit->text().trimmed();
    double price = ui->priceSpinBox->value();
    int stock = ui->stockSpinBox->value();
    double discount = ui->discountSpinBox->value();
    
    if (name.isEmpty()) {
        QMessageBox::warning(this, "输入错误", "商品名称不能为空");
        return;
    }
    
    if (price <= 0) {
        QMessageBox::warning(this, "输入错误", "商品价格必须大于0");
        return;
    }
    
    QString errorMsg;
    bool success = true;
    
    // 分别更新各个字段
    if (price != m_product.originalPrice) {
        if (!m_clientMgr->updateProductPrice(m_product.id, price, errorMsg)) {
            QMessageBox::warning(this, "更新失败", "更新价格失败：" + errorMsg);
            success = false;
        }
    }
    
    if (success && stock != m_product.stock) {
        if (!m_clientMgr->updateProductStock(m_product.id, stock, errorMsg)) {
            QMessageBox::warning(this, "更新失败", "更新库存失败：" + errorMsg);
            success = false;
        }
    }
    
    if (success && discount != m_product.discount) {
        if (!m_clientMgr->updateProductDiscount(m_product.id, discount, errorMsg)) {
            QMessageBox::warning(this, "更新失败", "更新折扣失败：" + errorMsg);
            success = false;
        }
    }
    
    if (success) {
        QMessageBox::information(this, "成功", "商品信息更新成功！");
        accept();
    }
}

void EditProductDialog::on_cancelButton_clicked()
{
    reject();
}
