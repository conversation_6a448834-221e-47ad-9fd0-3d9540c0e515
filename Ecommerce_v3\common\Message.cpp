#include "Message.h"
#include <QJsonParseError>
#include <QDebug>

// Message 类实现
Message::Message() : m_valid(false)
{
}

Message::Message(const QString& action) : m_action(action), m_valid(true)
{
}

Message::Message(const QString& action, const QJsonObject& data) 
    : m_action(action), m_data(data), m_valid(true)
{
}

Message Message::fromJson(const QString& jsonStr)
{
    Message msg;
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(jsonStr.toUtf8(), &error);
    
    if (error.error != QJsonParseError::NoError) {
        qWarning() << "JSON parse error:" << error.errorString();
        return msg;
    }
    
    if (!doc.isObject()) {
        qWarning() << "JSON is not an object";
        return msg;
    }
    
    QJsonObject obj = doc.object();
    if (!obj.contains("action")) {
        qWarning() << "JSON missing 'action' field";
        return msg;
    }
    
    msg.m_action = obj["action"].toString();
    if (obj.contains("data") && obj["data"].isObject()) {
        msg.m_data = obj["data"].toObject();
    }
    msg.m_valid = true;
    
    return msg;
}

QString Message::toJson() const
{
    QJsonObject obj;
    obj["action"] = m_action;
    if (!m_data.isEmpty()) {
        obj["data"] = m_data;
    }
    
    QJsonDocument doc(obj);
    return doc.toJson(QJsonDocument::Compact);
}

// Response 类实现
Response::Response() : m_success(false), m_status(500), m_valid(false)
{
}

Response::Response(bool success, const QString& message) 
    : m_success(success), m_message(message), m_status(success ? 200 : 400), m_valid(true)
{
}

Response::Response(bool success, const QString& message, const QJsonObject& data) 
    : m_success(success), m_message(message), m_data(data), m_status(success ? 200 : 400), m_valid(true)
{
}

Response Response::fromJson(const QString& jsonStr)
{
    Response resp;
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(jsonStr.toUtf8(), &error);
    
    if (error.error != QJsonParseError::NoError) {
        qWarning() << "JSON parse error:" << error.errorString();
        return resp;
    }
    
    if (!doc.isObject()) {
        qWarning() << "JSON is not an object";
        return resp;
    }
    
    QJsonObject obj = doc.object();
    resp.m_success = obj["success"].toBool();
    resp.m_message = obj["message"].toString();
    resp.m_status = obj["status"].toInt(resp.m_success ? 200 : 400);
    
    if (obj.contains("data") && obj["data"].isObject()) {
        resp.m_data = obj["data"].toObject();
    }
    resp.m_valid = true;
    
    return resp;
}

QString Response::toJson() const
{
    QJsonObject obj;
    obj["success"] = m_success;
    obj["message"] = m_message;
    obj["status"] = m_status;
    
    if (!m_data.isEmpty()) {
        obj["data"] = m_data;
    }
    
    QJsonDocument doc(obj);
    return doc.toJson(QJsonDocument::Compact);
}
