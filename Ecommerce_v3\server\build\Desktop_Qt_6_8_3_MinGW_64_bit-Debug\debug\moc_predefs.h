#define __DBL_MIN_EXP__ (-1021)
#define __cpp_nontype_template_parameter_auto 201606L
#define __UINT_LEAST16_MAX__ 0xffff
#define __FLT16_HAS_QUIET_NAN__ 1
#define __ATOMIC_ACQUIRE 2
#define __FLT128_MAX_10_EXP__ 4932
#define __FLT_MIN__ 1.17549435082228750796873653722224568e-38F
#define __GCC_IEC_559_COMPLEX 2
#define __cpp_aggregate_nsdmi 201304L
#define __UINT_LEAST8_TYPE__ unsigned char
#define __SIZEOF_FLOAT80__ 16
#define __BFLT16_DENORM_MIN__ 9.18354961579912115600575419704879436e-41BF16
#define __INTMAX_C(c) c ## LL
#define __CHAR_BIT__ 8
#define __MINGW32__ 1
#define __UINT8_MAX__ 0xff
#define __SCHAR_WIDTH__ 8
#define _WIN64 1
#define __WINT_MAX__ 0xffff
#define __FLT32_MIN_EXP__ (-125)
#define __cpp_static_assert 201411L
#define __BFLT16_MIN_10_EXP__ (-37)
#define __ORDER_LITTLE_ENDIAN__ 1234
#define __WCHAR_MAX__ 0xffff
#define __GCC_HAVE_SYNC_COMPARE_AND_SWAP_2 1
#define __GCC_HAVE_SYNC_COMPARE_AND_SWAP_4 1
#define __GCC_HAVE_SYNC_COMPARE_AND_SWAP_8 1
#define __GCC_ATOMIC_CHAR_LOCK_FREE 2
#define __GCC_IEC_559 2
#define __FLT32X_DECIMAL_DIG__ 17
#define __FLT_EVAL_METHOD__ 0
#define __cpp_binary_literals 201304L
#define __FLT64_DECIMAL_DIG__ 17
#define __cpp_noexcept_function_type 201510L
#define __GCC_ATOMIC_CHAR32_T_LOCK_FREE 2
#define __cpp_variadic_templates 200704L
#define __UINT_FAST64_MAX__ 0xffffffffffffffffULL
#define __SIG_ATOMIC_TYPE__ int
#define __DBL_MIN_10_EXP__ (-307)
#define __FINITE_MATH_ONLY__ 0
#define __cpp_variable_templates 201304L
#define __FLT32X_MAX_EXP__ 1024
#define __GCC_HAVE_SYNC_COMPARE_AND_SWAP_1 1
#define __FLT32_HAS_DENORM__ 1
#define __UINT_FAST8_MAX__ 0xff
#define __cpp_rvalue_reference 200610L
#define __cpp_nested_namespace_definitions 201411L
#define _stdcall __attribute__((__stdcall__))
#define __DEC64_MAX_EXP__ 385
#define __INT8_C(c) c
#define __LDBL_HAS_INFINITY__ 1
#define __INT_LEAST8_WIDTH__ 8
#define __cpp_variadic_using 201611L
#define __UINT_LEAST64_MAX__ 0xffffffffffffffffULL
#define __INT_LEAST8_MAX__ 0x7f
#define __cpp_attributes 200809L
#define __cpp_capture_star_this 201603L
#define __SHRT_MAX__ 0x7fff
#define __LDBL_MAX__ 1.18973149535723176502126385303097021e+4932L
#define __FLT64X_MAX_10_EXP__ 4932
#define __cpp_if_constexpr 201606L
#define __BFLT16_MAX_10_EXP__ 38
#define __BFLT16_MAX_EXP__ 128
#define __LDBL_IS_IEC_60559__ 1
#define __FLT64X_HAS_QUIET_NAN__ 1
#define __UINT_LEAST8_MAX__ 0xff
#define __GCC_ATOMIC_BOOL_LOCK_FREE 2
#define __FLT128_DENORM_MIN__ 6.47517511943802511092443895822764655e-4966F128
#define __UINTMAX_TYPE__ long long unsigned int
#define __cpp_nsdmi 200809L
#define __BFLT16_DECIMAL_DIG__ 4
#define __DEC32_EPSILON__ 1E-6DF
#define __FLT_EVAL_METHOD_TS_18661_3__ 0
#define __UINT32_MAX__ 0xffffffffU
#define __GXX_EXPERIMENTAL_CXX0X__ 1
#define __DBL_DENORM_MIN__ double(4.94065645841246544176568792868221372e-324L)
#define __FLT128_MIN_EXP__ (-16381)
#define __WINT_MIN__ 0
#define __FLT128_MIN_10_EXP__ (-4931)
#define __FLT32X_IS_IEC_60559__ 1
#define __INT_LEAST16_WIDTH__ 16
#define __SCHAR_MAX__ 0x7f
#define __FLT128_MANT_DIG__ 113
#define __WCHAR_MIN__ 0
#define __INT64_C(c) c ## LL
#define __GCC_ATOMIC_POINTER_LOCK_FREE 2
#define __ATOMIC_SEQ_CST 5
#define __INT_LEAST64_MAX__ 0x7fffffffffffffffLL
#define __FLT32X_MANT_DIG__ 53
#define __GCC_ATOMIC_CHAR16_T_LOCK_FREE 2
#define __cpp_aligned_new 201606L
#define __FLT32_MAX_10_EXP__ 38
#define __FLT64X_EPSILON__ 1.08420217248550443400745280086994171e-19F64x
#define __STDC_HOSTED__ 1
#define __DEC64_MIN_EXP__ (-382)
#define __WIN64 1
#define __cpp_decltype_auto 201304L
#define __DBL_DIG__ 15
#define __FLT_EPSILON__ 1.19209289550781250000000000000000000e-7F
#define __GXX_WEAK__ 1
#define __SHRT_WIDTH__ 16
#define __FLT32_IS_IEC_60559__ 1
#define __LDBL_MIN__ 3.36210314311209350626267781732175260e-4932L
#define __DBL_IS_IEC_60559__ 1
#define __DEC32_MAX__ 9.999999E96DF
#define __cpp_threadsafe_static_init 200806L
#define __cpp_enumerator_attributes 201411L
#define __FLT64X_DENORM_MIN__ 3.64519953188247460252840593361941982e-4951F64x
#define __FLT32X_HAS_INFINITY__ 1
#define __INT_WIDTH__ 32
#define __DECIMAL_DIG__ 21
#define __FLT64_EPSILON__ 2.22044604925031308084726333618164062e-16F64
#define __INT16_MAX__ 0x7fff
#define __FLT64_MIN_EXP__ (-1021)
#define __FLT64X_MIN_10_EXP__ (-4931)
#define __LDBL_HAS_QUIET_NAN__ 1
#define __cpp_return_type_deduction 201304L
#define __FLT16_MIN_EXP__ (-13)
#define __FLT64_MANT_DIG__ 53
#define _REENTRANT 1
#define __FLT64X_MANT_DIG__ 64
#define __BFLT16_DIG__ 2
#define __GNUC__ 13
#define _cdecl __attribute__((__cdecl__))
#define __GXX_RTTI 1
#define __MMX__ 1
#define __FLT_HAS_DENORM__ 1
#define __SIZEOF_LONG_DOUBLE__ 16
#define __BIGGEST_ALIGNMENT__ 16
#define __STDC_UTF_16__ 1
#define __SIZE_TYPE__ long long unsigned int
#define __FLT64_MAX_10_EXP__ 308
#define __BFLT16_IS_IEC_60559__ 0
#define __FLT16_MAX_10_EXP__ 4
#define __cpp_delegating_constructors 200604L
#define __DBL_MAX__ double(1.79769313486231570814527423731704357e+308L)
#define _thiscall __attribute__((__thiscall__))
#define __cpp_raw_strings 200710L
#define __INT_FAST32_MAX__ 0x7fffffff
#define __DBL_HAS_INFINITY__ 1
#define __INT64_MAX__ 0x7fffffffffffffffLL
#define __SIZEOF_FLOAT__ 4
#define __WINNT__ 1
#define __HAVE_SPECULATION_SAFE_VALUE 1
#define __cpp_fold_expressions 201603L
#define __DEC32_MIN_EXP__ (-94)
#define __INTPTR_WIDTH__ 64
#define __UINT_LEAST32_MAX__ 0xffffffffU
#define __FLT32X_HAS_DENORM__ 1
#define __INT_FAST16_TYPE__ short int
#define __MMX_WITH_SSE__ 1
#define _fastcall __attribute__((__fastcall__))
#define __LDBL_HAS_DENORM__ 1
#define __SEG_GS 1
#define __BFLT16_EPSILON__ 7.81250000000000000000000000000000000e-3BF16
#define __cplusplus 201703L
#define __cpp_ref_qualifiers 200710L
#define __DEC32_MIN__ 1E-95DF
#define __DEPRECATED 1
#define __cpp_rvalue_references 200610L
#define __DBL_MAX_EXP__ 1024
#define __WCHAR_WIDTH__ 16
#define __FLT32_MAX__ 3.40282346638528859811704183484516925e+38F32
#define __DEC128_EPSILON__ 1E-33DL
#define __FLT16_DECIMAL_DIG__ 5
#define __SSE2_MATH__ 1
#define __ATOMIC_HLE_RELEASE 131072
#define __WIN32__ 1
#define __PTRDIFF_MAX__ 0x7fffffffffffffffLL
#define __amd64 1
#define __tune_core2__ 1
#define __ATOMIC_HLE_ACQUIRE 65536
#define __GNUG__ 13
#define __LONG_LONG_MAX__ 0x7fffffffffffffffLL
#define __SIZEOF_SIZE_T__ 8
#define __BFLT16_HAS_INFINITY__ 1
#define __FLT64X_MIN_EXP__ (-16381)
#define __SIZEOF_WINT_T__ 2
#define __FLT32X_DIG__ 15
#define __LONG_LONG_WIDTH__ 64
#define __cpp_initializer_lists 200806L
#define __FLT32_MAX_EXP__ 128
#define __cpp_hex_float 201603L
#define __GXX_ABI_VERSION 1018
#define __FLT_MIN_EXP__ (-125)
#define __GCC_HAVE_DWARF2_CFI_ASM 1
#define __x86_64 1
#define __cpp_lambdas 200907L
#define __INT_FAST64_TYPE__ long long int
#define __BFLT16_MAX__ 3.38953138925153547590470800371487867e+38BF16
#define __FLT64_DENORM_MIN__ 4.94065645841246544176568792868221372e-324F64
#define __cpp_template_auto 201606L
#define __FLT16_DENORM_MIN__ 5.96046447753906250000000000000000000e-8F16
#define __FLT128_EPSILON__ 1.92592994438723585305597794258492732e-34F128
#define __FLT64X_NORM_MAX__ 1.18973149535723176502126385303097021e+4932F64x
#define __SIZEOF_POINTER__ 8
#define __DBL_HAS_QUIET_NAN__ 1
#define __FLT32X_EPSILON__ 2.22044604925031308084726333618164062e-16F32x
#define __LDBL_MAX_EXP__ 16384
#define __DECIMAL_BID_FORMAT__ 1
#define __GXX_TYPEINFO_EQUALITY_INLINE 0
#define __FLT64_MIN_10_EXP__ (-307)
#define __FLT16_MIN_10_EXP__ (-4)
#define __FLT64X_DECIMAL_DIG__ 21
#define __DEC128_MIN__ 1E-6143DL
#define __REGISTER_PREFIX__ 
#define __UINT16_MAX__ 0xffff
#define __cdecl __attribute__((__cdecl__))
#define __FLT128_HAS_INFINITY__ 1
#define __FLT32_MIN__ 1.17549435082228750796873653722224568e-38F32
#define __UINT8_TYPE__ unsigned char
#define __FLT_DIG__ 6
#define __NO_INLINE__ 1
#define __DEC_EVAL_METHOD__ 2
#define __FLT_MANT_DIG__ 24
#define __LDBL_DECIMAL_DIG__ 21
#define __VERSION__ "13.1.0"
#define __UINT64_C(c) c ## ULL
#define __cpp_unicode_characters 201411L
#define _WIN32 1
#define __SEH__ 1
#define __INT_LEAST32_MAX__ 0x7fffffff
#define __GCC_ATOMIC_INT_LOCK_FREE 2
#define __FLT128_MAX_EXP__ 16384
#define __FLT32_MANT_DIG__ 24
#define __FLOAT_WORD_ORDER__ __ORDER_LITTLE_ENDIAN__
#define __FLT32X_MIN_EXP__ (-1021)
#define __cpp_aggregate_bases 201603L
#define __BFLT16_MIN__ 1.17549435082228750796873653722224568e-38BF16
#define __FLT128_HAS_DENORM__ 1
#define __FLT32_DECIMAL_DIG__ 9
#define __FLT128_DIG__ 33
#define _INTEGRAL_MAX_BITS 64
#define __INT32_C(c) c
#define __DEC64_EPSILON__ 1E-15DD
#define __ORDER_PDP_ENDIAN__ 3412
#define __DEC128_MIN_EXP__ (-6142)
#define __DEC128_MAX__ 9.999999999999999999999999999999999E6144DL
#define __INT_FAST32_TYPE__ int
#define __UINT_LEAST16_TYPE__ short unsigned int
#define __DEC128_MAX_EXP__ 6145
#define __DBL_HAS_DENORM__ 1
#define __cpp_rtti 199711L
#define __UINT64_MAX__ 0xffffffffffffffffULL
#define __FLT_IS_IEC_60559__ 1
#define __GNUC_WIDE_EXECUTION_CHARSET_NAME "UTF-16LE"
#define __FLT64X_DIG__ 18
#define __INT8_TYPE__ signed char
#define __cpp_digit_separators 201309L
#define __GCC_ASM_FLAG_OUTPUTS__ 1
#define __UINT32_TYPE__ unsigned int
#define __BFLT16_HAS_QUIET_NAN__ 1
#define __FLT_RADIX__ 2
#define __INT_LEAST16_TYPE__ short int
#define __LDBL_EPSILON__ 1.08420217248550443400745280086994171e-19L
#define __UINTMAX_C(c) c ## ULL
#define __FLT16_DIG__ 3
#define __FLT32X_MIN__ 2.22507385850720138309023271733240406e-308F32x
#define __SIG_ATOMIC_MAX__ 0x7fffffff
#define __cpp_constexpr 201603L
#define __GCC_ATOMIC_WCHAR_T_LOCK_FREE 2
#define __USER_LABEL_PREFIX__ 
#define __SIZEOF_PTRDIFF_T__ 8
#define __FLT64X_HAS_INFINITY__ 1
#define __SIZEOF_LONG__ 4
#define __LDBL_DIG__ 18
#define __FLT64_IS_IEC_60559__ 1
#define __x86_64__ 1
#define __FLT16_IS_IEC_60559__ 1
#define __FLT16_MAX_EXP__ 16
#define __DEC32_SUBNORMAL_MIN__ 0.000001E-95DF
#define __MSVCRT__ 1
#define __INT_FAST16_MAX__ 0x7fff
#define __GCC_CONSTRUCTIVE_SIZE 64
#define __FLT64_DIG__ 15
#define __UINT_FAST32_MAX__ 0xffffffffU
#define __UINT_LEAST64_TYPE__ long long unsigned int
#define __FLT16_EPSILON__ 9.76562500000000000000000000000000000e-4F16
#define __FLT_HAS_QUIET_NAN__ 1
#define __FLT_MAX_10_EXP__ 38
#define __LONG_MAX__ 0x7fffffffL
#define __FLT64X_HAS_DENORM__ 1
#define __DEC128_SUBNORMAL_MIN__ 0.000000000000000000000000000000001E-6143DL
#define __FLT_HAS_INFINITY__ 1
#define __GNUC_EXECUTION_CHARSET_NAME "UTF-8"
#define __cpp_unicode_literals 200710L
#define __UINT_FAST16_TYPE__ short unsigned int
#define __DEC64_MAX__ 9.999999999999999E384DD
#define __INT_FAST32_WIDTH__ 32
#define __CHAR16_TYPE__ short unsigned int
#define __PRAGMA_REDEFINE_EXTNAME 1
#define __SIZE_WIDTH__ 64
#define __SEG_FS 1
#define __INT_LEAST16_MAX__ 0x7fff
#define __FLT16_NORM_MAX__ 6.55040000000000000000000000000000000e+4F16
#define __DEC64_MANT_DIG__ 16
#define __FLT32_DENORM_MIN__ 1.40129846432481707092372958328991613e-45F32
#define __SIG_ATOMIC_WIDTH__ 32
#define __INT_LEAST64_TYPE__ long long int
#define __INT16_TYPE__ short int
#define __INT_LEAST8_TYPE__ signed char
#define __FLT16_MAX__ 6.55040000000000000000000000000000000e+4F16
#define __nocona__ 1
#define __FLT128_MIN__ 3.36210314311209350626267781732175260e-4932F128
#define __cpp_structured_bindings 201606L
#define __SIZEOF_INT__ 4
#define __DEC32_MAX_EXP__ 97
#define __INT_FAST8_MAX__ 0x7f
#define __FLT128_MAX__ 1.18973149535723176508575932662800702e+4932F128
#define __INTPTR_MAX__ 0x7fffffffffffffffLL
#define __cpp_sized_deallocation 201309L
#define __cpp_guaranteed_copy_elision 201606L
#define __WIN64__ 1
#define __FLT64_HAS_QUIET_NAN__ 1
#define __stdcall __attribute__((__stdcall__))
#define __FLT32_MIN_10_EXP__ (-37)
#define __EXCEPTIONS 1
#define __GXX_MERGED_TYPEINFO_NAMES 0
#define __UINT16_C(c) c
#define __PTRDIFF_WIDTH__ 64
#define __LDBL_MANT_DIG__ 64
#define __cpp_range_based_for 201603L
#define __INT_FAST16_WIDTH__ 16
#define __FLT64_HAS_INFINITY__ 1
#define __FLT64X_MAX__ 1.18973149535723176502126385303097021e+4932F64x
#define __FLT16_HAS_INFINITY__ 1
#define __STDCPP_DEFAULT_NEW_ALIGNMENT__ 16
#define __SIG_ATOMIC_MIN__ (-__SIG_ATOMIC_MAX__ - 1)
#define __GCC_ATOMIC_LONG_LOCK_FREE 2
#define __cpp_nontype_template_args 201411L
#define __DEC32_MANT_DIG__ 7
#define __INTPTR_TYPE__ long long int
#define __UINT16_TYPE__ short unsigned int
#define __WCHAR_TYPE__ short unsigned int
#define __pic__ 1
#define __UINTPTR_MAX__ 0xffffffffffffffffULL
#define __INT_FAST64_WIDTH__ 64
#define __cpp_decltype 200707L
#define __INT_FAST64_MAX__ 0x7fffffffffffffffLL
#define __GCC_ATOMIC_TEST_AND_SET_TRUEVAL 1
#define __FLT_NORM_MAX__ 3.40282346638528859811704183484516925e+38F
#define __FLT32_HAS_INFINITY__ 1
#define __FLT64X_MAX_EXP__ 16384
#define __UINT_FAST64_TYPE__ long long unsigned int
#define __cpp_inline_variables 201606L
#define __BFLT16_MIN_EXP__ (-125)
#define __INT_MAX__ 0x7fffffff
#define WIN32 1
#define __nocona 1
#define __code_model_medium__ 1
#define __INT64_TYPE__ long long int
#define __FLT_MAX_EXP__ 128
#define WIN64 1
#define __ORDER_BIG_ENDIAN__ 4321
#define __DBL_MANT_DIG__ 53
#define __cpp_inheriting_constructors 201511L
#define __SIZEOF_FLOAT128__ 16
#define __BFLT16_MANT_DIG__ 8
#define __DEC64_MIN__ 1E-383DD
#define __WINT_TYPE__ short unsigned int
#define __UINT_LEAST32_TYPE__ unsigned int
#define __SIZEOF_SHORT__ 2
#define __FLT32_NORM_MAX__ 3.40282346638528859811704183484516925e+38F32
#define __SSE__ 1
#define __LDBL_MIN_EXP__ (-16381)
#define __FLT64_MAX__ 1.79769313486231570814527423731704357e+308F64
#define __amd64__ 1
#define __WINT_WIDTH__ 16
#define __INT_LEAST64_WIDTH__ 64
#define __FLT32X_MAX_10_EXP__ 308
#define __cpp_namespace_attributes 201411L
#define __WIN32 1
#define __SIZEOF_INT128__ 16
#define __FLT16_MIN__ 6.10351562500000000000000000000000000e-5F16
#define __FLT64X_IS_IEC_60559__ 1
#define __WCHAR_UNSIGNED__ 1
#define __LDBL_MAX_10_EXP__ 4932
#define __ATOMIC_RELAXED 0
#define __DBL_EPSILON__ double(2.22044604925031308084726333618164062e-16L)
#define __INT_LEAST32_TYPE__ int
#define __thiscall __attribute__((__thiscall__))
#define __UINT8_C(c) c
#define __FLT64_MAX_EXP__ 1024
#define __SIZEOF_WCHAR_T__ 2
#define __GNUC_PATCHLEVEL__ 0
#define __WINNT 1
#define __FLT128_NORM_MAX__ 1.18973149535723176508575932662800702e+4932F128
#define __FLT64_NORM_MAX__ 1.79769313486231570814527423731704357e+308F64
#define __FLT128_HAS_QUIET_NAN__ 1
#define __INTMAX_MAX__ 0x7fffffffffffffffLL
#define __SSE3__ 1
#define __INT_FAST8_TYPE__ signed char
#define __fastcall __attribute__((__fastcall__))
#define __FLT64X_MIN__ 3.36210314311209350626267781732175260e-4932F64x
#define __STDCPP_THREADS__ 1
#define __BFLT16_HAS_DENORM__ 1
#define __GNUC_STDC_INLINE__ 1
#define __FLT64_HAS_DENORM__ 1
#define __FLT32_EPSILON__ 1.19209289550781250000000000000000000e-7F32
#define __FLT16_HAS_DENORM__ 1
#define __DBL_DECIMAL_DIG__ 17
#define __STDC_UTF_32__ 1
#define __INT_FAST8_WIDTH__ 8
#define __FXSR__ 1
#define __FLT32X_MAX__ 1.79769313486231570814527423731704357e+308F32x
#define __DBL_NORM_MAX__ double(1.79769313486231570814527423731704357e+308L)
#define __BYTE_ORDER__ __ORDER_LITTLE_ENDIAN__
#define __MINGW64__ 1
#define __GCC_DESTRUCTIVE_SIZE 64
#define __INTMAX_WIDTH__ 64
#define __cpp_runtime_arrays 198712L
#define __FLT32_DIG__ 6
#define __UINT64_TYPE__ long long unsigned int
#define __UINT32_C(c) c ## U
#define __cpp_alias_templates 200704L
#define WINNT 1
#define __FLT_DENORM_MIN__ 1.40129846432481707092372958328991613e-45F
#define __FLT128_IS_IEC_60559__ 1
#define __INT8_MAX__ 0x7f
#define __LONG_WIDTH__ 32
#define __DBL_MIN__ double(2.22507385850720138309023271733240406e-308L)
#define __PIC__ 1
#define __INT32_MAX__ 0x7fffffff
#define __UINT_FAST32_TYPE__ unsigned int
#define __FLT16_MANT_DIG__ 11
#define __FLT32X_NORM_MAX__ 1.79769313486231570814527423731704357e+308F32x
#define __CHAR32_TYPE__ unsigned int
#define __FLT_MAX__ 3.40282346638528859811704183484516925e+38F
#define __SSE2__ 1
#define __cpp_deduction_guides 201703L
#define __BFLT16_NORM_MAX__ 3.38953138925153547590470800371487867e+38BF16
#define __INT32_TYPE__ int
#define __SIZEOF_DOUBLE__ 8
#define __cpp_exceptions 199711L
#define __FLT_MIN_10_EXP__ (-37)
#define __FLT64_MIN__ 2.22507385850720138309023271733240406e-308F64
#define __INT_LEAST32_WIDTH__ 32
#define __INTMAX_TYPE__ long long int
#define __GLIBCXX_BITSIZE_INT_N_0 128
#define __FLT32X_HAS_QUIET_NAN__ 1
#define __ATOMIC_CONSUME 1
#define __GNUC_MINOR__ 1
#define __GLIBCXX_TYPE_INT_N_0 __int128
#define __UINTMAX_MAX__ 0xffffffffffffffffULL
#define __FLT32X_DENORM_MIN__ 4.94065645841246544176568792868221372e-324F32x
#define __cpp_template_template_args 201611L
#define __DBL_MAX_10_EXP__ 308
#define __LDBL_DENORM_MIN__ 3.64519953188247460252840593361941982e-4951L
#define __INT16_C(c) c
#define __STDC__ 1
#define __PTRDIFF_TYPE__ long long int
#define __GCC_HAVE_SYNC_COMPARE_AND_SWAP_16 1
#define __FLT32X_MIN_10_EXP__ (-307)
#define __UINTPTR_TYPE__ long long unsigned int
#define __DEC64_SUBNORMAL_MIN__ 0.000000000000001E-383DD
#define __DEC128_MANT_DIG__ 34
#define __LDBL_MIN_10_EXP__ (-4931)
#define __cpp_generic_lambdas 201304L
#define __SSE_MATH__ 1
#define __SIZEOF_LONG_LONG__ 8
#define __cpp_user_defined_literals 200809L
#define __FLT128_DECIMAL_DIG__ 36
#define __GCC_ATOMIC_LLONG_LOCK_FREE 2
#define __FLT32_HAS_QUIET_NAN__ 1
#define __FLT_DECIMAL_DIG__ 9
#define __UINT_FAST16_MAX__ 0xffff
#define __LDBL_NORM_MAX__ 1.18973149535723176502126385303097021e+4932L
#define __GCC_ATOMIC_SHORT_LOCK_FREE 2
#define __SIZE_MAX__ 0xffffffffffffffffULL
#define __UINT_FAST8_TYPE__ unsigned char
#define __cpp_init_captures 201304L
#define __ATOMIC_ACQ_REL 4
#define __ATOMIC_RELEASE 3
#define __declspec(x) __attribute__((x))
