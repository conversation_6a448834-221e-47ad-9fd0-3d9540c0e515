#ifndef CLIENT_H
#define CLIENT_H

#include <QObject>
#include <QTcpSocket>
#include <QTimer>
#include <QByteArray>
#include <QJsonObject>

class Client : public QObject
{
    Q_OBJECT

public:
    explicit Client(QObject* parent = nullptr);
    ~Client();
    
    // 连接到服务器
    bool connectToServer(const QString& host, quint16 port);
    
    // 断开连接
    void disconnectFromServer();
    
    // 检查连接状态
    bool isConnected() const;
    
    // 发送请求并等待响应
    QJsonObject sendRequest(const QString& action, const QJsonObject& data = QJsonObject());
    
    // 异步发送请求
    void sendRequestAsync(const QString& action, const QJsonObject& data = QJsonObject());

signals:
    void connected();
    void disconnected();
    void responseReceived(const QJsonObject& response);
    void errorOccurred(const QString& error);

private slots:
    void onConnected();
    void onDisconnected();
    void onReadyRead();
    void onError(QAbstractSocket::SocketError error);
    void onHeartbeatTimer();

private:
    void processResponse(const QString& response);
    void startHeartbeat();
    void stopHeartbeat();

private:
    QTcpSocket* m_socket;
    QByteArray m_buffer;
    QTimer* m_heartbeatTimer;
    
    QString m_serverHost;
    quint16 m_serverPort;
    
    bool m_waitingForResponse;
    QJsonObject m_lastResponse;
    
    static const int HEARTBEAT_INTERVAL = 30000; // 30秒心跳间隔
    static const int RESPONSE_TIMEOUT = 10000;   // 10秒响应超时
};

#endif // CLIENT_H
