QMAKE_CXX.QT_COMPILER_STDCXX = 201703L
QMAKE_CXX.QMAKE_GCC_MAJOR_VERSION = 13
QMAKE_CXX.QMAKE_GCC_MINOR_VERSION = 1
QMAKE_CXX.QMAKE_GCC_PATCH_VERSION = 0
QMAKE_CXX.COMPILER_MACROS = \
    QT_COMPILER_STDCXX \
    QMAKE_GCC_MAJOR_VERSION \
    QMAKE_GCC_MINOR_VERSION \
    QMAKE_GCC_PATCH_VERSION
QMAKE_CXX.INCDIRS = \
    D:/software/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++ \
    D:/software/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32 \
    D:/software/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward \
    D:/software/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include \
    D:/software/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed \
    D:/software/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include
QMAKE_CXX.LIBDIRS = \
    D:/software/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0 \
    D:/software/Qt/Tools/mingw1310_64/lib/gcc \
    D:/software/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/lib \
    D:/software/Qt/Tools/mingw1310_64/lib
