QT       += core gui

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

TARGET = EcommerceApp
TEMPLATE = app

# Include paths (adjust as needed)
INCLUDEPATH += \
    $$PWD/src \
    $$PWD/src/models \
    $$PWD/src/managers \
    $$PWD/src/ui

FORMS += \
    src/ui/EditProfileDialog.ui \
    src/ui/LoginDialog.ui \
    src/ui/OrderDialog.ui \
    src/ui/RegisterDialog.ui \
    src/ui/MainWindow.ui

# Source files
SOURCES += \
    src/main.cpp \
    src/models/Food.cpp \
    src/models/User.cpp \
    src/models/Seller.cpp \
    src/models/Consumer.cpp \
    src/models/Product.cpp \
    src/models/Book.cpp \
    src/models/Clothing.cpp \
    src/models/Order.cpp \
    src/managers/UserManager.cpp \
    src/managers/ProductManager.cpp \
    src/ui/EditProfileDialog.cpp \
    src/ui/LoginDialog.cpp \
    src/ui/OrderDialog.cpp \
    src/ui/RegisterDialog.cpp \
    src/ui/MainWindow.cpp

# Header files (for IDE visibility)
HEADERS += \
    src/models/Food.h \
    src/models/User.h \
    src/models/Seller.h \
    src/models/Consumer.h \
    src/models/Product.h \
    src/models/Book.h \
    src/models/Clothing.h \
    src/models/Order.h \
    src/managers/UserManager.h \
    src/managers/ProductManager.h \
    src/ui/EditProfileDialog.h \
    src/ui/LoginDialog.h \
    src/ui/OrderDialog.h \
    src/ui/RegisterDialog.h \
    src/ui/MainWindow.h

OTHER_FILES +=

RESOURCES += \
    resources.qrc
