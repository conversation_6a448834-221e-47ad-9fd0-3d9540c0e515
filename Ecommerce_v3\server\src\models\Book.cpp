#include "Book.h"
#include <QStringList>

Book::Book(int id, const QString& name, double price, int stock,
           const QString& owner, double discount, const QString& author, const QString& imagePath)
    : Product(id, name, price, stock, owner, discount, imagePath),
    m_author(author)
{
}

QString Book::toCsvString() const
{
    // 基类 toCsvString 返回：id,name,price,stock,category,owner,discount,imagePath
    QString base = Product::toCsvString();
    // 现在在后面再加上 author
    return base + "," + m_author;
}
