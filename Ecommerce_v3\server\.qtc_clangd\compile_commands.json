[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-Wall", "-Wextra", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\common", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\managers", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\build\\moc", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\main.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/main.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-Wall", "-Wextra", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\common", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\managers", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\build\\moc", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\network\\Server.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/network/Server.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-Wall", "-Wextra", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\common", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\managers", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\build\\moc", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\network\\ClientHandler.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/network/ClientHandler.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-Wall", "-Wextra", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\common", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\managers", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\build\\moc", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\dispatcher\\MessageDispatcher.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/dispatcher/MessageDispatcher.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-Wall", "-Wextra", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\common", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\managers", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\build\\moc", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models\\User.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/src/models/User.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-Wall", "-Wextra", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\common", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\managers", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\build\\moc", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models\\Consumer.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/src/models/Consumer.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-Wall", "-Wextra", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\common", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\managers", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\build\\moc", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models\\Seller.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/src/models/Seller.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-Wall", "-Wextra", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\common", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\managers", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\build\\moc", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models\\Product.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/src/models/Product.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-Wall", "-Wextra", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\common", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\managers", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\build\\moc", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models\\Book.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/src/models/Book.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-Wall", "-Wextra", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\common", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\managers", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\build\\moc", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models\\Clothing.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/src/models/Clothing.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-Wall", "-Wextra", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\common", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\managers", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\build\\moc", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models\\Food.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/src/models/Food.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-Wall", "-Wextra", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\common", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\managers", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\build\\moc", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models\\Order.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/src/models/Order.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-Wall", "-Wextra", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\common", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\managers", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\build\\moc", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\managers\\UserManager.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/src/managers/UserManager.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-Wall", "-Wextra", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\common", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\managers", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\build\\moc", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\managers\\ProductManager.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/src/managers/ProductManager.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-Wall", "-Wextra", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\common", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\managers", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\build\\moc", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\common\\Message.cpp"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/common/Message.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-Wall", "-Wextra", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\common", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\managers", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\build\\moc", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\network\\Server.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/network/Server.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-Wall", "-Wextra", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\common", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\managers", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\build\\moc", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\network\\ClientHandler.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/network/ClientHandler.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-Wall", "-Wextra", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\common", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\managers", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\build\\moc", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\dispatcher\\MessageDispatcher.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/dispatcher/MessageDispatcher.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-Wall", "-Wextra", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\common", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\managers", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\build\\moc", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models\\User.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/src/models/User.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-Wall", "-Wextra", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\common", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\managers", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\build\\moc", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models\\Consumer.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/src/models/Consumer.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-Wall", "-Wextra", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\common", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\managers", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\build\\moc", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models\\Seller.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/src/models/Seller.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-Wall", "-Wextra", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\common", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\managers", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\build\\moc", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models\\Product.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/src/models/Product.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-Wall", "-Wextra", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\common", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\managers", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\build\\moc", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models\\Book.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/src/models/Book.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-Wall", "-Wextra", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\common", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\managers", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\build\\moc", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models\\Clothing.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/src/models/Clothing.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-Wall", "-Wextra", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\common", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\managers", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\build\\moc", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models\\Food.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/src/models/Food.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-Wall", "-Wextra", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\common", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\managers", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\build\\moc", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models\\Order.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/src/models/Order.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-Wall", "-Wextra", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\common", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\managers", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\build\\moc", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\managers\\UserManager.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/src/managers/UserManager.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-Wall", "-Wextra", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\common", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\managers", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\build\\moc", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\managers\\ProductManager.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/src/managers/ProductManager.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-Wall", "-Wextra", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\common", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\managers", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\build\\moc", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\common\\Protocol.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/common/Protocol.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-Wall", "-Wextra", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\common", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\managers", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\build\\moc", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\common\\Message.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/common/Message.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-Wall", "-Wextra", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\software\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\common", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\models", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\src\\managers", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtNetwork", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\include\\QtCore", "-ID:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\server\\build\\moc", "-ID:\\software\\Qt\\6.8.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\software\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\software\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\myCode\\Qt\\E-Commerce-Trading-Platform\\Ecommerce_v3\\common\\NetworkUtils.h"], "directory": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/.qtc_clangd", "file": "D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/common/NetworkUtils.h"}]