#include "ClientHandler.h"
#include "../dispatcher/MessageDispatcher.h"
#include "../../common/Message.h"
#include <QDebug>
#include <QHostAddress>

ClientHandler::ClientHandler(qintptr socketDescriptor, MessageDispatcher* dispatcher, QObject* parent)
    : QObject(parent)
    , m_socket(new QTcpSocket(this))
    , m_dispatcher(dispatcher)
    , m_currentUser(nullptr)
    , m_heartbeatTimer(new QTimer(this))
{
    // 设置socket
    if (!m_socket->setSocketDescriptor(socketDescriptor)) {
        qCritical() << "Failed to set socket descriptor:" << m_socket->errorString();
        deleteLater();
        return;
    }
    
    // 获取客户端地址信息
    m_clientAddress = m_socket->peerAddress().toString();
    m_clientPort = m_socket->peerPort();
    
    // 连接信号
    connect(m_socket, &QTcpSocket::readyRead, this, &ClientHandler::onReadyRead);
    connect(m_socket, &QTcpSocket::disconnected, this, &ClientHandler::onDisconnected);
    connect(m_socket, QOverload<QAbstractSocket::SocketError>::of(&QAbstractSocket::errorOccurred),
            this, &ClientHandler::onError);
    
    // 设置心跳
    setupHeartbeat();
    
    qInfo() << "ClientHandler created for" << getClientInfo();
}

ClientHandler::~ClientHandler()
{
    if (m_socket && m_socket->state() != QAbstractSocket::UnconnectedState) {
        m_socket->disconnectFromHost();
    }
    qInfo() << "ClientHandler destroyed for" << getClientInfo();
}

QString ClientHandler::getClientInfo() const
{
    return QString("%1:%2").arg(m_clientAddress).arg(m_clientPort);
}

bool ClientHandler::isConnected() const
{
    return m_socket && m_socket->state() == QAbstractSocket::ConnectedState;
}

void ClientHandler::disconnectFromHost()
{
    if (m_socket) {
        m_socket->disconnectFromHost();
    }

    // 通知MessageDispatcher清理该客户端的预订单
    emit clientDisconnecting(this);
}

void ClientHandler::sendResponse(const QString& response)
{
    if (!isConnected()) {
        qWarning() << "Cannot send response to disconnected client:" << getClientInfo();
        return;
    }
    
    QByteArray data = response.toUtf8();
    // 添加消息长度前缀（4字节）
    QByteArray lengthPrefix;
    lengthPrefix.resize(4);
    qToBigEndian(static_cast<quint32>(data.size()), lengthPrefix.data());
    
    m_socket->write(lengthPrefix + data);
    m_socket->flush();
    
    resetHeartbeat();
}

void ClientHandler::onReadyRead()
{
    m_buffer.append(m_socket->readAll());
    
    // 处理完整的消息
    while (m_buffer.size() >= 4) {
        // 读取消息长度
        quint32 messageLength = qFromBigEndian<quint32>(m_buffer.constData());
        
        if (m_buffer.size() < 4 + messageLength) {
            // 消息还没有完全接收
            break;
        }
        
        // 提取完整消息
        QByteArray messageData = m_buffer.mid(4, messageLength);
        m_buffer.remove(0, 4 + messageLength);
        
        QString message = QString::fromUtf8(messageData);
        processMessage(message);
    }
    
    resetHeartbeat();
}

void ClientHandler::onDisconnected()
{
    qInfo() << "Client disconnected:" << getClientInfo();
    m_heartbeatTimer->stop();
    emit disconnected();
}

void ClientHandler::onError(QAbstractSocket::SocketError error)
{
    qWarning() << "Socket error for client" << getClientInfo() << ":" << error << m_socket->errorString();
}

void ClientHandler::onHeartbeatTimeout()
{
    qWarning() << "Heartbeat timeout for client:" << getClientInfo();
    disconnectFromHost();
}

void ClientHandler::processMessage(const QString& message)
{
    qDebug() << "Received message from" << getClientInfo() << ":" << message;
    
    if (!m_dispatcher) {
        qCritical() << "MessageDispatcher is null!";
        return;
    }
    
    QString response = m_dispatcher->processMessage(message, this);
    sendResponse(response);
}

void ClientHandler::setupHeartbeat()
{
    m_heartbeatTimer->setSingleShot(true);
    m_heartbeatTimer->setInterval(HEARTBEAT_TIMEOUT);
    connect(m_heartbeatTimer, &QTimer::timeout, this, &ClientHandler::onHeartbeatTimeout);
    m_heartbeatTimer->start();
}

void ClientHandler::resetHeartbeat()
{
    if (m_heartbeatTimer->isActive()) {
        m_heartbeatTimer->stop();
    }
    m_heartbeatTimer->start();
}
