#ifndef ORDERDIALOG_H
#define ORDERDIALOG_H

#include <QDialog>
#include <QVector>
#include "../network/ClientManager.h"

namespace Ui {
class OrderDialog;
}

struct OrderItem {
    int productId;
    QString productName;
    double price;          // 折后价
    double originalPrice;  // 原价
    int quantity;
    int maxStock;
    double totalPrice;
    bool selected;
    QString owner;         // 商家

    OrderItem() : productId(0), price(0), originalPrice(0), quantity(0), maxStock(0), totalPrice(0), selected(true) {}
};

class OrderDialog : public QDialog
{
    Q_OBJECT

public:
    explicit OrderDialog(const QVector<CartItem>& cartItems, ClientManager* clientMgr, QWidget *parent = nullptr);
    ~OrderDialog();

private slots:
    void on_payOrderButton_clicked();
    void on_cancelButton_clicked();

private:
    void setupUI();
    void loadCartItems();
    void updateTotalAmount();

private:
    Ui::OrderDialog *ui;
    ClientManager* m_clientMgr;
    QVector<OrderItem> m_orderItems;
    double m_totalAmount;
};

#endif // ORDERDIALOG_H
