QT += core network
QT -= gui

CONFIG += c++17 console
CONFIG -= app_bundle

TARGET = EcommerceServer
TEMPLATE = app

# Include paths
INCLUDEPATH += \
    $$PWD \
    $$PWD/../common \
    $$PWD/src/models \
    $$PWD/src/managers

# Source files
SOURCES += \
    main.cpp \
    network/Server.cpp \
    network/ClientHandler.cpp \
    dispatcher/MessageDispatcher.cpp \
    src/models/User.cpp \
    src/models/Consumer.cpp \
    src/models/Seller.cpp \
    src/models/Product.cpp \
    src/models/Book.cpp \
    src/models/Clothing.cpp \
    src/models/Food.cpp \
    src/models/Order.cpp \
    src/managers/UserManager.cpp \
    src/managers/ProductManager.cpp \
    ../common/Message.cpp

# Header files
HEADERS += \
    network/Server.h \
    network/ClientHandler.h \
    dispatcher/MessageDispatcher.h \
    src/models/User.h \
    src/models/Consumer.h \
    src/models/Seller.h \
    src/models/Product.h \
    src/models/Book.h \
    src/models/Clothing.h \
    src/models/Food.h \
    src/models/Order.h \
    src/managers/UserManager.h \
    src/managers/ProductManager.h \
    ../common/Protocol.h \
    ../common/Message.h \
    ../common/NetworkUtils.h

# Build directories
DESTDIR = $$PWD/build
OBJECTS_DIR = $$PWD/build/obj
MOC_DIR = $$PWD/build/moc
RCC_DIR = $$PWD/build/rcc
UI_DIR = $$PWD/build/ui

# Compiler flags
QMAKE_CXXFLAGS += -Wall -Wextra
