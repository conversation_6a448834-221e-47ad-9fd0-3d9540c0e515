/********************************************************************************
** Form generated from reading UI file 'AddProductDialog.ui'
**
** Created by: Qt User Interface Compiler version 6.8.3
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_ADDPRODUCTDIALOG_H
#define UI_ADDPRODUCTDIALOG_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QDialog>
#include <QtWidgets/QDoubleSpinBox>
#include <QtWidgets/QFormLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QSpinBox>
#include <QtWidgets/QTextEdit>
#include <QtWidgets/QVBoxLayout>

QT_BEGIN_NAMESPACE

class Ui_AddProductDialog
{
public:
    QVBoxLayout *verticalLayout;
    QFormLayout *formLayout;
    QLabel *nameLabel;
    QLineEdit *nameLineEdit;
    QLabel *categoryLabel;
    QComboBox *categoryCombo;
    QLabel *priceLabel;
    QDoubleSpinBox *priceSpinBox;
    QLabel *stockLabel;
    QSpinBox *stockSpinBox;
    QLabel *discountLabel;
    QDoubleSpinBox *discountSpinBox;
    QLabel *extra1Label;
    QLineEdit *extra1LineEdit;
    QLabel *extra2Label;
    QLineEdit *extra2LineEdit;
    QLabel *imageLabel;
    QHBoxLayout *imageLayout;
    QLabel *imagePreviewLabel;
    QPushButton *selectImageButton;
    QPushButton *clearImageButton;
    QSpacerItem *horizontalSpacer_image;
    QLabel *remarkLabel;
    QTextEdit *remarkTextEdit;
    QSpacerItem *verticalSpacer;
    QHBoxLayout *buttonLayout;
    QSpacerItem *horizontalSpacer;
    QPushButton *addButton;
    QPushButton *cancelButton;

    void setupUi(QDialog *AddProductDialog)
    {
        if (AddProductDialog->objectName().isEmpty())
            AddProductDialog->setObjectName("AddProductDialog");
        AddProductDialog->resize(450, 400);
        verticalLayout = new QVBoxLayout(AddProductDialog);
        verticalLayout->setSpacing(8);
        verticalLayout->setObjectName("verticalLayout");
        verticalLayout->setContentsMargins(12, 12, 12, 12);
        formLayout = new QFormLayout();
        formLayout->setObjectName("formLayout");
        formLayout->setFieldGrowthPolicy(QFormLayout::ExpandingFieldsGrow);
        nameLabel = new QLabel(AddProductDialog);
        nameLabel->setObjectName("nameLabel");

        formLayout->setWidget(0, QFormLayout::LabelRole, nameLabel);

        nameLineEdit = new QLineEdit(AddProductDialog);
        nameLineEdit->setObjectName("nameLineEdit");

        formLayout->setWidget(0, QFormLayout::FieldRole, nameLineEdit);

        categoryLabel = new QLabel(AddProductDialog);
        categoryLabel->setObjectName("categoryLabel");

        formLayout->setWidget(1, QFormLayout::LabelRole, categoryLabel);

        categoryCombo = new QComboBox(AddProductDialog);
        categoryCombo->setObjectName("categoryCombo");

        formLayout->setWidget(1, QFormLayout::FieldRole, categoryCombo);

        priceLabel = new QLabel(AddProductDialog);
        priceLabel->setObjectName("priceLabel");

        formLayout->setWidget(2, QFormLayout::LabelRole, priceLabel);

        priceSpinBox = new QDoubleSpinBox(AddProductDialog);
        priceSpinBox->setObjectName("priceSpinBox");
        priceSpinBox->setDecimals(2);
        priceSpinBox->setMinimum(0.010000000000000);
        priceSpinBox->setMaximum(999999.989999999990687);

        formLayout->setWidget(2, QFormLayout::FieldRole, priceSpinBox);

        stockLabel = new QLabel(AddProductDialog);
        stockLabel->setObjectName("stockLabel");

        formLayout->setWidget(3, QFormLayout::LabelRole, stockLabel);

        stockSpinBox = new QSpinBox(AddProductDialog);
        stockSpinBox->setObjectName("stockSpinBox");
        stockSpinBox->setMaximum(999999);

        formLayout->setWidget(3, QFormLayout::FieldRole, stockSpinBox);

        discountLabel = new QLabel(AddProductDialog);
        discountLabel->setObjectName("discountLabel");

        formLayout->setWidget(4, QFormLayout::LabelRole, discountLabel);

        discountSpinBox = new QDoubleSpinBox(AddProductDialog);
        discountSpinBox->setObjectName("discountSpinBox");
        discountSpinBox->setDecimals(1);
        discountSpinBox->setMinimum(0.100000000000000);
        discountSpinBox->setMaximum(1.000000000000000);
        discountSpinBox->setSingleStep(0.100000000000000);
        discountSpinBox->setValue(1.000000000000000);

        formLayout->setWidget(4, QFormLayout::FieldRole, discountSpinBox);

        extra1Label = new QLabel(AddProductDialog);
        extra1Label->setObjectName("extra1Label");

        formLayout->setWidget(5, QFormLayout::LabelRole, extra1Label);

        extra1LineEdit = new QLineEdit(AddProductDialog);
        extra1LineEdit->setObjectName("extra1LineEdit");

        formLayout->setWidget(5, QFormLayout::FieldRole, extra1LineEdit);

        extra2Label = new QLabel(AddProductDialog);
        extra2Label->setObjectName("extra2Label");

        formLayout->setWidget(6, QFormLayout::LabelRole, extra2Label);

        extra2LineEdit = new QLineEdit(AddProductDialog);
        extra2LineEdit->setObjectName("extra2LineEdit");

        formLayout->setWidget(6, QFormLayout::FieldRole, extra2LineEdit);

        imageLabel = new QLabel(AddProductDialog);
        imageLabel->setObjectName("imageLabel");

        formLayout->setWidget(7, QFormLayout::LabelRole, imageLabel);

        imageLayout = new QHBoxLayout();
        imageLayout->setObjectName("imageLayout");
        imagePreviewLabel = new QLabel(AddProductDialog);
        imagePreviewLabel->setObjectName("imagePreviewLabel");
        imagePreviewLabel->setMinimumSize(QSize(64, 64));
        imagePreviewLabel->setMaximumSize(QSize(64, 64));
        imagePreviewLabel->setAlignment(Qt::AlignCenter);

        imageLayout->addWidget(imagePreviewLabel);

        selectImageButton = new QPushButton(AddProductDialog);
        selectImageButton->setObjectName("selectImageButton");

        imageLayout->addWidget(selectImageButton);

        clearImageButton = new QPushButton(AddProductDialog);
        clearImageButton->setObjectName("clearImageButton");

        imageLayout->addWidget(clearImageButton);

        horizontalSpacer_image = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        imageLayout->addItem(horizontalSpacer_image);


        formLayout->setLayout(7, QFormLayout::FieldRole, imageLayout);

        remarkLabel = new QLabel(AddProductDialog);
        remarkLabel->setObjectName("remarkLabel");

        formLayout->setWidget(8, QFormLayout::LabelRole, remarkLabel);

        remarkTextEdit = new QTextEdit(AddProductDialog);
        remarkTextEdit->setObjectName("remarkTextEdit");
        remarkTextEdit->setMaximumSize(QSize(16777215, 80));

        formLayout->setWidget(8, QFormLayout::FieldRole, remarkTextEdit);


        verticalLayout->addLayout(formLayout);

        verticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        verticalLayout->addItem(verticalSpacer);

        buttonLayout = new QHBoxLayout();
        buttonLayout->setObjectName("buttonLayout");
        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        buttonLayout->addItem(horizontalSpacer);

        addButton = new QPushButton(AddProductDialog);
        addButton->setObjectName("addButton");
        addButton->setMinimumSize(QSize(80, 30));

        buttonLayout->addWidget(addButton);

        cancelButton = new QPushButton(AddProductDialog);
        cancelButton->setObjectName("cancelButton");
        cancelButton->setMinimumSize(QSize(80, 30));

        buttonLayout->addWidget(cancelButton);


        verticalLayout->addLayout(buttonLayout);


        retranslateUi(AddProductDialog);

        QMetaObject::connectSlotsByName(AddProductDialog);
    } // setupUi

    void retranslateUi(QDialog *AddProductDialog)
    {
        AddProductDialog->setWindowTitle(QCoreApplication::translate("AddProductDialog", "\346\267\273\345\212\240\345\225\206\345\223\201", nullptr));
        nameLabel->setText(QCoreApplication::translate("AddProductDialog", "\345\225\206\345\223\201\345\220\215\347\247\260\357\274\232", nullptr));
        nameLineEdit->setPlaceholderText(QCoreApplication::translate("AddProductDialog", "\350\257\267\350\276\223\345\205\245\345\225\206\345\223\201\345\220\215\347\247\260", nullptr));
        categoryLabel->setText(QCoreApplication::translate("AddProductDialog", "\345\225\206\345\223\201\347\261\273\345\210\253\357\274\232", nullptr));
        priceLabel->setText(QCoreApplication::translate("AddProductDialog", "\345\225\206\345\223\201\344\273\267\346\240\274\357\274\232", nullptr));
        priceSpinBox->setSuffix(QCoreApplication::translate("AddProductDialog", " \345\205\203", nullptr));
        stockLabel->setText(QCoreApplication::translate("AddProductDialog", "\345\272\223\345\255\230\346\225\260\351\207\217\357\274\232", nullptr));
        stockSpinBox->setSuffix(QCoreApplication::translate("AddProductDialog", " \344\273\266", nullptr));
        discountLabel->setText(QCoreApplication::translate("AddProductDialog", "\346\212\230\346\211\243\357\274\232", nullptr));
        extra1Label->setText(QCoreApplication::translate("AddProductDialog", "\351\242\235\345\244\226\344\277\241\346\201\2571\357\274\232", nullptr));
        extra2Label->setText(QCoreApplication::translate("AddProductDialog", "\351\242\235\345\244\226\344\277\241\346\201\2572\357\274\232", nullptr));
        imageLabel->setText(QCoreApplication::translate("AddProductDialog", "\345\225\206\345\223\201\345\233\276\347\211\207\357\274\232", nullptr));
        imagePreviewLabel->setText(QCoreApplication::translate("AddProductDialog", "\346\227\240\345\233\276\347\211\207", nullptr));
        imagePreviewLabel->setStyleSheet(QCoreApplication::translate("AddProductDialog", "border: 1px solid #ccc;", nullptr));
        selectImageButton->setText(QCoreApplication::translate("AddProductDialog", "\351\200\211\346\213\251\345\233\276\347\211\207", nullptr));
        clearImageButton->setText(QCoreApplication::translate("AddProductDialog", "\346\270\205\351\231\244\345\233\276\347\211\207", nullptr));
        remarkLabel->setText(QCoreApplication::translate("AddProductDialog", "\345\225\206\345\223\201\345\244\207\346\263\250\357\274\232", nullptr));
        remarkTextEdit->setPlaceholderText(QCoreApplication::translate("AddProductDialog", "\345\217\257\351\200\211\347\232\204\345\225\206\345\223\201\346\217\217\350\277\260\344\277\241\346\201\257", nullptr));
        addButton->setText(QCoreApplication::translate("AddProductDialog", "\346\267\273\345\212\240", nullptr));
        cancelButton->setText(QCoreApplication::translate("AddProductDialog", "\345\217\226\346\266\210", nullptr));
    } // retranslateUi

};

namespace Ui {
    class AddProductDialog: public Ui_AddProductDialog {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_ADDPRODUCTDIALOG_H
