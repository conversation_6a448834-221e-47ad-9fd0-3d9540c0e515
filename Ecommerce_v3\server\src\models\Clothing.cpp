#include "Clothing.h"
#include <QStringList>

Clothing::Clothing(int id, const QString& name, double price, int stock,
                   const QString& owner, double discount,
                   const QString& size, const QString& color, const QString& imagePath)
    : Product(id, name, price, stock, owner, discount, imagePath),
    m_size(size), m_color(color)
{
}

QString Clothing::toCsvString() const
{
    // 基类：id,name,price,stock,category,owner,discount,imagePath
    QString base = Product::toCsvString();
    // 额外 append size, color
    return base + "," + m_size + "," + m_color;
}
