#include "ProductManager.h"
#include "Book.h"
#include "Clothing.h"
#include "Food.h"
#include <QFile>
#include <QTextStream>
#include <QDebug>

static ProductManager* s_instance = nullptr;

// 返回单例
ProductManager* ProductManager::instance()
{
    return s_instance;
}

// 创建单例并 load
void ProductManager::init(const QString& productFilePath)
{
    if (!s_instance) {
        s_instance = new ProductManager(productFilePath);
        s_instance->loadProducts();
    }
}

// 销毁单例
void ProductManager::cleanup()
{
    delete s_instance;
    s_instance = nullptr;
}

ProductManager::ProductManager(const QString& productFilePath)
    : m_productFilePath(productFilePath)
{
}

ProductManager::~ProductManager()
{
    qDeleteAll(m_products);
    m_products.clear();
}

bool ProductManager::idExists(int id) const
{
    for (auto p : m_products) {
        if (p->getId() == id) return true;
    }
    return false;
}

int ProductManager::getNextProductId() const
{
    int maxId = 0;
    for (auto p : m_products) {
        if (p->getId() > maxId) maxId = p->getId();
    }
    return maxId + 1;
}

bool ProductManager::loadProducts()
{
    QFile file(m_productFilePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        qWarning() << "Cannot open product file:" << m_productFilePath;
        return false;
    }
    QTextStream in(&file);
    while (!in.atEnd()) {
        QString line = in.readLine().trimmed();
        if (line.isEmpty()) continue;
        QStringList parts = line.split(",");
        // 至少需要 7 个字段： id,name,price,stock,category,owner,discount
        if (parts.size() < 7) continue;

        int id       = parts[0].toInt();
        QString name = parts[1];
        double price = parts[2].toDouble();
        int stock    = parts[3].toInt();
        int category = parts[4].toInt();
        QString owner= parts[5];
        double disc  = parts[6].toDouble();

        Product* p = nullptr;
        if (category == Product::BookCategory) {
            if (parts.size() < 8) continue;
            QString author = parts[7];
            p = new Book(id, name, price, stock, owner, disc, author);
        }
        else if (category == Product::ClothingCategory) {
            if (parts.size() < 9) continue;
            QString size  = parts[7];
            QString color = parts[8];
            p = new Clothing(id, name, price, stock, owner, disc, size, color);
        }
        else if (category == Product::FoodCategory) {
            // Food 无额外字段
            p = new Food(id, name, price, stock, owner, disc);
        }
        if (p) {
            m_products.append(p);
        }
    }
    file.close();
    return true;
}

bool ProductManager::saveProducts()
{
    QFile file(m_productFilePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Truncate | QIODevice::Text)) {
        qWarning() << "Cannot write to product file:" << m_productFilePath;
        return false;
    }
    QTextStream out(&file);
    for (auto p : m_products) {
        out << p->toCsvString() << "\n";
    }
    file.close();
    return true;
}

bool ProductManager::addProduct(Product::Category cat,
                                int id, const QString& name, double price, int stock,
                                const QString& owner, double discount,
                                const QString& extra1, const QString& extra2)
{
    if (idExists(id)) return false;

    Product* p = nullptr;
    if (cat == Product::BookCategory) {
        // extra1 = author
        p = new Book(id, name, price, stock, owner, discount, extra1);
    }
    else if (cat == Product::ClothingCategory) {
        // extra1 = size, extra2 = color
        p = new Clothing(id, name, price, stock, owner, discount, extra1, extra2);
    }
    else if (cat == Product::FoodCategory) {
        p = new Food(id, name, price, stock, owner, discount);
    }

    if (p) {
        m_products.append(p);
        return saveProducts();
    }
    return false;
}

Product* ProductManager::getProductById(int id)
{
    for (auto p : m_products) {
        if (p->getId() == id) return p;
    }
    return nullptr;
}

QVector<Product*> ProductManager::getAllProducts() const
{
    return m_products;
}

QVector<Product*> ProductManager::searchByName(const QString& name) const
{
    QVector<Product*> results;
    for (auto p : m_products) {
        if (p->getName().contains(name, Qt::CaseInsensitive))
            results.append(p);
    }
    return results;
}

bool ProductManager::updateProductStock(int id, int newStock)
{
    Product* p = getProductById(id);
    if (!p) return false;
    p->setStock(newStock);
    return saveProducts();
}

bool ProductManager::updateProductPrice(int id, double newPrice)
{
    Product* p = getProductById(id);
    if (!p) return false;
    p->setPrice(newPrice);
    return saveProducts();
}

bool ProductManager::updateProductDiscount(int id, double newDiscount)
{
    Product* p = getProductById(id);
    if (!p) return false;
    p->setDiscount(newDiscount);
    return saveProducts();
}
