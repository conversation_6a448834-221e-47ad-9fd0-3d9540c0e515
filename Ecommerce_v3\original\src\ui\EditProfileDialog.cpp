#include "EditProfileDialog.h"
#include "ui_EditProfileDialog.h"
#include <QFileDialog>
#include <QMessageBox>
#include "UserManager.h"

EditProfileDialog::EditProfileDialog(User* user, UserManager* userMgr, QWidget *parent) :
    QDialog(parent),
    ui(new Ui::EditProfileDialog),
    m_user(user),
    m_userMgr(userMgr),
    m_newAvatarPath(user->getAvatarPath())
{
    ui->setupUi(this);
    setWindowTitle("编辑个人信息");

    // 初始化各个字段
    ui->usernameLineEdit->setText(m_user->getUsername());
    ui->oldPwdLineEdit->setPlaceholderText("修改密码请填写旧密码");
    ui->passwordLineEdit->setPlaceholderText("不修改请留空");
    ui->signatureTextEdit->setPlainText(m_user->getSignature());

    // 显示当前头像
    QString avatarPath = m_user->getAvatarPath();
    if (avatarPath.isEmpty() || !QFile::exists(avatarPath)) {
        avatarPath = ":/default_avatar.png";
    }
    ui->avatarLabel->setPixmap(
        QPixmap(avatarPath).scaled(64,64, Qt::KeepAspectRatio, Qt::SmoothTransformation));
}

EditProfileDialog::~EditProfileDialog()
{
    delete ui;
}

void EditProfileDialog::on_changeAvatarButton_clicked()
{
    QString filePath = QFileDialog::getOpenFileName(
        this, "选择头像", "", "Images (*.png *.jpg *.bmp)");
    if (filePath.isEmpty()) return;
    m_newAvatarPath = filePath;
    ui->avatarLabel->setPixmap(
        QPixmap(m_newAvatarPath).scaled(64,64, Qt::KeepAspectRatio, Qt::SmoothTransformation));
}

void EditProfileDialog::on_saveButton_clicked()
{
    QString newUsername  = ui->usernameLineEdit->text().trimmed();
    QString oldPwd       = ui->oldPwdLineEdit->text();
    QString newPwd       = ui->passwordLineEdit->text();
    QString newSignature = ui->signatureTextEdit->toPlainText();

    // 校验用户名
    if (newUsername.isEmpty()) {
        QMessageBox::warning(this, "错误", "用户名不能为空。");
        return;
    }
    // 如果用户名改变，检查重复
    if (newUsername != m_user->getUsername() &&
        m_userMgr->loginUser(newUsername, "") == nullptr && // 不能直接 loginUser("")，改为判断 usernameExists
        m_userMgr->getUserByName(newUsername) != nullptr) {
        // 更好的做法：UserManager 增加 usernameExists()
        QMessageBox::warning(this, "错误", "用户名已存在。");
        return;
    }

    // 修改用户名
    if (newUsername != m_user->getUsername()) {
        m_user->setUsername(newUsername);
    }

    // 修改密码（如填写）
    if (!newPwd.isEmpty()) {
        if (!m_user->checkPassword(oldPwd)) {
            QMessageBox::warning(this, "错误", "旧密码不正确。");
            return;
        }
        m_user->setPassword(newPwd);
    }

    // 修改签名
    if (newSignature != m_user->getSignature()) {
        m_user->setSignature(newSignature);
    }

    // 修改头像
    if (m_newAvatarPath != m_user->getAvatarPath()) {
        m_user->setAvatarPath(m_newAvatarPath);
    }

    // 最后调用 UserManager::saveUsers() 将所有用户写回文件
    if (!m_userMgr->saveUsers()) {
        QMessageBox::warning(this, "错误", "保存用户文件失败。");
        return;
    }

    accept();
}

void EditProfileDialog::on_cancelButton_clicked()
{
    reject();
}
