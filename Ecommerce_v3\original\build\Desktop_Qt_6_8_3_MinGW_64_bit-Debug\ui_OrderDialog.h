/********************************************************************************
** Form generated from reading UI file 'OrderDialog.ui'
**
** Created by: Qt User Interface Compiler version 6.8.3
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_ORDERDIALOG_H
#define UI_ORDERDIALOG_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QDialog>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QVBoxLayout>

QT_BEGIN_NAMESPACE

class Ui_OrderDialog
{
public:
    QVBoxLayout *verticalLayout_order;
    QTableWidget *orderTable;
    QHBoxLayout *horizontalLayout_total;
    QSpacerItem *horizontalSpacer_totalLeft;
    QLabel *labelTotal;
    QLabel *totalLabel;
    QSpacerItem *horizontalSpacer_totalRight;
    QHBoxLayout *horizontalLayout_buttons;
    QSpacerItem *horizontalSpacer_buttonsLeft;
    QPushButton *payButton;
    QPushButton *cancelButton;
    QSpacerItem *horizontalSpacer_buttonsRight;

    void setupUi(QDialog *OrderDialog)
    {
        if (OrderDialog->objectName().isEmpty())
            OrderDialog->setObjectName("OrderDialog");
        OrderDialog->resize(550, 400);
        verticalLayout_order = new QVBoxLayout(OrderDialog);
        verticalLayout_order->setObjectName("verticalLayout_order");
        orderTable = new QTableWidget(OrderDialog);
        if (orderTable->columnCount() < 6)
            orderTable->setColumnCount(6);
        QTableWidgetItem *__qtablewidgetitem = new QTableWidgetItem();
        orderTable->setHorizontalHeaderItem(0, __qtablewidgetitem);
        QTableWidgetItem *__qtablewidgetitem1 = new QTableWidgetItem();
        orderTable->setHorizontalHeaderItem(1, __qtablewidgetitem1);
        QTableWidgetItem *__qtablewidgetitem2 = new QTableWidgetItem();
        orderTable->setHorizontalHeaderItem(2, __qtablewidgetitem2);
        QTableWidgetItem *__qtablewidgetitem3 = new QTableWidgetItem();
        orderTable->setHorizontalHeaderItem(3, __qtablewidgetitem3);
        QTableWidgetItem *__qtablewidgetitem4 = new QTableWidgetItem();
        orderTable->setHorizontalHeaderItem(4, __qtablewidgetitem4);
        QTableWidgetItem *__qtablewidgetitem5 = new QTableWidgetItem();
        orderTable->setHorizontalHeaderItem(5, __qtablewidgetitem5);
        orderTable->setObjectName("orderTable");
        orderTable->setColumnCount(6);
        orderTable->setRowCount(0);

        verticalLayout_order->addWidget(orderTable);

        horizontalLayout_total = new QHBoxLayout();
        horizontalLayout_total->setSpacing(10);
        horizontalLayout_total->setObjectName("horizontalLayout_total");
        horizontalSpacer_totalLeft = new QSpacerItem(0, 0, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_total->addItem(horizontalSpacer_totalLeft);

        labelTotal = new QLabel(OrderDialog);
        labelTotal->setObjectName("labelTotal");

        horizontalLayout_total->addWidget(labelTotal);

        totalLabel = new QLabel(OrderDialog);
        totalLabel->setObjectName("totalLabel");

        horizontalLayout_total->addWidget(totalLabel);

        horizontalSpacer_totalRight = new QSpacerItem(0, 0, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_total->addItem(horizontalSpacer_totalRight);


        verticalLayout_order->addLayout(horizontalLayout_total);

        horizontalLayout_buttons = new QHBoxLayout();
        horizontalLayout_buttons->setObjectName("horizontalLayout_buttons");
        horizontalSpacer_buttonsLeft = new QSpacerItem(0, 0, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_buttons->addItem(horizontalSpacer_buttonsLeft);

        payButton = new QPushButton(OrderDialog);
        payButton->setObjectName("payButton");
        payButton->setMinimumSize(QSize(80, 30));

        horizontalLayout_buttons->addWidget(payButton);

        cancelButton = new QPushButton(OrderDialog);
        cancelButton->setObjectName("cancelButton");
        cancelButton->setMinimumSize(QSize(80, 30));

        horizontalLayout_buttons->addWidget(cancelButton);

        horizontalSpacer_buttonsRight = new QSpacerItem(0, 0, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_buttons->addItem(horizontalSpacer_buttonsRight);


        verticalLayout_order->addLayout(horizontalLayout_buttons);


        retranslateUi(OrderDialog);

        QMetaObject::connectSlotsByName(OrderDialog);
    } // setupUi

    void retranslateUi(QDialog *OrderDialog)
    {
        OrderDialog->setWindowTitle(QCoreApplication::translate("OrderDialog", "\350\256\242\345\215\225\347\241\256\350\256\244", nullptr));
        QTableWidgetItem *___qtablewidgetitem = orderTable->horizontalHeaderItem(0);
        ___qtablewidgetitem->setText(QCoreApplication::translate("OrderDialog", "\345\220\215\347\247\260", nullptr));
        QTableWidgetItem *___qtablewidgetitem1 = orderTable->horizontalHeaderItem(1);
        ___qtablewidgetitem1->setText(QCoreApplication::translate("OrderDialog", "\345\215\225\344\273\267", nullptr));
        QTableWidgetItem *___qtablewidgetitem2 = orderTable->horizontalHeaderItem(2);
        ___qtablewidgetitem2->setText(QCoreApplication::translate("OrderDialog", "\346\212\230\345\220\216\344\273\267", nullptr));
        QTableWidgetItem *___qtablewidgetitem3 = orderTable->horizontalHeaderItem(3);
        ___qtablewidgetitem3->setText(QCoreApplication::translate("OrderDialog", "\346\225\260\351\207\217", nullptr));
        QTableWidgetItem *___qtablewidgetitem4 = orderTable->horizontalHeaderItem(4);
        ___qtablewidgetitem4->setText(QCoreApplication::translate("OrderDialog", "\345\260\217\350\256\241", nullptr));
        QTableWidgetItem *___qtablewidgetitem5 = orderTable->horizontalHeaderItem(5);
        ___qtablewidgetitem5->setText(QCoreApplication::translate("OrderDialog", "\345\225\206\345\256\266", nullptr));
        labelTotal->setText(QCoreApplication::translate("OrderDialog", "\346\200\273\351\207\221\351\242\235: ", nullptr));
        totalLabel->setText(QCoreApplication::translate("OrderDialog", "0.00", nullptr));
        payButton->setText(QCoreApplication::translate("OrderDialog", "\346\224\257\344\273\230", nullptr));
        cancelButton->setText(QCoreApplication::translate("OrderDialog", "\345\217\226\346\266\210", nullptr));
    } // retranslateUi

};

namespace Ui {
    class OrderDialog: public Ui_OrderDialog {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_ORDERDIALOG_H
