#############################################################################
# Makefile for building: EcommerceServer
# Generated by qmake (3.1) (Qt 6.8.3)
# Project:  EcommerceServer.pro
# Template: app
#############################################################################

MAKEFILE      = Makefile.Release

EQ            = =

####### Compiler, tools and options

CC            = gcc
CXX           = g++
DEFINES       = -DUNICODE -D_UNICODE -DWIN32 -DMINGW_HAS_SECURE_API=1 -DQT_NO_DEBUG -DQT_NETWORK_LIB -DQT_CORE_LIB
CFLAGS        = -fno-keep-inline-dllexport -O2 -Wall -Wextra -Wextra $(DEFINES)
CXXFLAGS      = -fno-keep-inline-dllexport -Wall -Wextra -O2 -std=gnu++1z -Wall -Wextra -Wextra -fexceptions -mthreads $(DEFINES)
INCPATH       = -I. -I. -I../common -Isrc/models -Isrc/managers -ID:/software/Qt/6.8.3/mingw_64/include -ID:/software/Qt/6.8.3/mingw_64/include/QtNetwork -ID:/software/Qt/6.8.3/mingw_64/include/QtCore -Ibuild/moc -ID:/software/Qt/6.8.3/mingw_64/mkspecs/win32-g++ 
LINKER      =        g++
LFLAGS        =        -Wl,-s -Wl,-subsystem,console -mthreads
LIBS        =        D:\software\Qt\6.8.3\mingw_64\lib\libQt6Network.a D:\software\Qt\6.8.3\mingw_64\lib\libQt6Core.a   
QMAKE         = D:\software\Qt\6.8.3\mingw_64\bin\qmake.exe
DEL_FILE      = del
CHK_DIR_EXISTS= if not exist
MKDIR         = mkdir
COPY          = copy /y
COPY_FILE     = copy /y
COPY_DIR      = xcopy /s /q /y /i
INSTALL_FILE  = copy /y
INSTALL_PROGRAM = copy /y
INSTALL_DIR   = xcopy /s /q /y /i
QINSTALL      = D:\software\Qt\6.8.3\mingw_64\bin\qmake.exe -install qinstall
QINSTALL_PROGRAM = D:\software\Qt\6.8.3\mingw_64\bin\qmake.exe -install qinstall -exe
DEL_FILE      = del
SYMLINK       = $(QMAKE) -install ln -f -s
DEL_DIR       = rmdir
MOVE          = move
IDC           = idc
IDL           = midl
ZIP           = zip -r -9
DEF_FILE      = 
RES_FILE      = 
SED           = $(QMAKE) -install sed
MOVE          = move

####### Output directory

OBJECTS_DIR   = build\obj

####### Files

SOURCES       = main.cpp \
		network\Server.cpp \
		network\ClientHandler.cpp \
		dispatcher\MessageDispatcher.cpp \
		src\models\User.cpp \
		src\models\Consumer.cpp \
		src\models\Seller.cpp \
		src\models\Product.cpp \
		src\models\Book.cpp \
		src\models\Clothing.cpp \
		src\models\Food.cpp \
		src\models\Order.cpp \
		src\managers\UserManager.cpp \
		src\managers\ProductManager.cpp \
		..\common\Message.cpp build\moc\moc_Server.cpp \
		build\moc\moc_ClientHandler.cpp \
		build\moc\moc_MessageDispatcher.cpp
OBJECTS       = build/obj/main.o \
		build/obj/Server.o \
		build/obj/ClientHandler.o \
		build/obj/MessageDispatcher.o \
		build/obj/User.o \
		build/obj/Consumer.o \
		build/obj/Seller.o \
		build/obj/Product.o \
		build/obj/Book.o \
		build/obj/Clothing.o \
		build/obj/Food.o \
		build/obj/Order.o \
		build/obj/UserManager.o \
		build/obj/ProductManager.o \
		build/obj/Message.o \
		build/obj/moc_Server.o \
		build/obj/moc_ClientHandler.o \
		build/obj/moc_MessageDispatcher.o

DIST          =  network\Server.h \
		network\ClientHandler.h \
		dispatcher\MessageDispatcher.h \
		src\models\User.h \
		src\models\Consumer.h \
		src\models\Seller.h \
		src\models\Product.h \
		src\models\Book.h \
		src\models\Clothing.h \
		src\models\Food.h \
		src\models\Order.h \
		src\managers\UserManager.h \
		src\managers\ProductManager.h \
		..\common\Protocol.h \
		..\common\Message.h \
		..\common\NetworkUtils.h main.cpp \
		network\Server.cpp \
		network\ClientHandler.cpp \
		dispatcher\MessageDispatcher.cpp \
		src\models\User.cpp \
		src\models\Consumer.cpp \
		src\models\Seller.cpp \
		src\models\Product.cpp \
		src\models\Book.cpp \
		src\models\Clothing.cpp \
		src\models\Food.cpp \
		src\models\Order.cpp \
		src\managers\UserManager.cpp \
		src\managers\ProductManager.cpp \
		..\common\Message.cpp
QMAKE_TARGET  = EcommerceServer
DESTDIR        = build\ #avoid trailing-slash linebreak
TARGET         = EcommerceServer.exe
DESTDIR_TARGET = build\EcommerceServer.exe

####### Build rules

first: all
all: Makefile.Release  build/EcommerceServer.exe

build/EcommerceServer.exe: D:/software/Qt/6.8.3/mingw_64/lib/libQt6Network.a D:/software/Qt/6.8.3/mingw_64/lib/libQt6Core.a $(OBJECTS) 
	$(LINKER) $(LFLAGS) -o $(DESTDIR_TARGET) @build\obj\object_script.EcommerceServer.Release $(LIBS)

qmake: FORCE
	@$(QMAKE) -o Makefile.Release EcommerceServer.pro

qmake_all: FORCE

dist:
	$(ZIP) EcommerceServer.zip $(SOURCES) $(DIST) EcommerceServer.pro D:\software\Qt\6.8.3\mingw_64\mkspecs\features\spec_pre.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\device_config.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\common\sanitize.conf D:\software\Qt\6.8.3\mingw_64\mkspecs\common\gcc-base.conf D:\software\Qt\6.8.3\mingw_64\mkspecs\common\g++-base.conf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\win32\windows_vulkan_sdk.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\common\windows-vulkan.conf D:\software\Qt\6.8.3\mingw_64\mkspecs\common\g++-win32.conf D:\software\Qt\6.8.3\mingw_64\mkspecs\common\windows-desktop.conf D:\software\Qt\6.8.3\mingw_64\mkspecs\qconfig.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_ext_freetype.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_ext_libjpeg.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_ext_libpng.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_charts.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_charts_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_chartsqml.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_chartsqml_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_concurrent.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_concurrent_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_core.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_core_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_dbus.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_dbus_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_designer.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_designer_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_designercomponents_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_devicediscovery_support_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_entrypoint_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_example_icons_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_examples_asset_downloader_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_fb_support_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_ffmpegmediapluginimpl_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_freetype_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_gui.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_gui_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_harfbuzz_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_help.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_help_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_httpserver.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_httpserver_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_jpeg_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_labsanimation.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_labsanimation_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_labsfolderlistmodel.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_labsfolderlistmodel_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_labsplatform.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_labsplatform_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_labsqmlmodels.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_labsqmlmodels_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_labssettings.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_labssettings_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_labssharedimage.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_labssharedimage_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_labswavefrontmesh.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_labswavefrontmesh_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_linguist.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_multimedia.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_multimedia_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_multimediaquick_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_multimediatestlibprivate_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_multimediawidgets.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_multimediawidgets_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_network.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_network_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_opengl.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_opengl_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_openglwidgets.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_openglwidgets_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_packetprotocol_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_png_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_printsupport.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_printsupport_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qdoccatch_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qdoccatchconversions_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qdoccatchgenerators_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qml.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qml_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmlassetdownloader.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmlassetdownloader_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmlcompiler.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmlcompiler_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmlcore.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmlcore_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmldebug_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmldom_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmlintegration.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmlintegration_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmllocalstorage.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmllocalstorage_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmlls_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmlmeta.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmlmeta_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmlmodels.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmlmodels_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmlnetwork.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmlnetwork_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmltest.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmltest_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmltoolingsettings_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmltyperegistrar_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmlworkerscript.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmlworkerscript_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmlxmllistmodel.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_qmlxmllistmodel_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quick.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quick3dspatialaudio_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quick_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basic.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basic_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basicstyleimpl.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basicstyleimpl_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fluentwinui3styleimpl.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fluentwinui3styleimpl_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusion.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusion_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusionstyleimpl.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusionstyleimpl_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imagine.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imagine_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imaginestyleimpl.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imaginestyleimpl_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2impl.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2impl_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2material.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2material_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2materialstyleimpl.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2materialstyleimpl_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universal.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universal_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universalstyleimpl.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universalstyleimpl_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2windowsstyleimpl.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrols2windowsstyleimpl_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickcontrolstestutilsprivate_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickdialogs2.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickdialogs2_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickdialogs2utils.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickdialogs2utils_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickeffects_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quicklayouts.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quicklayouts_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickparticles_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickshapes_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quicktemplates2.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quicktemplates2_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quicktestutilsprivate_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickvectorimage.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickvectorimage_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickvectorimagegenerator_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickwidgets.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_quickwidgets_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_serialport.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_serialport_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_spatialaudio.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_spatialaudio_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_sql.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_sql_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_svg.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_svg_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_svgwidgets.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_svgwidgets_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_testinternals_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_testlib.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_testlib_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_tools_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_uiplugin.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_uitools.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_uitools_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_websockets.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_websockets_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_widgets.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_widgets_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_xml.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_xml_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\modules\qt_lib_zlib_private.pri D:\software\Qt\6.8.3\mingw_64\mkspecs\features\qt_functions.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\qt_config.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\win32-g++\qmake.conf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\spec_post.prf .qmake.stash D:\software\Qt\6.8.3\mingw_64\mkspecs\features\exclusive_builds.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\toolchain.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\default_pre.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\win32\default_pre.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\resolve_config.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\exclusive_builds_post.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\default_post.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\build_pass.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\win32\console.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\precompile_header.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\warn_on.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\permissions.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\qt.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\resources_functions.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\resources.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\moc.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\qmake_use.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\file_copies.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\testcase_targets.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\exceptions.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\yacc.prf D:\software\Qt\6.8.3\mingw_64\mkspecs\features\lex.prf EcommerceServer.pro D:\software\Qt\6.8.3\mingw_64\lib\Qt6Network.prl D:\software\Qt\6.8.3\mingw_64\lib\Qt6Core.prl    D:\software\Qt\6.8.3\mingw_64\mkspecs\features\data\dummy.cpp network\Server.h network\ClientHandler.h dispatcher\MessageDispatcher.h src\models\User.h src\models\Consumer.h src\models\Seller.h src\models\Product.h src\models\Book.h src\models\Clothing.h src\models\Food.h src\models\Order.h src\managers\UserManager.h src\managers\ProductManager.h ..\common\Protocol.h ..\common\Message.h ..\common\NetworkUtils.h  main.cpp network\Server.cpp network\ClientHandler.cpp dispatcher\MessageDispatcher.cpp src\models\User.cpp src\models\Consumer.cpp src\models\Seller.cpp src\models\Product.cpp src\models\Book.cpp src\models\Clothing.cpp src\models\Food.cpp src\models\Order.cpp src\managers\UserManager.cpp src\managers\ProductManager.cpp ..\common\Message.cpp    

clean: compiler_clean 
	-$(DEL_FILE) build\obj\main.o build\obj\Server.o build\obj\ClientHandler.o build\obj\MessageDispatcher.o build\obj\User.o build\obj\Consumer.o build\obj\Seller.o build\obj\Product.o build\obj\Book.o build\obj\Clothing.o build\obj\Food.o build\obj\Order.o build\obj\UserManager.o build\obj\ProductManager.o build\obj\Message.o build\obj\moc_Server.o build\obj\moc_ClientHandler.o build\obj\moc_MessageDispatcher.o

distclean: clean 
	-$(DEL_FILE) .qmake.stash
	-$(DEL_FILE) $(DESTDIR_TARGET)
	-$(DEL_FILE) Makefile.Release

mocclean: compiler_moc_header_clean compiler_moc_objc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_objc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_no_pch_compiler_make_all:
compiler_no_pch_compiler_clean:
compiler_rcc_make_all:
compiler_rcc_clean:
compiler_moc_predefs_make_all: build/moc/moc_predefs.h
compiler_moc_predefs_clean:
	-$(DEL_FILE) build\moc\moc_predefs.h
build/moc/moc_predefs.h: D:/software/Qt/6.8.3/mingw_64/mkspecs/features/data/dummy.cpp
	g++ -fno-keep-inline-dllexport -Wall -Wextra -O2 -std=gnu++1z -Wall -Wextra -Wextra -dM -E -o build\moc\moc_predefs.h D:\software\Qt\6.8.3\mingw_64\mkspecs\features\data\dummy.cpp

compiler_moc_header_make_all: build/moc/moc_Server.cpp build/moc/moc_ClientHandler.cpp build/moc/moc_MessageDispatcher.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) build\moc\moc_Server.cpp build\moc\moc_ClientHandler.cpp build\moc\moc_MessageDispatcher.cpp
build/moc/moc_Server.cpp: network/Server.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/QTcpServer \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qtcpserver.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qtnetworkglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversionchecks.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconfig.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcore-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcoreexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qprocessordetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsystemdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qassert.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtnoop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypes.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypeinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsysinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlogging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qflags.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasicatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qgenericatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qyieldcpu.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconstructormacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qforeach.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttypetraits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobalstatic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmalloc.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qminmax.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnumeric.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qoverload.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qswap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtresource.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttranslation.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qversiontagging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qtnetwork-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qtnetworkexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnamespace.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20type_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtmetamacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstring.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qchar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrefcount.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpair.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydataops.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qxptype_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20functional.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20memory.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringliteral.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qanystringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qutf8stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringbuilder.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhashfunctions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearraylist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringmatcher.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetatype.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatastream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevicebase.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfloat16.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmath.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetacontainer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopeguard.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbindingstorage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qabstractsocket.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qhostaddress.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdebug.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtextstream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qset.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhash.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/QTcpSocket \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qtcpsocket.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvariant.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q23utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QVector \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvector.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QMap \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QTimer \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasictimer.h \
		build/moc/moc_predefs.h \
		D:/software/Qt/6.8.3/mingw_64/bin/moc.exe
	D:\software\Qt\6.8.3\mingw_64\bin\moc.exe $(DEFINES) --include D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/build/moc/moc_predefs.h -ID:/software/Qt/6.8.3/mingw_64/mkspecs/win32-g++ -ID:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server -ID:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server -ID:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/common -ID:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/src/models -ID:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/src/managers -ID:/software/Qt/6.8.3/mingw_64/include -ID:/software/Qt/6.8.3/mingw_64/include/QtNetwork -ID:/software/Qt/6.8.3/mingw_64/include/QtCore -I"D:/VS Code/mingw/include/c++/13.2.0" -I"D:/VS Code/mingw/include/c++/13.2.0/x86_64-w64-mingw32" -I"D:/VS Code/mingw/include/c++/13.2.0/backward" -I"D:/VS Code/mingw/lib/gcc/x86_64-w64-mingw32/13.2.0/include" -I"D:/VS Code/mingw/lib/gcc/x86_64-w64-mingw32/13.2.0/include-fixed" -I"D:/VS Code/mingw/x86_64-w64-mingw32/include" network\Server.h -o build\moc\moc_Server.cpp

build/moc/moc_ClientHandler.cpp: network/ClientHandler.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnamespace.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversionchecks.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconfig.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcore-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcoreexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qprocessordetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsystemdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qassert.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtnoop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypes.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypeinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsysinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlogging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qflags.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasicatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qgenericatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qyieldcpu.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconstructormacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qforeach.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttypetraits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobalstatic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmalloc.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qminmax.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnumeric.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qoverload.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qswap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtresource.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttranslation.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qversiontagging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20type_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtmetamacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstring.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qchar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrefcount.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpair.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydataops.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qxptype_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20functional.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20memory.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringliteral.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qanystringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qutf8stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringbuilder.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhashfunctions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearraylist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringmatcher.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetatype.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatastream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevicebase.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfloat16.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmath.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetacontainer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopeguard.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbindingstorage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/QTcpSocket \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qtcpsocket.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qtnetworkglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qtnetwork-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qtnetworkexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qabstractsocket.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qhostaddress.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdebug.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtextstream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qset.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhash.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvariant.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q23utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QTimer \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasictimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QByteArray \
		build/moc/moc_predefs.h \
		D:/software/Qt/6.8.3/mingw_64/bin/moc.exe
	D:\software\Qt\6.8.3\mingw_64\bin\moc.exe $(DEFINES) --include D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/build/moc/moc_predefs.h -ID:/software/Qt/6.8.3/mingw_64/mkspecs/win32-g++ -ID:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server -ID:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server -ID:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/common -ID:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/src/models -ID:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/src/managers -ID:/software/Qt/6.8.3/mingw_64/include -ID:/software/Qt/6.8.3/mingw_64/include/QtNetwork -ID:/software/Qt/6.8.3/mingw_64/include/QtCore -I"D:/VS Code/mingw/include/c++/13.2.0" -I"D:/VS Code/mingw/include/c++/13.2.0/x86_64-w64-mingw32" -I"D:/VS Code/mingw/include/c++/13.2.0/backward" -I"D:/VS Code/mingw/lib/gcc/x86_64-w64-mingw32/13.2.0/include" -I"D:/VS Code/mingw/lib/gcc/x86_64-w64-mingw32/13.2.0/include-fixed" -I"D:/VS Code/mingw/x86_64-w64-mingw32/include" network\ClientHandler.h -o build\moc\moc_ClientHandler.cpp

build/moc/moc_MessageDispatcher.cpp: dispatcher/MessageDispatcher.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnamespace.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversionchecks.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconfig.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcore-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcoreexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qprocessordetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsystemdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qassert.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtnoop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypes.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypeinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsysinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlogging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qflags.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasicatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qgenericatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qyieldcpu.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconstructormacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qforeach.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttypetraits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobalstatic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmalloc.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qminmax.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnumeric.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qoverload.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qswap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtresource.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttranslation.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qversiontagging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20type_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtmetamacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstring.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qchar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrefcount.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpair.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydataops.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qxptype_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20functional.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20memory.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringliteral.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qanystringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qutf8stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringbuilder.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhashfunctions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearraylist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringmatcher.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetatype.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatastream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevicebase.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfloat16.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmath.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetacontainer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopeguard.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbindingstorage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QString \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborcommon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdebug.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtextstream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qset.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhash.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatetime.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcalendar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlocale.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvariant.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q23utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qregularexpression.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qurl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/quuid.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qendian.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QMap \
		build/moc/moc_predefs.h \
		D:/software/Qt/6.8.3/mingw_64/bin/moc.exe
	D:\software\Qt\6.8.3\mingw_64\bin\moc.exe $(DEFINES) --include D:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/build/moc/moc_predefs.h -ID:/software/Qt/6.8.3/mingw_64/mkspecs/win32-g++ -ID:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server -ID:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server -ID:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/common -ID:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/src/models -ID:/myCode/Qt/E-Commerce-Trading-Platform/Ecommerce_v3/server/src/managers -ID:/software/Qt/6.8.3/mingw_64/include -ID:/software/Qt/6.8.3/mingw_64/include/QtNetwork -ID:/software/Qt/6.8.3/mingw_64/include/QtCore -I"D:/VS Code/mingw/include/c++/13.2.0" -I"D:/VS Code/mingw/include/c++/13.2.0/x86_64-w64-mingw32" -I"D:/VS Code/mingw/include/c++/13.2.0/backward" -I"D:/VS Code/mingw/lib/gcc/x86_64-w64-mingw32/13.2.0/include" -I"D:/VS Code/mingw/lib/gcc/x86_64-w64-mingw32/13.2.0/include-fixed" -I"D:/VS Code/mingw/x86_64-w64-mingw32/include" dispatcher\MessageDispatcher.h -o build\moc\moc_MessageDispatcher.cpp

compiler_moc_objc_header_make_all:
compiler_moc_objc_header_clean:
compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_moc_predefs_clean compiler_moc_header_clean 



####### Compile

build/obj/main.o: main.cpp D:/software/Qt/6.8.3/mingw_64/include/QtCore/QCoreApplication \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreapplication.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversionchecks.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconfig.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcore-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcoreexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qprocessordetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsystemdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qassert.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtnoop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypes.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypeinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsysinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlogging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qflags.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasicatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qgenericatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qyieldcpu.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconstructormacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qforeach.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttypetraits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobalstatic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmalloc.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qminmax.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnumeric.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qoverload.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qswap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtresource.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttranslation.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qversiontagging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstring.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qchar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20type_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrefcount.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnamespace.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtmetamacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpair.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydataops.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qxptype_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20functional.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20memory.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringliteral.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qanystringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qutf8stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringbuilder.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetatype.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatastream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevicebase.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfloat16.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhashfunctions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmath.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetacontainer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopeguard.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qeventloop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearraylist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringmatcher.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbindingstorage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnativeinterface.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdebug.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtextstream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qset.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhash.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreapplication_platform.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfuture.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfutureinterface.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmutex.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtsan_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qresultstore.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfuture_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qthreadpool.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qthread.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrunnable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexception.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpromise.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvariant.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q23utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QDebug \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QDir \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdir.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdirlisting.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfiledevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatetime.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcalendar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlocale.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfile.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfileinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtimezone.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QStandardPaths \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstandardpaths.h \
		network/Server.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/QTcpServer \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qtcpserver.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qtnetworkglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qtnetwork-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qtnetworkexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qabstractsocket.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qhostaddress.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/QTcpSocket \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qtcpsocket.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QVector \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvector.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QMap \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QTimer \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasictimer.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\obj\main.o main.cpp

build/obj/Server.o: network/Server.cpp network/Server.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/QTcpServer \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qtcpserver.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qtnetworkglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversionchecks.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconfig.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcore-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcoreexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qprocessordetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsystemdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qassert.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtnoop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypes.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypeinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsysinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlogging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qflags.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasicatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qgenericatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qyieldcpu.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconstructormacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qforeach.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttypetraits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobalstatic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmalloc.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qminmax.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnumeric.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qoverload.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qswap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtresource.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttranslation.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qversiontagging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qtnetwork-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qtnetworkexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnamespace.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20type_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtmetamacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstring.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qchar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrefcount.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpair.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydataops.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qxptype_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20functional.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20memory.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringliteral.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qanystringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qutf8stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringbuilder.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhashfunctions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearraylist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringmatcher.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetatype.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatastream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevicebase.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfloat16.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmath.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetacontainer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopeguard.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbindingstorage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qabstractsocket.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qhostaddress.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdebug.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtextstream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qset.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhash.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/QTcpSocket \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qtcpsocket.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvariant.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q23utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QVector \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvector.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QMap \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QTimer \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasictimer.h \
		network/ClientHandler.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QByteArray \
		dispatcher/MessageDispatcher.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QString \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborcommon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatetime.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcalendar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlocale.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qregularexpression.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qurl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/quuid.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qendian.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QDebug \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/QHostAddress
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\obj\Server.o network\Server.cpp

build/obj/ClientHandler.o: network/ClientHandler.cpp network/ClientHandler.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnamespace.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversionchecks.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconfig.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcore-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcoreexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qprocessordetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsystemdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qassert.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtnoop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypes.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypeinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsysinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlogging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qflags.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasicatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qgenericatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qyieldcpu.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconstructormacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qforeach.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttypetraits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobalstatic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmalloc.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qminmax.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnumeric.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qoverload.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qswap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtresource.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttranslation.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qversiontagging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20type_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtmetamacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstring.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qchar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrefcount.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpair.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydataops.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qxptype_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20functional.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20memory.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringliteral.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qanystringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qutf8stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringbuilder.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhashfunctions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearraylist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringmatcher.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetatype.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatastream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevicebase.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfloat16.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmath.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetacontainer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopeguard.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbindingstorage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/QTcpSocket \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qtcpsocket.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qtnetworkglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qtnetwork-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qtnetworkexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qabstractsocket.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qhostaddress.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdebug.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtextstream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qset.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhash.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvariant.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q23utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QTimer \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasictimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QByteArray \
		dispatcher/MessageDispatcher.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QString \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborcommon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatetime.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcalendar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlocale.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qregularexpression.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qurl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/quuid.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qendian.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QMap \
		../common/Message.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonDocument \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsondocument.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonArray \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonarray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QDebug \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/QHostAddress
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\obj\ClientHandler.o network\ClientHandler.cpp

build/obj/MessageDispatcher.o: dispatcher/MessageDispatcher.cpp dispatcher/MessageDispatcher.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnamespace.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversionchecks.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconfig.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcore-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcoreexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qprocessordetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsystemdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qassert.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtnoop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypes.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypeinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsysinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlogging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qflags.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasicatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qgenericatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qyieldcpu.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconstructormacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qforeach.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttypetraits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobalstatic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmalloc.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qminmax.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnumeric.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qoverload.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qswap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtresource.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttranslation.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qversiontagging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20type_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtmetamacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstring.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qchar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrefcount.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpair.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydataops.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qxptype_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20functional.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20memory.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringliteral.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qanystringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qutf8stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringbuilder.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhashfunctions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearraylist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringmatcher.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetatype.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatastream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevicebase.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfloat16.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmath.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetacontainer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopeguard.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbindingstorage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QString \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborcommon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdebug.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtextstream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qset.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhash.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatetime.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcalendar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlocale.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvariant.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q23utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qregularexpression.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qurl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/quuid.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qendian.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QMap \
		../common/Message.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonDocument \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsondocument.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonArray \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonarray.h \
		../common/Protocol.h \
		network/ClientHandler.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/QTcpSocket \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qtcpsocket.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qtnetworkglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qtnetwork-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qtnetworkexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qabstractsocket.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtNetwork/qhostaddress.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QTimer \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasictimer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QByteArray \
		src/managers/UserManager.h \
		src/models/User.h \
		src/models/Seller.h \
		src/models/Consumer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QVector \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvector.h \
		src/managers/ProductManager.h \
		src/models/Product.h \
		src/models/Book.h \
		src/models/Clothing.h \
		src/models/Food.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QDebug
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\obj\MessageDispatcher.o dispatcher\MessageDispatcher.cpp

build/obj/User.o: src/models/User.cpp src/models/User.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QString \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstring.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qchar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversionchecks.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconfig.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcore-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcoreexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qprocessordetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsystemdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qassert.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtnoop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypes.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypeinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsysinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlogging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qflags.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasicatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qgenericatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qyieldcpu.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconstructormacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qforeach.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttypetraits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobalstatic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmalloc.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qminmax.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnumeric.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qoverload.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qswap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtresource.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttranslation.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qversiontagging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20type_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrefcount.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnamespace.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtmetamacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpair.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydataops.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qxptype_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20functional.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20memory.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringliteral.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qanystringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qutf8stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringbuilder.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QCryptographicHash \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcryptographichash.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qspan.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20iterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QStringList \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhashfunctions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearraylist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringmatcher.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\obj\User.o src\models\User.cpp

build/obj/Consumer.o: src/models/Consumer.cpp src/models/Consumer.h \
		src/models/User.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QString \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstring.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qchar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversionchecks.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconfig.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcore-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcoreexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qprocessordetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsystemdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qassert.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtnoop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypes.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypeinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsysinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlogging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qflags.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasicatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qgenericatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qyieldcpu.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconstructormacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qforeach.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttypetraits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobalstatic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmalloc.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qminmax.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnumeric.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qoverload.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qswap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtresource.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttranslation.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qversiontagging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20type_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrefcount.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnamespace.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtmetamacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpair.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydataops.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qxptype_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20functional.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20memory.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringliteral.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qanystringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qutf8stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringbuilder.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter_base.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\obj\Consumer.o src\models\Consumer.cpp

build/obj/Seller.o: src/models/Seller.cpp src/models/Seller.h \
		src/models/User.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QString \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstring.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qchar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversionchecks.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconfig.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcore-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcoreexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qprocessordetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsystemdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qassert.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtnoop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypes.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypeinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsysinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlogging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qflags.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasicatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qgenericatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qyieldcpu.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconstructormacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qforeach.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttypetraits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobalstatic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmalloc.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qminmax.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnumeric.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qoverload.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qswap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtresource.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttranslation.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qversiontagging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20type_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrefcount.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnamespace.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtmetamacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpair.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydataops.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qxptype_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20functional.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20memory.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringliteral.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qanystringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qutf8stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringbuilder.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter_base.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\obj\Seller.o src\models\Seller.cpp

build/obj/Product.o: src/models/Product.cpp src/models/Product.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QString \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstring.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qchar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversionchecks.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconfig.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcore-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcoreexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qprocessordetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsystemdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qassert.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtnoop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypes.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypeinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsysinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlogging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qflags.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasicatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qgenericatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qyieldcpu.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconstructormacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qforeach.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttypetraits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobalstatic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmalloc.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qminmax.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnumeric.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qoverload.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qswap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtresource.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttranslation.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qversiontagging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20type_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrefcount.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnamespace.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtmetamacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpair.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydataops.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qxptype_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20functional.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20memory.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringliteral.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qanystringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qutf8stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringbuilder.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QStringList \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhashfunctions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearraylist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringmatcher.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\obj\Product.o src\models\Product.cpp

build/obj/Book.o: src/models/Book.cpp src/models/Book.h \
		src/models/Product.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QString \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstring.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qchar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversionchecks.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconfig.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcore-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcoreexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qprocessordetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsystemdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qassert.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtnoop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypes.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypeinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsysinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlogging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qflags.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasicatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qgenericatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qyieldcpu.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconstructormacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qforeach.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttypetraits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobalstatic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmalloc.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qminmax.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnumeric.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qoverload.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qswap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtresource.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttranslation.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qversiontagging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20type_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrefcount.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnamespace.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtmetamacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpair.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydataops.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qxptype_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20functional.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20memory.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringliteral.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qanystringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qutf8stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringbuilder.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QStringList \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhashfunctions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearraylist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringmatcher.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\obj\Book.o src\models\Book.cpp

build/obj/Clothing.o: src/models/Clothing.cpp src/models/Clothing.h \
		src/models/Product.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QString \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstring.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qchar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversionchecks.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconfig.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcore-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcoreexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qprocessordetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsystemdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qassert.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtnoop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypes.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypeinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsysinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlogging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qflags.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasicatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qgenericatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qyieldcpu.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconstructormacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qforeach.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttypetraits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobalstatic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmalloc.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qminmax.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnumeric.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qoverload.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qswap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtresource.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttranslation.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qversiontagging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20type_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrefcount.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnamespace.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtmetamacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpair.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydataops.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qxptype_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20functional.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20memory.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringliteral.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qanystringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qutf8stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringbuilder.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QStringList \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhashfunctions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearraylist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringmatcher.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\obj\Clothing.o src\models\Clothing.cpp

build/obj/Food.o: src/models/Food.cpp src/models/Food.h \
		src/models/Product.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QString \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstring.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qchar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversionchecks.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconfig.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcore-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcoreexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qprocessordetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsystemdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qassert.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtnoop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypes.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypeinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsysinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlogging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qflags.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasicatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qgenericatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qyieldcpu.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconstructormacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qforeach.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttypetraits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobalstatic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmalloc.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qminmax.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnumeric.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qoverload.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qswap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtresource.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttranslation.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qversiontagging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20type_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrefcount.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnamespace.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtmetamacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpair.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydataops.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qxptype_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20functional.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20memory.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringliteral.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qanystringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qutf8stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringbuilder.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QStringList \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhashfunctions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearraylist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringmatcher.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\obj\Food.o src\models\Food.cpp

build/obj/Order.o: src/models/Order.cpp src/models/Order.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QString \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstring.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qchar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversionchecks.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconfig.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcore-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcoreexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qprocessordetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsystemdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qassert.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtnoop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypes.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypeinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsysinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlogging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qflags.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasicatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qgenericatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qyieldcpu.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconstructormacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qforeach.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttypetraits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobalstatic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmalloc.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qminmax.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnumeric.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qoverload.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qswap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtresource.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttranslation.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qversiontagging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20type_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrefcount.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnamespace.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtmetamacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpair.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydataops.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qxptype_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20functional.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20memory.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringliteral.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qanystringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qutf8stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringbuilder.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QVector \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvector.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhashfunctions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearraylist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringmatcher.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QStringList
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\obj\Order.o src\models\Order.cpp

build/obj/UserManager.o: src/managers/UserManager.cpp src/managers/UserManager.h \
		src/models/User.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QString \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstring.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qchar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversionchecks.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconfig.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcore-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcoreexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qprocessordetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsystemdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qassert.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtnoop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypes.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypeinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsysinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlogging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qflags.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasicatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qgenericatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qyieldcpu.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconstructormacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qforeach.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttypetraits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobalstatic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmalloc.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qminmax.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnumeric.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qoverload.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qswap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtresource.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttranslation.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qversiontagging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20type_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrefcount.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnamespace.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtmetamacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpair.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydataops.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qxptype_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20functional.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20memory.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringliteral.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qanystringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qutf8stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringbuilder.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter_base.h \
		src/models/Seller.h \
		src/models/Consumer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QVector \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvector.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhashfunctions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearraylist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringmatcher.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QFile \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfile.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfiledevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevicebase.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetatype.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatastream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfloat16.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmath.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetacontainer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopeguard.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbindingstorage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QTextStream \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtextstream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QDebug \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdebug.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qset.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhash.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvarlengtharray.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\obj\UserManager.o src\managers\UserManager.cpp

build/obj/ProductManager.o: src/managers/ProductManager.cpp src/managers/ProductManager.h \
		src/models/Product.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QString \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstring.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qchar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversionchecks.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconfig.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcore-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcoreexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qprocessordetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsystemdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qassert.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtnoop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypes.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypeinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsysinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlogging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qflags.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasicatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qgenericatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qyieldcpu.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconstructormacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qforeach.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttypetraits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobalstatic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmalloc.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qminmax.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnumeric.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qoverload.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qswap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtresource.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttranslation.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qversiontagging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20type_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrefcount.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnamespace.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtmetamacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpair.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydataops.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qxptype_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20functional.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20memory.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringliteral.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qanystringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qutf8stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringbuilder.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QVector \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvector.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhashfunctions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearraylist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringmatcher.h \
		src/models/Book.h \
		src/models/Clothing.h \
		src/models/Food.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QFile \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfile.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfiledevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevice.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevicebase.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetatype.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatastream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfloat16.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmath.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetacontainer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopeguard.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbindingstorage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QTextStream \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtextstream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QDebug \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdebug.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qset.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhash.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvarlengtharray.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\obj\ProductManager.o src\managers\ProductManager.cpp

build/obj/Message.o: ../common/Message.cpp ../common/Message.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QString \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstring.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qchar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobal.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversionchecks.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconfig.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcore-config.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtcoreexports.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qprocessordetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsystemdetection.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qassert.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtnoop.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypes.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtversion.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtypeinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsysinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlogging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qflags.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbasicatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qgenericatomic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qyieldcpu.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qconstructormacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qforeach.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttypetraits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qglobalstatic.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmalloc.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qminmax.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnumeric.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qoverload.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qswap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtresource.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qttranslation.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qversiontagging.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcompare.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20type_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qrefcount.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qnamespace.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtmetamacros.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qpair.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qarraydataops.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qxptype_traits.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20functional.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20memory.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearrayview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringfwd.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringliteral.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qanystringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qutf8stringview.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringbuilder.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonObject \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborvalue.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcborcommon.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetatype.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatastream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiodevicebase.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qfloat16.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhashfunctions.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmath.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qalgorithms.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterable.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmetacontainer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qscopeguard.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdebug.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qtextstream.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qiterator.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbytearraylist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringlist.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qstringmatcher.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qmap.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qset.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qhash.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qdatetime.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcalendar.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qlocale.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qvariant.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q23utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/q20utility.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qcoreevent.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qobject_impl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qbindingstorage.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qregularexpression.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qurl.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/quuid.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qendian.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonDocument \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsondocument.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonArray \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/qjsonarray.h \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QJsonParseError \
		D:/software/Qt/6.8.3/mingw_64/include/QtCore/QDebug
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\obj\Message.o ..\common\Message.cpp

build/obj/moc_Server.o: build/moc/moc_Server.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\obj\moc_Server.o build\moc\moc_Server.cpp

build/obj/moc_ClientHandler.o: build/moc/moc_ClientHandler.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\obj\moc_ClientHandler.o build\moc\moc_ClientHandler.cpp

build/obj/moc_MessageDispatcher.o: build/moc/moc_MessageDispatcher.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\obj\moc_MessageDispatcher.o build\moc\moc_MessageDispatcher.cpp

####### Install

install:  FORCE

uninstall:  FORCE

FORCE:

.SUFFIXES:

