#ifndef SERVER_H
#define SERVER_H

#include <QTcpServer>
#include <QTcpSocket>
#include <QVector>
#include <QMap>
#include <QTimer>

class ClientHandler;
class UserManager;
class ProductManager;
class MessageDispatcher;

class Server : public QTcpServer
{
    Q_OBJECT

public:
    explicit Server(QObject* parent = nullptr);
    ~Server();
    
    // 启动服务器
    bool startServer(quint16 port = 8888);
    
    // 停止服务器
    void stopServer();
    
    // 获取连接的客户端数量
    int getClientCount() const { return m_clients.size(); }
    
    // 设置管理器
    void setUserManager(UserManager* userMgr) { m_userManager = userMgr; }
    void setProductManager(ProductManager* prodMgr) { m_productManager = prodMgr; }

protected:
    void incomingConnection(qintptr socketDescriptor) override;

private slots:
    void onClientDisconnected();
    void onCleanupTimer();

private:
    void setupManagers();
    void cleanupDisconnectedClients();

private:
    QVector<ClientHandler*> m_clients;
    QMap<QTcpSocket*, ClientHandler*> m_socketToClient;
    
    UserManager* m_userManager;
    ProductManager* m_productManager;
    MessageDispatcher* m_dispatcher;
    
    QTimer* m_cleanupTimer;
    quint16 m_port;
    bool m_isRunning;
};

#endif // SERVER_H
