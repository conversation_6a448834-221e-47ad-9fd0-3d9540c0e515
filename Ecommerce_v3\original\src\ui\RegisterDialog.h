#ifndef REGISTERDIALOG_H
#define REGISTERDIALOG_H

#include <QDialog>
#include "UserManager.h"

namespace Ui {
class RegisterDialog;
}

class RegisterDialog : public QDialog
{
    Q_OBJECT

public:
    explicit RegisterDialog(UserManager* userMgr, QWidget *parent = nullptr);
    ~RegisterDialog();

private slots:
    void on_registerButton_clicked();

private:
    Ui::RegisterDialog *ui;
    UserManager* m_userMgr;
};

#endif // REGISTERDIALOG_H
