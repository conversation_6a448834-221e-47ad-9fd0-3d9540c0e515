#include "StyleManager.h"

// 预定义颜色
const QString StyleManager::PRIMARY_COLOR = "#4A90E2";
const QString StyleManager::SUCCESS_COLOR = "#5CB85C";
const QString StyleManager::WARNING_COLOR = "#F0AD4E";
const QString StyleManager::DANGER_COLOR = "#D9534F";
const QString StyleManager::INFO_COLOR = "#5BC0DE";

StyleManager& StyleManager::instance()
{
    static StyleManager instance;
    return instance;
}

QString StyleManager::getTableStyle()
{
    return R"(
QTableWidget {
    background-color: rgba(255, 255, 255, 0.95);
    border: 1px solid #E0E0E0;
    border-radius: 6px;
    gridline-color: #F0F0F0;
    selection-background-color: rgba(74, 144, 226, 0.3);
    font-size: 10pt;
}

QTableWidget::item {
    padding: 8px;
    border-bottom: 1px solid #F0F0F0;
}

QTableWidget::item:selected {
    background-color: rgba(74, 144, 226, 0.2);
    color: #333;
}

QTableWidget::item:hover {
    background-color: rgba(74, 144, 226, 0.1);
}

QHeaderView::section {
    background-color: #F8F9FA;
    color: #495057;
    padding: 10px;
    border: none;
    border-bottom: 2px solid #E9ECEF;
    font-weight: bold;
    font-size: 10pt;
}

QHeaderView::section:hover {
    background-color: #E9ECEF;
}
)";
}

QString StyleManager::getButtonStyle(const QString& color)
{
    return QString(R"(
QPushButton {
    background-color: %1;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: bold;
    font-size: 10pt;
    min-width: 80px;
}

QPushButton:hover {
    background-color: %2;
}

QPushButton:pressed {
    background-color: %3;
}

QPushButton:disabled {
    background-color: #CCCCCC;
    color: #666666;
}
)").arg(color)
   .arg(adjustColor(color, -20))
   .arg(adjustColor(color, -40));
}

QString StyleManager::getTabWidgetStyle()
{
    return R"(
QTabWidget::pane {
    border: 2px solid #E0E0E0;
    border-radius: 8px;
    background-color: rgba(255, 255, 255, 0.9);
    margin-top: -1px;
}

QTabBar::tab {
    background-color: rgba(248, 249, 250, 0.9);
    border: 1px solid #E0E0E0;
    border-bottom: none;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
    padding: 10px 20px;
    margin-right: 2px;
    font-weight: bold;
    font-size: 11pt;
    min-width: 80px;
}

QTabBar::tab:selected {
    background-color: rgba(255, 255, 255, 0.95);
    border-color: #4A90E2;
    color: #4A90E2;
    border-bottom: 2px solid #4A90E2;
}

QTabBar::tab:hover:!selected {
    background-color: rgba(250, 250, 250, 0.9);
}
)";
}

QString StyleManager::getFormStyle()
{
    return R"(
QLabel {
    color: #495057;
    font-size: 11pt;
}

QLineEdit {
    border: 2px solid #E9ECEF;
    border-radius: 4px;
    padding: 8px;
    font-size: 10pt;
    background-color: white;
}

QLineEdit:focus {
    border-color: #4A90E2;
    outline: none;
}

QComboBox {
    border: 2px solid #E9ECEF;
    border-radius: 4px;
    padding: 8px;
    font-size: 10pt;
    background-color: white;
}

QComboBox:focus {
    border-color: #4A90E2;
}

QSpinBox {
    border: 2px solid #E9ECEF;
    border-radius: 4px;
    padding: 4px;
    font-size: 10pt;
    background-color: white;
}

QSpinBox:focus {
    border-color: #4A90E2;
}
)";
}

QString StyleManager::getMainWindowStyle()
{
    return R"(
QMainWindow {
    background-color: #F8F9FA;
}

QMenuBar {
    background-color: #343A40;
    color: white;
    border: none;
    font-size: 10pt;
}

QMenuBar::item {
    background-color: transparent;
    padding: 8px 16px;
}

QMenuBar::item:selected {
    background-color: #495057;
}

QStatusBar {
    background-color: #E9ECEF;
    color: #495057;
    border-top: 1px solid #DEE2E6;
    font-size: 9pt;
}
)";
}

QString StyleManager::adjustColor(const QString& color, int adjustment)
{
    // 简单的颜色调整函数
    if (color == PRIMARY_COLOR) {
        if (adjustment < 0) return adjustment < -30 ? "#2E5F8A" : "#357ABD";
    } else if (color == SUCCESS_COLOR) {
        if (adjustment < 0) return adjustment < -30 ? "#357A35" : "#449D44";
    } else if (color == WARNING_COLOR) {
        if (adjustment < 0) return adjustment < -30 ? "#D58512" : "#EC971F";
    } else if (color == DANGER_COLOR) {
        if (adjustment < 0) return adjustment < -30 ? "#AC2925" : "#C9302C";
    } else if (color == INFO_COLOR) {
        if (adjustment < 0) return adjustment < -30 ? "#269ABC" : "#31B0D5";
    }
    return color;
}
