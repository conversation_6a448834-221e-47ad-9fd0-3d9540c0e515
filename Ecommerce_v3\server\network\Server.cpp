#include "Server.h"
#include "ClientHandler.h"
#include "../dispatcher/MessageDispatcher.h"
#include <QDebug>
#include <QHostAddress>

Server::Server(QObject* parent)
    : QTcpServer(parent)
    , m_user<PERSON>anager(nullptr)
    , m_product<PERSON>anager(nullptr)
    , m_dispatcher(nullptr)
    , m_cleanupTimer(new QTimer(this))
    , m_port(8888)
    , m_isRunning(false)
{
    // 设置清理定时器，每30秒清理一次断开的连接
    m_cleanupTimer->setInterval(30000);
    connect(m_cleanupTimer, &QTimer::timeout, this, &Server::onCleanupTimer);
}

Server::~Server()
{
    stopServer();
    delete m_dispatcher;
}

bool Server::startServer(quint16 port)
{
    if (m_isRunning) {
        qWarning() << "Server is already running on port" << m_port;
        return false;
    }
    
    setupManagers();
    
    if (!listen(QHostAddress::Any, port)) {
        qCritical() << "Failed to start server on port" << port << ":" << errorString();
        return false;
    }
    
    m_port = port;
    m_isRunning = true;
    m_cleanupTimer->start();
    
    qInfo() << "Server started successfully on port" << port;
    qInfo() << "Server address:" << serverAddress().toString();
    
    return true;
}

void Server::stopServer()
{
    if (!m_isRunning) {
        return;
    }
    
    qInfo() << "Stopping server...";
    
    m_cleanupTimer->stop();
    
    // 断开所有客户端连接
    for (ClientHandler* client : m_clients) {
        client->disconnectFromHost();
        client->deleteLater();
    }
    m_clients.clear();
    m_socketToClient.clear();
    
    close();
    m_isRunning = false;
    
    qInfo() << "Server stopped.";
}

void Server::incomingConnection(qintptr socketDescriptor)
{
    qInfo() << "New client connection, socket descriptor:" << socketDescriptor;
    
    ClientHandler* client = new ClientHandler(socketDescriptor, m_dispatcher, this);

    connect(client, &ClientHandler::disconnected, this, &Server::onClientDisconnected);
    connect(client, &ClientHandler::clientDisconnecting, m_dispatcher, &MessageDispatcher::cleanupClientData);
    
    m_clients.append(client);
    m_socketToClient[client->getSocket()] = client;
    
    qInfo() << "Client connected. Total clients:" << m_clients.size();
}

void Server::onClientDisconnected()
{
    ClientHandler* client = qobject_cast<ClientHandler*>(sender());
    if (!client) {
        return;
    }
    
    qInfo() << "Client disconnected:" << client->getClientInfo();
    
    m_clients.removeAll(client);
    m_socketToClient.remove(client->getSocket());
    
    client->deleteLater();
    
    qInfo() << "Client removed. Total clients:" << m_clients.size();
}

void Server::onCleanupTimer()
{
    cleanupDisconnectedClients();
}

void Server::setupManagers()
{
    if (!m_dispatcher) {
        m_dispatcher = new MessageDispatcher(this);
        m_dispatcher->setUserManager(m_userManager);
        m_dispatcher->setProductManager(m_productManager);
    }
}

void Server::cleanupDisconnectedClients()
{
    QVector<ClientHandler*> toRemove;
    
    for (ClientHandler* client : m_clients) {
        if (!client->isConnected()) {
            toRemove.append(client);
        }
    }
    
    for (ClientHandler* client : toRemove) {
        qInfo() << "Cleaning up disconnected client:" << client->getClientInfo();
        m_clients.removeAll(client);
        m_socketToClient.remove(client->getSocket());
        client->deleteLater();
    }
    
    if (!toRemove.isEmpty()) {
        qInfo() << "Cleaned up" << toRemove.size() << "disconnected clients. Total clients:" << m_clients.size();
    }
}
