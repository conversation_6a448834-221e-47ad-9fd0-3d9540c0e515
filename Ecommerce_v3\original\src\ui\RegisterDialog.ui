<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>RegisterDialog</class>
 <widget class="QDialog" name="RegisterDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>450</width>
    <height>260</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>用户注册</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="margin">
    <number>12</number>
   </property>
   <property name="spacing">
    <number>8</number>
   </property>
   <!-- 用户名 -->
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_username">
     <property name="spacing">
      <number>6</number>
     </property>
     <item>
      <widget class="QLabel" name="labelUsername">
       <property name="text">
        <string>用户名：</string>
       </property>
       <property name="minimumSize">
        <size>
         <width>80</width>
         <height>0</height>
        </size>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="usernameLineEdit">
       <property name="placeholderText">
        <string>2-16位字符</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <!-- 密码 -->
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_password">
     <property name="spacing">
      <number>6</number>
     </property>
     <item>
      <widget class="QLabel" name="labelPassword">
       <property name="text">
        <string>密码：</string>
       </property>
       <property name="minimumSize">
        <size>
         <width>80</width>
         <height>0</height>
        </size>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="passwordLineEdit">
       <property name="echoMode">
        <enum>QLineEdit::Password</enum>
       </property>
       <property name="placeholderText">
        <string>6-20位字符</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <!-- 确认密码 -->
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_confirm">
     <property name="spacing">
      <number>6</number>
     </property>
     <item>
      <widget class="QLabel" name="labelConfirm">
       <property name="text">
        <string>确认密码：</string>
       </property>
       <property name="minimumSize">
        <size>
         <width>80</width>
         <height>0</height>
        </size>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="confirmLineEdit">
       <property name="echoMode">
        <enum>QLineEdit::Password</enum>
       </property>
       <property name="placeholderText">
        <string>再次输入密码</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <!-- 用户类型 -->
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_userType">
     <property name="spacing">
      <number>6</number>
     </property>
     <item>
      <widget class="QLabel" name="labelUserType">
       <property name="text">
        <string>用户类型：</string>
       </property>
       <property name="minimumSize">
        <size>
         <width>80</width>
         <height>0</height>
        </size>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="userTypeCombo"/>
     </item>
    </layout>
   </item>
   <!-- 注册按钮 -->
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_button">
     <property name="spacing">
      <number>12</number>
     </property>
     <item>
      <spacer name="horizontalSpacer_left">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeType">
        <enum>QSizePolicy::Expanding</enum>
       </property>
       <property name="sizeHint">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="registerButton">
       <property name="minimumSize">
        <size>
         <width>100</width>
         <height>32</height>
        </size>
       </property>
       <property name="text">
        <string>立即注册</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_right">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeType">
        <enum>QSizePolicy::Expanding</enum>
       </property>
       <property name="sizeHint">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
