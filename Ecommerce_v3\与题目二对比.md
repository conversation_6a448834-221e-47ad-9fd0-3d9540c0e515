# 单机版代码复用情况

## 整体框架

层次 | 单机版 | 网络版服务器 | 网络版客户端 | 复用率
---|---|---|---|---
数据模型 | 有 | 复用 | 无 | 95%
业务逻辑 | 有 | 复用 | 新 | 85%
UI界面 | 有 | 无 | 新 | 70%
网络层 | 无 | 新 | 新 | 0%

数据模型(Models)修改：图片路径
业务逻辑(Managers)修改：图片路径
UI界面：直接修改本地文件改为网络请求


## 转换步骤

**目录结构：**
```
    原始结构:
    Ecommerce_v2/
    ├── src/
    │   ├── main.cpp
    │   ├── models/
    │   ├── managers/
    │   └── ui/

    转换为:
    Ecommerce_v3/
    ├── common/          # 新增：共享协议和工具
    ├── server/          # 新增：服务器端
    │   ├── main.cpp     # 新的服务器主程序
    │   ├── src/         # 复用：models + managers
    │   ├── network/     # 新增：网络层
    │   └── dispatcher/  # 新增：消息分发
    └── client/          # 新增：客户端
        ├── main.cpp     # 新的客户端主程序
        ├── ui/          # 改造：原UI + 网络管理
        └── network/     # 新增：客户端网络层
```

**网络层开发：**
1. 服务器端：
    - Server.h/cpp：监听端口，接受客户端连接，为每个客户端创建一个ClientHandler
    - ClientHandler.h/cpp：处理与单个客户端的通信，接收请求，调用MessageDispatcher分发
        ```cpp
        // server/network/Server.h
        class Server : public QTcpServer {
            // TCP服务器实现
        };

        // server/network/ClientHandler.h
        class ClientHandler : public QObject {
            // 客户端连接处理
        };
        ```
    - MessageDispatcher.h/cpp：根据请求类型调用相应的Manager处理
        ```cpp
        // server/dispatcher/MessageDispatcher.h
        class MessageDispatcher : public QObject {
            Response handleLogin(const Message& msg, ClientHandler* client);
            Response handleRegister(const Message& msg, ClientHandler* client);
            Response handleGetAllProducts(const Message& msg, ClientHandler* client);
            // ... 更多处理方法
        };
        ```
    - 业务逻辑
2. 客户端：
    - Client.h/cpp：与服务器建立连接，发送请求，接收响应
    - ClientManager.h/cpp：管理客户端状态，提供简单的API给UI层
3. 共享协议和工具：
    - Protocol.h：定义请求和响应的类型和格式
        ```cpp
        // common/Protocol.h
        namespace Protocol {
            namespace Request {
                const QString LOGIN = "LOGIN";
                const QString REGISTER = "REGISTER";
                const QString GET_ALL_PRODUCTS = "GET_ALL_PRODUCTS";
                // ... 更多协议
            }
        }
        ```
    - Message.h/cpp：定义消息的结构和序列化/反序列化方法
        ```cpp
        // common/Message.h
        class Message {
            QString m_action;
            QJsonObject m_data;
            // 序列化和反序列化方法
        };
        ```
    - NetworkUtils.h/cpp：定义网络相关的工具函数

**UI界面：**
1. 移除Manager依赖
```cpp
// 原始MainWindow.h
class MainWindow : public QMainWindow {
    UserManager* m_userMgr;      // 移除
    ProductManager* m_prodMgr;   // 移除
};

// 网络版MainWindow.h
class MainWindow : public QMainWindow {
    ClientManager* m_clientMgr;  // 新增：网络管理器
};
```
2. 开发客户端网络管理器
```cpp
// client/network/ClientManager.h
class ClientManager : public QObject {
    bool login(const QString& username, const QString& password, QString& errorMsg);
    bool register(const QString& username, const QString& password, User::UserType type, QString& errorMsg);
    QVector<QJsonObject> getAllProducts(QString& errorMsg);
    // ... 更多网络方法
};
```
3. 改造UI事件处理
```cpp
// 原始版本 - 直接操作
void MainWindow::on_addToCartButton_clicked() {
    // 直接操作本地购物车
    m_cart.append(cartItem);
}

// 网络版本 - 网络请求
void MainWindow::on_addToCartButton_clicked() {
    QString errorMsg;
    if (!m_clientMgr->addToCart(productId, quantity, errorMsg)) {
        QMessageBox::warning(this, "错误", errorMsg);
    }
}
```
**主程序：**
1. 服务器
```cpp
// server/main.cpp
int main(int argc, char *argv[]) {
    QCoreApplication app(argc, argv);  // 无GUI
    
    // 初始化管理器
    UserManager userManager(userFilePath);
    ProductManager::init(productFilePath);
    
    // 创建并启动服务器
    Server server;
    server.setUserManager(&userManager);
    server.setProductManager(ProductManager::instance());
    server.startServer(8888);
    
    return app.exec();
}
```
2. 客户端
```cpp
// client/main.cpp
int main(int argc, char *argv[]) {
    QApplication app(argc, argv);  // 保持GUI
    
    // 创建网络管理器
    ClientManager clientMgr;
    if (!clientMgr.connectToServer("localhost", 8888)) {
        QMessageBox::critical(nullptr, "错误", "无法连接到服务器");
        return -1;
    }
    
    // 登录对话框
    LoginDialog loginDlg(&clientMgr);
    if (loginDlg.exec() != QDialog::Accepted) {
        return 0;
    }
    
    // 主窗口
    MainWindow w(&clientMgr);
    w.show();
    
    return app.exec();
}
```

