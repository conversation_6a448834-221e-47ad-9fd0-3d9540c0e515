/********************************************************************************
** Form generated from reading UI file 'MainWindow.ui'
**
** Created by: Qt User Interface Compiler version 6.8.3
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MAINWINDOW_H
#define UI_MAINWINDOW_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QMenuBar>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QStatusBar>
#include <QtWidgets/QTabWidget>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_MainWindow
{
public:
    QWidget *centralwidget;
    QVBoxLayout *verticalLayout_main;
    QTabWidget *tabs;
    QWidget *homeTab;
    QVBoxLayout *verticalLayout_home;
    QHBoxLayout *horizontalLayout_filter;
    QPushButton *filterButton;
    QSpacerItem *horizontalSpacer_filterRight;
    QHBoxLayout *horizontalLayout_searchHome;
    QLineEdit *homeSearchLineEdit;
    QPushButton *searchHomeButton;
    QPushButton *refreshHomeButton;
    QSpacerItem *horizontalSpacer_searchHomeRight;
    QTableWidget *homeProductsTable;
    QWidget *cartTab;
    QVBoxLayout *verticalLayout_cart;
    QTableWidget *cartTable;
    QHBoxLayout *horizontalLayout_cartButtons;
    QSpacerItem *horizontalSpacer_cartLeft;
    QPushButton *generateOrderButton;
    QPushButton *removeCartButton;
    QSpacerItem *horizontalSpacer_cartRight;
    QWidget *productTab;
    QVBoxLayout *verticalLayout_product;
    QHBoxLayout *horizontalLayout_productButtons;
    QPushButton *addProductButton;
    QPushButton *refreshProductButton;
    QPushButton *updateProductButton;
    QPushButton *setDiscountButton;
    QTableWidget *productProductsTable;
    QWidget *profileTab;
    QVBoxLayout *verticalLayout_profile;
    QHBoxLayout *horizontalLayout_avatarView;
    QLabel *labelAvatarStatic;
    QLabel *avatarLabel;
    QSpacerItem *horizontalSpacer_avatarRight;
    QHBoxLayout *horizontalLayout_username;
    QLabel *labelUsernameStatic;
    QLabel *usernameLabel;
    QSpacerItem *horizontalSpacer_usernameRight;
    QHBoxLayout *horizontalLayout_balance;
    QLabel *labelBalanceStatic;
    QLabel *balanceLabel;
    QSpacerItem *horizontalSpacer_balanceRight;
    QHBoxLayout *horizontalLayout_signature;
    QLabel *labelSignatureStatic;
    QLabel *signatureLabel;
    QSpacerItem *horizontalSpacer_sigRight;
    QHBoxLayout *horizontalLayout_profileButtons;
    QSpacerItem *horizontalSpacer_profileLeft;
    QPushButton *rechargeButton;
    QSpacerItem *horizontalSpacer_betweenButtons1;
    QPushButton *editProfileButton;
    QSpacerItem *horizontalSpacer_betweenButtons2;
    QPushButton *logoutButton;
    QSpacerItem *horizontalSpacer_profileRight;
    QMenuBar *menubar;
    QStatusBar *statusbar;

    void setupUi(QMainWindow *MainWindow)
    {
        if (MainWindow->objectName().isEmpty())
            MainWindow->setObjectName("MainWindow");
        MainWindow->resize(900, 650);
        centralwidget = new QWidget(MainWindow);
        centralwidget->setObjectName("centralwidget");
        verticalLayout_main = new QVBoxLayout(centralwidget);
        verticalLayout_main->setObjectName("verticalLayout_main");
        tabs = new QTabWidget(centralwidget);
        tabs->setObjectName("tabs");
        homeTab = new QWidget();
        homeTab->setObjectName("homeTab");
        verticalLayout_home = new QVBoxLayout(homeTab);
        verticalLayout_home->setSpacing(10);
        verticalLayout_home->setContentsMargins(10, 10, 10, 10);
        verticalLayout_home->setObjectName("verticalLayout_home");
        horizontalLayout_filter = new QHBoxLayout();
        horizontalLayout_filter->setSpacing(8);
        horizontalLayout_filter->setObjectName("horizontalLayout_filter");
        filterButton = new QPushButton(homeTab);
        filterButton->setObjectName("filterButton");
        filterButton->setMinimumSize(QSize(120, 32));

        horizontalLayout_filter->addWidget(filterButton);

        horizontalSpacer_filterRight = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_filter->addItem(horizontalSpacer_filterRight);


        verticalLayout_home->addLayout(horizontalLayout_filter);

        horizontalLayout_searchHome = new QHBoxLayout();
        horizontalLayout_searchHome->setSpacing(8);
        horizontalLayout_searchHome->setObjectName("horizontalLayout_searchHome");
        homeSearchLineEdit = new QLineEdit(homeTab);
        homeSearchLineEdit->setObjectName("homeSearchLineEdit");

        horizontalLayout_searchHome->addWidget(homeSearchLineEdit);

        searchHomeButton = new QPushButton(homeTab);
        searchHomeButton->setObjectName("searchHomeButton");
        searchHomeButton->setMinimumSize(QSize(80, 28));

        horizontalLayout_searchHome->addWidget(searchHomeButton);

        refreshHomeButton = new QPushButton(homeTab);
        refreshHomeButton->setObjectName("refreshHomeButton");
        refreshHomeButton->setMinimumSize(QSize(80, 28));

        horizontalLayout_searchHome->addWidget(refreshHomeButton);

        horizontalSpacer_searchHomeRight = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_searchHome->addItem(horizontalSpacer_searchHomeRight);


        verticalLayout_home->addLayout(horizontalLayout_searchHome);

        homeProductsTable = new QTableWidget(homeTab);
        if (homeProductsTable->columnCount() < 9)
            homeProductsTable->setColumnCount(9);
        QTableWidgetItem *__qtablewidgetitem = new QTableWidgetItem();
        homeProductsTable->setHorizontalHeaderItem(0, __qtablewidgetitem);
        QTableWidgetItem *__qtablewidgetitem1 = new QTableWidgetItem();
        homeProductsTable->setHorizontalHeaderItem(1, __qtablewidgetitem1);
        QTableWidgetItem *__qtablewidgetitem2 = new QTableWidgetItem();
        homeProductsTable->setHorizontalHeaderItem(2, __qtablewidgetitem2);
        QTableWidgetItem *__qtablewidgetitem3 = new QTableWidgetItem();
        homeProductsTable->setHorizontalHeaderItem(3, __qtablewidgetitem3);
        QTableWidgetItem *__qtablewidgetitem4 = new QTableWidgetItem();
        homeProductsTable->setHorizontalHeaderItem(4, __qtablewidgetitem4);
        QTableWidgetItem *__qtablewidgetitem5 = new QTableWidgetItem();
        homeProductsTable->setHorizontalHeaderItem(5, __qtablewidgetitem5);
        QTableWidgetItem *__qtablewidgetitem6 = new QTableWidgetItem();
        homeProductsTable->setHorizontalHeaderItem(6, __qtablewidgetitem6);
        QTableWidgetItem *__qtablewidgetitem7 = new QTableWidgetItem();
        homeProductsTable->setHorizontalHeaderItem(7, __qtablewidgetitem7);
        QTableWidgetItem *__qtablewidgetitem8 = new QTableWidgetItem();
        homeProductsTable->setHorizontalHeaderItem(8, __qtablewidgetitem8);
        homeProductsTable->setObjectName("homeProductsTable");
        homeProductsTable->setColumnCount(9);
        homeProductsTable->setRowCount(0);

        verticalLayout_home->addWidget(homeProductsTable);

        tabs->addTab(homeTab, QString());
        cartTab = new QWidget();
        cartTab->setObjectName("cartTab");
        verticalLayout_cart = new QVBoxLayout(cartTab);
        verticalLayout_cart->setSpacing(10);
        verticalLayout_cart->setContentsMargins(10, 10, 10, 10);
        verticalLayout_cart->setObjectName("verticalLayout_cart");
        cartTable = new QTableWidget(cartTab);
        if (cartTable->columnCount() < 9)
            cartTable->setColumnCount(9);
        QTableWidgetItem *__qtablewidgetitem9 = new QTableWidgetItem();
        cartTable->setHorizontalHeaderItem(0, __qtablewidgetitem9);
        QTableWidgetItem *__qtablewidgetitem10 = new QTableWidgetItem();
        cartTable->setHorizontalHeaderItem(1, __qtablewidgetitem10);
        QTableWidgetItem *__qtablewidgetitem11 = new QTableWidgetItem();
        cartTable->setHorizontalHeaderItem(2, __qtablewidgetitem11);
        QTableWidgetItem *__qtablewidgetitem12 = new QTableWidgetItem();
        cartTable->setHorizontalHeaderItem(3, __qtablewidgetitem12);
        QTableWidgetItem *__qtablewidgetitem13 = new QTableWidgetItem();
        cartTable->setHorizontalHeaderItem(4, __qtablewidgetitem13);
        QTableWidgetItem *__qtablewidgetitem14 = new QTableWidgetItem();
        cartTable->setHorizontalHeaderItem(5, __qtablewidgetitem14);
        QTableWidgetItem *__qtablewidgetitem15 = new QTableWidgetItem();
        cartTable->setHorizontalHeaderItem(6, __qtablewidgetitem15);
        QTableWidgetItem *__qtablewidgetitem16 = new QTableWidgetItem();
        cartTable->setHorizontalHeaderItem(7, __qtablewidgetitem16);
        QTableWidgetItem *__qtablewidgetitem17 = new QTableWidgetItem();
        cartTable->setHorizontalHeaderItem(8, __qtablewidgetitem17);
        cartTable->setObjectName("cartTable");
        cartTable->setColumnCount(9);
        cartTable->setRowCount(0);

        verticalLayout_cart->addWidget(cartTable);

        horizontalLayout_cartButtons = new QHBoxLayout();
        horizontalLayout_cartButtons->setSpacing(12);
        horizontalLayout_cartButtons->setObjectName("horizontalLayout_cartButtons");
        horizontalSpacer_cartLeft = new QSpacerItem(0, 0, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_cartButtons->addItem(horizontalSpacer_cartLeft);

        generateOrderButton = new QPushButton(cartTab);
        generateOrderButton->setObjectName("generateOrderButton");
        generateOrderButton->setMinimumSize(QSize(100, 32));

        horizontalLayout_cartButtons->addWidget(generateOrderButton);

        removeCartButton = new QPushButton(cartTab);
        removeCartButton->setObjectName("removeCartButton");
        removeCartButton->setMinimumSize(QSize(100, 32));

        horizontalLayout_cartButtons->addWidget(removeCartButton);

        horizontalSpacer_cartRight = new QSpacerItem(0, 0, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_cartButtons->addItem(horizontalSpacer_cartRight);


        verticalLayout_cart->addLayout(horizontalLayout_cartButtons);

        tabs->addTab(cartTab, QString());
        productTab = new QWidget();
        productTab->setObjectName("productTab");
        verticalLayout_product = new QVBoxLayout(productTab);
        verticalLayout_product->setSpacing(10);
        verticalLayout_product->setContentsMargins(10, 10, 10, 10);
        verticalLayout_product->setObjectName("verticalLayout_product");
        horizontalLayout_productButtons = new QHBoxLayout();
        horizontalLayout_productButtons->setSpacing(12);
        horizontalLayout_productButtons->setObjectName("horizontalLayout_productButtons");
        addProductButton = new QPushButton(productTab);
        addProductButton->setObjectName("addProductButton");
        addProductButton->setMinimumSize(QSize(100, 32));

        horizontalLayout_productButtons->addWidget(addProductButton);

        refreshProductButton = new QPushButton(productTab);
        refreshProductButton->setObjectName("refreshProductButton");
        refreshProductButton->setMinimumSize(QSize(100, 32));

        horizontalLayout_productButtons->addWidget(refreshProductButton);

        updateProductButton = new QPushButton(productTab);
        updateProductButton->setObjectName("updateProductButton");
        updateProductButton->setMinimumSize(QSize(100, 32));

        horizontalLayout_productButtons->addWidget(updateProductButton);

        setDiscountButton = new QPushButton(productTab);
        setDiscountButton->setObjectName("setDiscountButton");
        setDiscountButton->setMinimumSize(QSize(100, 32));

        horizontalLayout_productButtons->addWidget(setDiscountButton);


        verticalLayout_product->addLayout(horizontalLayout_productButtons);

        productProductsTable = new QTableWidget(productTab);
        if (productProductsTable->columnCount() < 8)
            productProductsTable->setColumnCount(8);
        QTableWidgetItem *__qtablewidgetitem18 = new QTableWidgetItem();
        productProductsTable->setHorizontalHeaderItem(0, __qtablewidgetitem18);
        QTableWidgetItem *__qtablewidgetitem19 = new QTableWidgetItem();
        productProductsTable->setHorizontalHeaderItem(1, __qtablewidgetitem19);
        QTableWidgetItem *__qtablewidgetitem20 = new QTableWidgetItem();
        productProductsTable->setHorizontalHeaderItem(2, __qtablewidgetitem20);
        QTableWidgetItem *__qtablewidgetitem21 = new QTableWidgetItem();
        productProductsTable->setHorizontalHeaderItem(3, __qtablewidgetitem21);
        QTableWidgetItem *__qtablewidgetitem22 = new QTableWidgetItem();
        productProductsTable->setHorizontalHeaderItem(4, __qtablewidgetitem22);
        QTableWidgetItem *__qtablewidgetitem23 = new QTableWidgetItem();
        productProductsTable->setHorizontalHeaderItem(5, __qtablewidgetitem23);
        QTableWidgetItem *__qtablewidgetitem24 = new QTableWidgetItem();
        productProductsTable->setHorizontalHeaderItem(6, __qtablewidgetitem24);
        QTableWidgetItem *__qtablewidgetitem25 = new QTableWidgetItem();
        productProductsTable->setHorizontalHeaderItem(7, __qtablewidgetitem25);
        productProductsTable->setObjectName("productProductsTable");
        productProductsTable->setColumnCount(8);
        productProductsTable->setRowCount(0);

        verticalLayout_product->addWidget(productProductsTable);

        tabs->addTab(productTab, QString());
        profileTab = new QWidget();
        profileTab->setObjectName("profileTab");
        verticalLayout_profile = new QVBoxLayout(profileTab);
        verticalLayout_profile->setObjectName("verticalLayout_profile");
        horizontalLayout_avatarView = new QHBoxLayout();
        horizontalLayout_avatarView->setObjectName("horizontalLayout_avatarView");
        labelAvatarStatic = new QLabel(profileTab);
        labelAvatarStatic->setObjectName("labelAvatarStatic");
        labelAvatarStatic->setMinimumSize(QSize(60, 0));

        horizontalLayout_avatarView->addWidget(labelAvatarStatic);

        avatarLabel = new QLabel(profileTab);
        avatarLabel->setObjectName("avatarLabel");
        avatarLabel->setMinimumSize(QSize(128, 128));
        avatarLabel->setMaximumSize(QSize(128, 128));
        avatarLabel->setScaledContents(true);
        avatarLabel->setPixmap(QPixmap(QString::fromUtf8(":/default_avatar.png")));

        horizontalLayout_avatarView->addWidget(avatarLabel);

        horizontalSpacer_avatarRight = new QSpacerItem(0, 0, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_avatarView->addItem(horizontalSpacer_avatarRight);


        verticalLayout_profile->addLayout(horizontalLayout_avatarView);

        horizontalLayout_username = new QHBoxLayout();
        horizontalLayout_username->setObjectName("horizontalLayout_username");
        labelUsernameStatic = new QLabel(profileTab);
        labelUsernameStatic->setObjectName("labelUsernameStatic");
        labelUsernameStatic->setMinimumSize(QSize(80, 0));

        horizontalLayout_username->addWidget(labelUsernameStatic);

        usernameLabel = new QLabel(profileTab);
        usernameLabel->setObjectName("usernameLabel");
        usernameLabel->setMinimumSize(QSize(100, 0));
        usernameLabel->setMaximumSize(QSize(100, 50));

        horizontalLayout_username->addWidget(usernameLabel);

        horizontalSpacer_usernameRight = new QSpacerItem(0, 0, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_username->addItem(horizontalSpacer_usernameRight);


        verticalLayout_profile->addLayout(horizontalLayout_username);

        horizontalLayout_balance = new QHBoxLayout();
        horizontalLayout_balance->setObjectName("horizontalLayout_balance");
        labelBalanceStatic = new QLabel(profileTab);
        labelBalanceStatic->setObjectName("labelBalanceStatic");
        labelBalanceStatic->setMinimumSize(QSize(80, 0));

        horizontalLayout_balance->addWidget(labelBalanceStatic);

        balanceLabel = new QLabel(profileTab);
        balanceLabel->setObjectName("balanceLabel");
        balanceLabel->setMinimumSize(QSize(100, 0));
        balanceLabel->setMaximumSize(QSize(100, 50));

        horizontalLayout_balance->addWidget(balanceLabel);

        horizontalSpacer_balanceRight = new QSpacerItem(0, 0, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_balance->addItem(horizontalSpacer_balanceRight);


        verticalLayout_profile->addLayout(horizontalLayout_balance);

        horizontalLayout_signature = new QHBoxLayout();
        horizontalLayout_signature->setObjectName("horizontalLayout_signature");
        labelSignatureStatic = new QLabel(profileTab);
        labelSignatureStatic->setObjectName("labelSignatureStatic");
        labelSignatureStatic->setMinimumSize(QSize(80, 0));

        horizontalLayout_signature->addWidget(labelSignatureStatic);

        signatureLabel = new QLabel(profileTab);
        signatureLabel->setObjectName("signatureLabel");
        signatureLabel->setWordWrap(true);
        signatureLabel->setMinimumSize(QSize(300, 100));

        horizontalLayout_signature->addWidget(signatureLabel);

        horizontalSpacer_sigRight = new QSpacerItem(0, 0, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_signature->addItem(horizontalSpacer_sigRight);


        verticalLayout_profile->addLayout(horizontalLayout_signature);

        horizontalLayout_profileButtons = new QHBoxLayout();
        horizontalLayout_profileButtons->setObjectName("horizontalLayout_profileButtons");
        horizontalSpacer_profileLeft = new QSpacerItem(0, 0, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_profileButtons->addItem(horizontalSpacer_profileLeft);

        rechargeButton = new QPushButton(profileTab);
        rechargeButton->setObjectName("rechargeButton");
        rechargeButton->setMinimumSize(QSize(120, 35));

        horizontalLayout_profileButtons->addWidget(rechargeButton);

        horizontalSpacer_betweenButtons1 = new QSpacerItem(15, 0, QSizePolicy::Policy::Fixed, QSizePolicy::Policy::Minimum);

        horizontalLayout_profileButtons->addItem(horizontalSpacer_betweenButtons1);

        editProfileButton = new QPushButton(profileTab);
        editProfileButton->setObjectName("editProfileButton");
        editProfileButton->setMinimumSize(QSize(150, 35));

        horizontalLayout_profileButtons->addWidget(editProfileButton);

        horizontalSpacer_betweenButtons2 = new QSpacerItem(15, 0, QSizePolicy::Policy::Fixed, QSizePolicy::Policy::Minimum);

        horizontalLayout_profileButtons->addItem(horizontalSpacer_betweenButtons2);

        logoutButton = new QPushButton(profileTab);
        logoutButton->setObjectName("logoutButton");
        logoutButton->setMinimumSize(QSize(120, 35));

        horizontalLayout_profileButtons->addWidget(logoutButton);

        horizontalSpacer_profileRight = new QSpacerItem(0, 0, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_profileButtons->addItem(horizontalSpacer_profileRight);


        verticalLayout_profile->addLayout(horizontalLayout_profileButtons);

        tabs->addTab(profileTab, QString());

        verticalLayout_main->addWidget(tabs);

        MainWindow->setCentralWidget(centralwidget);
        menubar = new QMenuBar(MainWindow);
        menubar->setObjectName("menubar");
        MainWindow->setMenuBar(menubar);
        statusbar = new QStatusBar(MainWindow);
        statusbar->setObjectName("statusbar");
        MainWindow->setStatusBar(statusbar);

        retranslateUi(MainWindow);

        QMetaObject::connectSlotsByName(MainWindow);
    } // setupUi

    void retranslateUi(QMainWindow *MainWindow)
    {
        MainWindow->setWindowTitle(QCoreApplication::translate("MainWindow", "\347\224\265\345\225\206\344\272\244\346\230\223\345\271\263\345\217\260", nullptr));
        filterButton->setText(QCoreApplication::translate("MainWindow", "\347\255\233\351\200\211\345\225\206\345\223\201\347\261\273\345\236\213", nullptr));
        homeSearchLineEdit->setPlaceholderText(QCoreApplication::translate("MainWindow", "\350\257\267\350\276\223\345\205\245\345\225\206\345\223\201\345\220\215\347\247\260\345\205\263\351\224\256\345\255\227", nullptr));
        searchHomeButton->setText(QCoreApplication::translate("MainWindow", "\346\220\234\347\264\242", nullptr));
        refreshHomeButton->setText(QCoreApplication::translate("MainWindow", "\345\210\267\346\226\260", nullptr));
        QTableWidgetItem *___qtablewidgetitem = homeProductsTable->horizontalHeaderItem(0);
        ___qtablewidgetitem->setText(QCoreApplication::translate("MainWindow", "\346\223\215\344\275\234", nullptr));
        QTableWidgetItem *___qtablewidgetitem1 = homeProductsTable->horizontalHeaderItem(1);
        ___qtablewidgetitem1->setText(QCoreApplication::translate("MainWindow", "ID", nullptr));
        QTableWidgetItem *___qtablewidgetitem2 = homeProductsTable->horizontalHeaderItem(2);
        ___qtablewidgetitem2->setText(QCoreApplication::translate("MainWindow", "\345\220\215\347\247\260", nullptr));
        QTableWidgetItem *___qtablewidgetitem3 = homeProductsTable->horizontalHeaderItem(3);
        ___qtablewidgetitem3->setText(QCoreApplication::translate("MainWindow", "\347\261\273\345\210\253", nullptr));
        QTableWidgetItem *___qtablewidgetitem4 = homeProductsTable->horizontalHeaderItem(4);
        ___qtablewidgetitem4->setText(QCoreApplication::translate("MainWindow", "\345\216\237\344\273\267", nullptr));
        QTableWidgetItem *___qtablewidgetitem5 = homeProductsTable->horizontalHeaderItem(5);
        ___qtablewidgetitem5->setText(QCoreApplication::translate("MainWindow", "\346\212\230\345\220\216\344\273\267", nullptr));
        QTableWidgetItem *___qtablewidgetitem6 = homeProductsTable->horizontalHeaderItem(6);
        ___qtablewidgetitem6->setText(QCoreApplication::translate("MainWindow", "\345\272\223\345\255\230", nullptr));
        QTableWidgetItem *___qtablewidgetitem7 = homeProductsTable->horizontalHeaderItem(7);
        ___qtablewidgetitem7->setText(QCoreApplication::translate("MainWindow", "\345\225\206\345\256\266", nullptr));
        QTableWidgetItem *___qtablewidgetitem8 = homeProductsTable->horizontalHeaderItem(8);
        ___qtablewidgetitem8->setText(QCoreApplication::translate("MainWindow", "\345\244\207\346\263\250", nullptr));
        tabs->setTabText(tabs->indexOf(homeTab), QCoreApplication::translate("MainWindow", "\344\270\273\351\241\265", nullptr));
        QTableWidgetItem *___qtablewidgetitem9 = cartTable->horizontalHeaderItem(0);
        ___qtablewidgetitem9->setText(QCoreApplication::translate("MainWindow", "\351\200\211\344\270\255", nullptr));
        QTableWidgetItem *___qtablewidgetitem10 = cartTable->horizontalHeaderItem(1);
        ___qtablewidgetitem10->setText(QCoreApplication::translate("MainWindow", "ID", nullptr));
        QTableWidgetItem *___qtablewidgetitem11 = cartTable->horizontalHeaderItem(2);
        ___qtablewidgetitem11->setText(QCoreApplication::translate("MainWindow", "\345\220\215\347\247\260", nullptr));
        QTableWidgetItem *___qtablewidgetitem12 = cartTable->horizontalHeaderItem(3);
        ___qtablewidgetitem12->setText(QCoreApplication::translate("MainWindow", "\347\261\273\345\210\253", nullptr));
        QTableWidgetItem *___qtablewidgetitem13 = cartTable->horizontalHeaderItem(4);
        ___qtablewidgetitem13->setText(QCoreApplication::translate("MainWindow", "\345\216\237\344\273\267", nullptr));
        QTableWidgetItem *___qtablewidgetitem14 = cartTable->horizontalHeaderItem(5);
        ___qtablewidgetitem14->setText(QCoreApplication::translate("MainWindow", "\346\212\230\345\220\216\344\273\267", nullptr));
        QTableWidgetItem *___qtablewidgetitem15 = cartTable->horizontalHeaderItem(6);
        ___qtablewidgetitem15->setText(QCoreApplication::translate("MainWindow", "\350\264\255\344\271\260\346\225\260\351\207\217", nullptr));
        QTableWidgetItem *___qtablewidgetitem16 = cartTable->horizontalHeaderItem(7);
        ___qtablewidgetitem16->setText(QCoreApplication::translate("MainWindow", "\345\225\206\345\256\266", nullptr));
        QTableWidgetItem *___qtablewidgetitem17 = cartTable->horizontalHeaderItem(8);
        ___qtablewidgetitem17->setText(QCoreApplication::translate("MainWindow", "\345\244\207\346\263\250", nullptr));
        generateOrderButton->setText(QCoreApplication::translate("MainWindow", "\347\224\237\346\210\220\350\256\242\345\215\225", nullptr));
        removeCartButton->setText(QCoreApplication::translate("MainWindow", "\345\210\240\351\231\244\350\264\255\347\211\251\350\275\246", nullptr));
        tabs->setTabText(tabs->indexOf(cartTab), QCoreApplication::translate("MainWindow", "\350\264\255\347\211\251\350\275\246", nullptr));
        addProductButton->setText(QCoreApplication::translate("MainWindow", "\346\267\273\345\212\240\345\225\206\345\223\201", nullptr));
        refreshProductButton->setText(QCoreApplication::translate("MainWindow", "\345\210\267\346\226\260\345\210\227\350\241\250", nullptr));
        updateProductButton->setText(QCoreApplication::translate("MainWindow", "\346\233\264\346\226\260\345\225\206\345\223\201", nullptr));
        setDiscountButton->setText(QCoreApplication::translate("MainWindow", "\350\256\276\347\275\256\346\212\230\346\211\243", nullptr));
        QTableWidgetItem *___qtablewidgetitem18 = productProductsTable->horizontalHeaderItem(0);
        ___qtablewidgetitem18->setText(QCoreApplication::translate("MainWindow", "ID", nullptr));
        QTableWidgetItem *___qtablewidgetitem19 = productProductsTable->horizontalHeaderItem(1);
        ___qtablewidgetitem19->setText(QCoreApplication::translate("MainWindow", "\345\220\215\347\247\260", nullptr));
        QTableWidgetItem *___qtablewidgetitem20 = productProductsTable->horizontalHeaderItem(2);
        ___qtablewidgetitem20->setText(QCoreApplication::translate("MainWindow", "\347\261\273\345\210\253", nullptr));
        QTableWidgetItem *___qtablewidgetitem21 = productProductsTable->horizontalHeaderItem(3);
        ___qtablewidgetitem21->setText(QCoreApplication::translate("MainWindow", "\345\216\237\344\273\267", nullptr));
        QTableWidgetItem *___qtablewidgetitem22 = productProductsTable->horizontalHeaderItem(4);
        ___qtablewidgetitem22->setText(QCoreApplication::translate("MainWindow", "\346\212\230\345\220\216\344\273\267", nullptr));
        QTableWidgetItem *___qtablewidgetitem23 = productProductsTable->horizontalHeaderItem(5);
        ___qtablewidgetitem23->setText(QCoreApplication::translate("MainWindow", "\345\272\223\345\255\230", nullptr));
        QTableWidgetItem *___qtablewidgetitem24 = productProductsTable->horizontalHeaderItem(6);
        ___qtablewidgetitem24->setText(QCoreApplication::translate("MainWindow", "\345\225\206\345\256\266", nullptr));
        QTableWidgetItem *___qtablewidgetitem25 = productProductsTable->horizontalHeaderItem(7);
        ___qtablewidgetitem25->setText(QCoreApplication::translate("MainWindow", "\345\244\207\346\263\250", nullptr));
        tabs->setTabText(tabs->indexOf(productTab), QCoreApplication::translate("MainWindow", "\345\225\206\345\223\201\347\256\241\347\220\206", nullptr));
        labelAvatarStatic->setText(QCoreApplication::translate("MainWindow", "\345\244\264\345\203\217\357\274\232", nullptr));
        labelUsernameStatic->setText(QCoreApplication::translate("MainWindow", "\347\224\250\346\210\267\345\220\215\357\274\232", nullptr));
        usernameLabel->setText(QCoreApplication::translate("MainWindow", "---", nullptr));
        labelBalanceStatic->setText(QCoreApplication::translate("MainWindow", "\344\275\231\351\242\235\357\274\232", nullptr));
        balanceLabel->setText(QCoreApplication::translate("MainWindow", "0.00", nullptr));
        labelSignatureStatic->setText(QCoreApplication::translate("MainWindow", "\344\270\252\346\200\247\347\255\276\345\220\215\357\274\232", nullptr));
        signatureLabel->setText(QCoreApplication::translate("MainWindow", "\350\277\231\344\270\252\344\272\272\345\276\210\346\207\222\357\274\214\344\273\200\344\271\210\351\203\275\346\262\241\345\206\231", nullptr));
        rechargeButton->setText(QCoreApplication::translate("MainWindow", "\345\205\205\345\200\274", nullptr));
        editProfileButton->setText(QCoreApplication::translate("MainWindow", "\344\277\256\346\224\271\344\270\252\344\272\272\344\277\241\346\201\257", nullptr));
        logoutButton->setText(QCoreApplication::translate("MainWindow", "\351\200\200\345\207\272\347\231\273\345\275\225", nullptr));
        tabs->setTabText(tabs->indexOf(profileTab), QCoreApplication::translate("MainWindow", "\346\210\221\347\232\204\350\264\246\346\210\267", nullptr));
    } // retranslateUi

};

namespace Ui {
    class MainWindow: public Ui_MainWindow {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MAINWINDOW_H
