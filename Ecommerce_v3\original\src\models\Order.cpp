#include "Order.h"
#include <QStringList>

Order::Order(int orderId, const QString& buyerUsername)
    : m_orderId(orderId),
    m_buyerUsername(buyerUsername),
    m_status(Pending)
{
}

int Order::getOrderId() const
{
    return m_orderId;
}

const QString& Order::getBuyerUsername() const
{
    return m_buyerUsername;
}

Order::Status Order::getStatus() const
{
    return m_status;
}

void Order::setStatus(Order::Status s)
{
    m_status = s;
}

void Order::addItem(int productId, int quantity)
{
    m_items.append(qMakePair(productId, quantity));
}

const QVector<QPair<int,int>>& Order::getItems() const
{
    return m_items;
}

QString Order::toCsvString() const
{
    // Format: orderId,buyerUsername,status,item1Id:item1Qty;item2Id:item2Qty;...
    QStringList itemStrs;
    for (auto& pair : m_items) {
        itemStrs << QString("%1:%2").arg(pair.first).arg(pair.second);
    }
    QStringList parts;
    parts << QString::number(m_orderId)
          << m_buyerUsername
          << QString::number(static_cast<int>(m_status))
          << itemStrs.join(";");
    return parts.join(",");
}
