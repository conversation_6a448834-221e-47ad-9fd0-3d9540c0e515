#include "OrderDialog.h"
#include "ui_OrderDialog.h"
#include <QMessageBox>
#include <QTableWidgetItem>
#include <QCheckBox>
#include <QSpinBox>
#include <QHeaderView>

OrderDialog::OrderDialog(const QVector<CartItem>& cartItems, ClientManager* clientMgr, QWidget *parent) :
    QDialog(parent),
    ui(new Ui::OrderDialog),
    m_clientMgr(clientMgr),
    m_totalAmount(0.0)
{
    ui->setupUi(this);
    setupUI();

    // 获取商品详细信息并创建订单项
    QString errorMsg;
    QVector<ClientProduct> allProducts = m_clientMgr->getAllProducts(errorMsg);

    for (const CartItem& cartItem : cartItems) {
        // 找到对应商品的详细信息
        for (const ClientProduct& product : allProducts) {
            if (product.id == cartItem.productId) {
                OrderItem orderItem;
                orderItem.productId = cartItem.productId;
                orderItem.productName = product.name;
                orderItem.price = cartItem.price;  // 折后价
                orderItem.originalPrice = product.originalPrice;  // 原价
                orderItem.quantity = cartItem.quantity;
                orderItem.maxStock = product.stock;
                orderItem.totalPrice = cartItem.totalPrice;
                orderItem.selected = true;
                orderItem.owner = product.owner;
                m_orderItems.append(orderItem);
                break;
            }
        }
    }

    loadCartItems();
    updateTotalAmount();
}

OrderDialog::~OrderDialog()
{
    delete ui;
}

void OrderDialog::setupUI()
{
    setWindowTitle("订单确认");
    setModal(true);
    resize(700, 400);

    // 设置表格 - 与单机版一致的列结构
    ui->orderTable->setColumnCount(6);
    QStringList headers = {"名称", "单价(原价)", "折后价", "数量", "小计", "商家"};
    ui->orderTable->setHorizontalHeaderLabels(headers);

    // 设置列宽
    ui->orderTable->setColumnWidth(0, 150); // 名称
    ui->orderTable->setColumnWidth(1, 80);  // 单价(原价)
    ui->orderTable->setColumnWidth(2, 80);  // 折后价
    ui->orderTable->setColumnWidth(3, 60);  // 数量
    ui->orderTable->setColumnWidth(4, 80);  // 小计
    ui->orderTable->setColumnWidth(5, 100); // 商家

    ui->orderTable->verticalHeader()->setVisible(false);
    ui->orderTable->setSelectionBehavior(QAbstractItemView::SelectRows);

    // 修改按钮文本
    ui->payOrderButton->setText("支付");
    ui->payOrderButton->setEnabled(true);
}

void OrderDialog::loadCartItems()
{
    ui->orderTable->setRowCount(m_orderItems.size());

    for (int i = 0; i < m_orderItems.size(); ++i) {
        const OrderItem& item = m_orderItems[i];

        // 0. 名称
        ui->orderTable->setItem(i, 0, new QTableWidgetItem(item.productName));

        // 1. 单价（原价）
        ui->orderTable->setItem(i, 1, new QTableWidgetItem(QString::number(item.originalPrice, 'f', 2)));

        // 2. 折后价
        ui->orderTable->setItem(i, 2, new QTableWidgetItem(QString::number(item.price, 'f', 2)));

        // 3. 数量
        ui->orderTable->setItem(i, 3, new QTableWidgetItem(QString::number(item.quantity)));

        // 4. 小计 = 折后价 * 数量
        ui->orderTable->setItem(i, 4, new QTableWidgetItem(QString::number(item.totalPrice, 'f', 2)));

        // 5. 商家
        ui->orderTable->setItem(i, 5, new QTableWidgetItem(item.owner));

        // 设置所有项为只读
        for (int col = 0; col < 6; ++col) {
            if (ui->orderTable->item(i, col)) {
                ui->orderTable->item(i, col)->setFlags(ui->orderTable->item(i, col)->flags() & ~Qt::ItemIsEditable);
            }
        }
    }
}

void OrderDialog::updateTotalAmount()
{
    m_totalAmount = 0.0;

    // 计算所有商品的总金额
    for (const OrderItem& item : m_orderItems) {
        m_totalAmount += item.totalPrice;
    }

    ui->totalAmountLabel->setText(QString("总计：￥%1").arg(m_totalAmount, 0, 'f', 2));
}

// 移除了复杂的选择和生成订单逻辑，保持单机版的简洁设计

void OrderDialog::on_payOrderButton_clicked()
{
    // 检查消费者余额
    if (m_clientMgr->getCurrentBalance() < m_totalAmount) {
        QMessageBox::warning(this, "余额不足", "您的余额不足，请先充值。");
        reject();
        return;
    }

    // 确认支付（此时库存已经在预订单阶段扣减了）
    QString errorMsg;
    if (!m_clientMgr->createOrder(errorMsg)) {
        QMessageBox::warning(this, "支付失败", errorMsg);
        reject();
        return;
    }

    accept();
}

void OrderDialog::on_cancelButton_clicked()
{
    reject();
}
