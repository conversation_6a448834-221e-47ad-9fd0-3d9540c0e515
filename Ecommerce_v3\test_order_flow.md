# 新订单处理流程测试指南

## 测试目标
验证"订单确认后立即库存扣减，如果支付失败再加回去"的新订单处理流程。

## 测试场景

### 场景1：正常订单流程测试
**目标**：验证正常情况下的订单处理流程

**步骤**：
1. 启动服务器和客户端
2. 登录一个消费者账户（确保有足够余额）
3. 浏览商品，选择一个有库存的商品
4. 添加到购物车
5. 生成订单并支付

**预期结果**：
- 订单确认后，商品库存立即减少
- 支付成功后，用户余额减少，商家余额增加
- 购物车中的商品被清除
- 服务器日志显示完整的处理流程

### 场景2：余额不足的订单流程测试
**目标**：验证支付失败时的库存回滚机制

**步骤**：
1. 登录一个余额不足的消费者账户
2. 选择一个价格超过用户余额的商品
3. 添加到购物车
4. 尝试生成订单并支付

**预期结果**：
- 订单确认后，商品库存立即减少
- 支付失败时，库存自动回滚到原始数量
- 用户收到"余额不足"的错误提示
- 服务器日志显示库存回滚过程

### 场景3：并发订单测试
**目标**：验证多用户同时下单时的库存处理

**步骤**：
1. 启动多个客户端实例
2. 同时登录不同的用户账户
3. 选择同一个商品（库存数量有限）
4. 几乎同时提交订单

**预期结果**：
- 先确认的订单成功扣减库存
- 后续订单如果库存不足应该失败
- 不会出现超卖情况

## 日志观察要点

### 成功订单的日志模式
```
开始处理订单，用户: [用户名] 订单金额: [金额]
库存扣减 - 商品ID: [ID] 原库存: [数量] 扣减数量: [数量] 新库存: [数量]
支付成功，订单处理完成
```

### 失败订单的日志模式
```
开始处理订单，用户: [用户名] 订单金额: [金额]
库存扣减 - 商品ID: [ID] 原库存: [数量] 扣减数量: [数量] 新库存: [数量]
支付失败，开始回滚库存，错误信息: [错误信息]
库存回滚 - 商品ID: [ID] 恢复库存: [数量]
库存回滚完成
```

## 测试数据准备

### 用户数据
- 富有用户：余额1000元
- 贫穷用户：余额10元

### 商品数据
- 便宜商品：价格5元，库存10个
- 昂贵商品：价格100元，库存5个

## 验证检查点

1. **库存一致性**：确保库存扣减和回滚操作的原子性
2. **余额一致性**：确保用户和商家余额的正确更新
3. **错误处理**：确保各种异常情况都能正确处理
4. **日志完整性**：确保所有关键操作都有日志记录
5. **用户体验**：确保用户界面能正确显示操作结果

## 性能测试

### 响应时间测试
- 测量从订单确认到支付完成的总时间
- 测量库存回滚操作的执行时间

### 并发性能测试
- 测试多用户同时下单的处理能力
- 验证系统在高并发下的稳定性

## 回归测试

确保新的订单处理流程不会影响其他功能：
- 用户登录/注册
- 商品管理
- 购物车操作
- 个人信息管理

## 测试结果记录

### 测试环境
- 操作系统：Windows 11
- Qt版本：6.8.3
- 编译器：MinGW 13.2.0

### 测试结果
[ ] 场景1：正常订单流程 - 通过/失败
[ ] 场景2：余额不足流程 - 通过/失败  
[ ] 场景3：并发订单测试 - 通过/失败
[ ] 库存一致性验证 - 通过/失败
[ ] 余额一致性验证 - 通过/失败
[ ] 错误处理验证 - 通过/失败
[ ] 日志完整性验证 - 通过/失败

### 发现的问题
1. [记录发现的问题]
2. [记录发现的问题]

### 改进建议
1. [记录改进建议]
2. [记录改进建议]
