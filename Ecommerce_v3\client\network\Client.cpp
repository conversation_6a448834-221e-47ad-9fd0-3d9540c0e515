#include "Client.h"
#include "../../common/Message.h"
#include <QDebug>
#include <QEventLoop>
#include <QTimer>
#include <QHostAddress>

Client::Client(QObject* parent)
    : QObject(parent)
    , m_socket(new QTcpSocket(this))
    , m_heartbeatTimer(new QTimer(this))
    , m_serverPort(0)
    , m_waitingForResponse(false)
{
    // 连接socket信号
    connect(m_socket, &QTcpSocket::connected, this, &Client::onConnected);
    connect(m_socket, &QTcpSocket::disconnected, this, &Client::onDisconnected);
    connect(m_socket, &QTcpSocket::readyRead, this, &Client::onReadyRead);
    connect(m_socket, QOverload<QAbstractSocket::SocketError>::of(&QAbstractSocket::errorOccurred),
            this, &Client::onError);
    
    // 设置心跳定时器
    m_heartbeatTimer->setInterval(HEARTBEAT_INTERVAL);
    connect(m_heartbeatTimer, &QTimer::timeout, this, &Client::onHeartbeatTimer);
}

Client::~Client()
{
    disconnectFromServer();
}

bool Client::connectToServer(const QString& host, quint16 port)
{
    if (isConnected()) {
        qWarning() << "Already connected to server";
        return true;
    }
    
    m_serverHost = host;
    m_serverPort = port;
    
    qInfo() << "Connecting to server:" << host << ":" << port;
    
    m_socket->connectToHost(host, port);
    
    // 等待连接完成
    if (!m_socket->waitForConnected(5000)) {
        qCritical() << "Failed to connect to server:" << m_socket->errorString();
        return false;
    }
    
    return true;
}

void Client::disconnectFromServer()
{
    if (m_socket->state() != QAbstractSocket::UnconnectedState) {
        qInfo() << "Disconnecting from server";
        stopHeartbeat();
        m_socket->disconnectFromHost();
        
        if (m_socket->state() != QAbstractSocket::UnconnectedState) {
            m_socket->waitForDisconnected(3000);
        }
    }
}

bool Client::isConnected() const
{
    return m_socket->state() == QAbstractSocket::ConnectedState;
}

QJsonObject Client::sendRequest(const QString& action, const QJsonObject& data)
{
    if (!isConnected()) {
        QJsonObject error;
        error["success"] = false;
        error["message"] = "Not connected to server";
        return error;
    }
    
    // 创建请求消息
    Message msg(action, data);
    QString requestStr = msg.toJson();
    
    // 发送请求
    QByteArray requestData = requestStr.toUtf8();
    QByteArray lengthPrefix;
    lengthPrefix.resize(4);
    qToBigEndian(static_cast<quint32>(requestData.size()), lengthPrefix.data());
    
    m_socket->write(lengthPrefix + requestData);
    m_socket->flush();
    
    // 等待响应
    m_waitingForResponse = true;
    QEventLoop loop;
    QTimer timeoutTimer;
    timeoutTimer.setSingleShot(true);
    timeoutTimer.setInterval(RESPONSE_TIMEOUT);
    
    connect(this, &Client::responseReceived, &loop, &QEventLoop::quit);
    connect(&timeoutTimer, &QTimer::timeout, &loop, &QEventLoop::quit);
    
    timeoutTimer.start();
    loop.exec();
    
    m_waitingForResponse = false;
    
    if (!timeoutTimer.isActive()) {
        // 超时
        QJsonObject error;
        error["success"] = false;
        error["message"] = "Request timeout";
        return error;
    }
    
    timeoutTimer.stop();
    return m_lastResponse;
}

void Client::sendRequestAsync(const QString& action, const QJsonObject& data)
{
    if (!isConnected()) {
        emit errorOccurred("Not connected to server");
        return;
    }
    
    // 创建请求消息
    Message msg(action, data);
    QString requestStr = msg.toJson();
    
    // 发送请求
    QByteArray requestData = requestStr.toUtf8();
    QByteArray lengthPrefix;
    lengthPrefix.resize(4);
    qToBigEndian(static_cast<quint32>(requestData.size()), lengthPrefix.data());
    
    m_socket->write(lengthPrefix + requestData);
    m_socket->flush();
}

void Client::onConnected()
{
    qInfo() << "Connected to server:" << m_serverHost << ":" << m_serverPort;
    startHeartbeat();
    emit connected();
}

void Client::onDisconnected()
{
    qInfo() << "Disconnected from server";
    stopHeartbeat();
    emit disconnected();
}

void Client::onReadyRead()
{
    m_buffer.append(m_socket->readAll());
    
    // 处理完整的消息
    while (m_buffer.size() >= 4) {
        // 读取消息长度
        quint32 messageLength = qFromBigEndian<quint32>(m_buffer.constData());
        
        if (m_buffer.size() < 4 + messageLength) {
            // 消息还没有完全接收
            break;
        }
        
        // 提取完整消息
        QByteArray messageData = m_buffer.mid(4, messageLength);
        m_buffer.remove(0, 4 + messageLength);
        
        QString response = QString::fromUtf8(messageData);
        processResponse(response);
    }
}

void Client::onError(QAbstractSocket::SocketError error)
{
    QString errorString = m_socket->errorString();
    qCritical() << "Socket error:" << error << errorString;
    emit errorOccurred(errorString);
}

void Client::onHeartbeatTimer()
{
    if (isConnected()) {
        // 发送心跳包（空的ping消息）
        sendRequestAsync("ping");
    }
}

void Client::processResponse(const QString& response)
{
    qDebug() << "Received response:" << response;
    
    Response resp = Response::fromJson(response);
    if (!resp.isValid()) {
        qWarning() << "Invalid response format";
        return;
    }
    
    QJsonObject responseObj;
    responseObj["success"] = resp.isSuccess();
    responseObj["message"] = resp.getMessage();
    responseObj["status"] = resp.getStatus();
    
    QJsonObject data = resp.getData();
    if (!data.isEmpty()) {
        responseObj["data"] = data;
    }
    
    if (m_waitingForResponse) {
        m_lastResponse = responseObj;
    }
    
    emit responseReceived(responseObj);
}

void Client::startHeartbeat()
{
    m_heartbeatTimer->start();
}

void Client::stopHeartbeat()
{
    m_heartbeatTimer->stop();
}
