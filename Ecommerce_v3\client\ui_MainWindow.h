/********************************************************************************
** Form generated from reading UI file 'MainWindow.ui'
**
** Created by: Qt User Interface Compiler version 6.8.3
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MAINWINDOW_H
#define UI_MAINWINDOW_H

#include <QtCore/QVariant>
#include <QtGui/QIcon>
#include <QtWidgets/QApplication>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QFormLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QMenuBar>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QStatusBar>
#include <QtWidgets/QTabWidget>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_MainWindow
{
public:
    QWidget *centralwidget;
    QVBoxLayout *verticalLayout_main;
    QTabWidget *tabs;
    QWidget *homeTab;
    QVBoxLayout *verticalLayout_home;
    QHBoxLayout *horizontalLayout_searchHome;
    QComboBox *categoryFilterCombo;
    QLineEdit *homeSearchLineEdit;
    QPushButton *searchHomeButton;
    QPushButton *refreshHomeButton;
    QSpacerItem *horizontalSpacer_searchHomeRight;
    QTableWidget *homeProductsTable;
    QHBoxLayout *horizontalLayout_homeButtons;
    QSpacerItem *horizontalSpacer_homeLeft;
    QPushButton *addToCartButton;
    QSpacerItem *horizontalSpacer_homeRight;
    QWidget *cartTab;
    QVBoxLayout *verticalLayout_cart;
    QTableWidget *cartTable;
    QHBoxLayout *horizontalLayout_cartButtons;
    QSpacerItem *horizontalSpacer_cartLeft;
    QPushButton *refreshCartButton;
    QPushButton *generateOrderButton;
    QPushButton *removeCartButton;
    QSpacerItem *horizontalSpacer_cartRight;
    QWidget *productTab;
    QVBoxLayout *verticalLayout_product;
    QTableWidget *productProductsTable;
    QHBoxLayout *horizontalLayout_productButtons;
    QSpacerItem *horizontalSpacer_productLeft;
    QPushButton *addProductButton;
    QPushButton *refreshProductButton;
    QPushButton *updateProductButton;
    QPushButton *batchDiscountButton;
    QSpacerItem *horizontalSpacer_productRight;
    QWidget *accountTab;
    QVBoxLayout *verticalLayout_account;
    QHBoxLayout *avatarLayout;
    QLabel *labelAvatar;
    QLabel *avatarLabel;
    QSpacerItem *avatarSpacer;
    QFormLayout *formLayout_account;
    QLabel *labelUsername;
    QLabel *usernameLabel;
    QLabel *labelUserType;
    QLabel *userTypeLabel;
    QLabel *labelBalance;
    QLabel *balanceLabel;
    QLabel *labelSignature;
    QLabel *signatureLabel;
    QSpacerItem *verticalSpacer_accountTop;
    QHBoxLayout *horizontalLayout_accountButtons;
    QSpacerItem *horizontalSpacer_accountLeft;
    QPushButton *refreshProfileButton;
    QPushButton *rechargeButton;
    QPushButton *profileButton;
    QPushButton *changePwdButton;
    QPushButton *logoutButton;
    QSpacerItem *horizontalSpacer_accountRight;
    QMenuBar *menubar;
    QStatusBar *statusbar;

    void setupUi(QMainWindow *MainWindow)
    {
        if (MainWindow->objectName().isEmpty())
            MainWindow->setObjectName("MainWindow");
        MainWindow->resize(1000, 700);
        centralwidget = new QWidget(MainWindow);
        centralwidget->setObjectName("centralwidget");
        verticalLayout_main = new QVBoxLayout(centralwidget);
        verticalLayout_main->setObjectName("verticalLayout_main");
        tabs = new QTabWidget(centralwidget);
        tabs->setObjectName("tabs");
        homeTab = new QWidget();
        homeTab->setObjectName("homeTab");
        verticalLayout_home = new QVBoxLayout(homeTab);
        verticalLayout_home->setObjectName("verticalLayout_home");
        horizontalLayout_searchHome = new QHBoxLayout();
        horizontalLayout_searchHome->setObjectName("horizontalLayout_searchHome");
        categoryFilterCombo = new QComboBox(homeTab);
        categoryFilterCombo->addItem(QString());
        categoryFilterCombo->addItem(QString());
        categoryFilterCombo->addItem(QString());
        categoryFilterCombo->addItem(QString());
        categoryFilterCombo->setObjectName("categoryFilterCombo");
        categoryFilterCombo->setMinimumSize(QSize(100, 0));

        horizontalLayout_searchHome->addWidget(categoryFilterCombo);

        homeSearchLineEdit = new QLineEdit(homeTab);
        homeSearchLineEdit->setObjectName("homeSearchLineEdit");

        horizontalLayout_searchHome->addWidget(homeSearchLineEdit);

        searchHomeButton = new QPushButton(homeTab);
        searchHomeButton->setObjectName("searchHomeButton");
        QIcon icon;
        icon.addFile(QString::fromUtf8(":/icons/search.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        searchHomeButton->setIcon(icon);

        horizontalLayout_searchHome->addWidget(searchHomeButton);

        refreshHomeButton = new QPushButton(homeTab);
        refreshHomeButton->setObjectName("refreshHomeButton");
        QIcon icon1;
        icon1.addFile(QString::fromUtf8(":/icons/refresh.svg"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        refreshHomeButton->setIcon(icon1);

        horizontalLayout_searchHome->addWidget(refreshHomeButton);

        horizontalSpacer_searchHomeRight = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_searchHome->addItem(horizontalSpacer_searchHomeRight);


        verticalLayout_home->addLayout(horizontalLayout_searchHome);

        homeProductsTable = new QTableWidget(homeTab);
        homeProductsTable->setObjectName("homeProductsTable");
        homeProductsTable->setSelectionBehavior(QAbstractItemView::SelectRows);
        homeProductsTable->setSortingEnabled(true);

        verticalLayout_home->addWidget(homeProductsTable);

        horizontalLayout_homeButtons = new QHBoxLayout();
        horizontalLayout_homeButtons->setObjectName("horizontalLayout_homeButtons");
        horizontalSpacer_homeLeft = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_homeButtons->addItem(horizontalSpacer_homeLeft);

        addToCartButton = new QPushButton(homeTab);
        addToCartButton->setObjectName("addToCartButton");
        QIcon icon2;
        icon2.addFile(QString::fromUtf8(":/icons/cart.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        addToCartButton->setIcon(icon2);

        horizontalLayout_homeButtons->addWidget(addToCartButton);

        horizontalSpacer_homeRight = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_homeButtons->addItem(horizontalSpacer_homeRight);


        verticalLayout_home->addLayout(horizontalLayout_homeButtons);

        tabs->addTab(homeTab, QString());
        cartTab = new QWidget();
        cartTab->setObjectName("cartTab");
        verticalLayout_cart = new QVBoxLayout(cartTab);
        verticalLayout_cart->setObjectName("verticalLayout_cart");
        cartTable = new QTableWidget(cartTab);
        cartTable->setObjectName("cartTable");
        cartTable->setSelectionBehavior(QAbstractItemView::SelectRows);

        verticalLayout_cart->addWidget(cartTable);

        horizontalLayout_cartButtons = new QHBoxLayout();
        horizontalLayout_cartButtons->setObjectName("horizontalLayout_cartButtons");
        horizontalSpacer_cartLeft = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_cartButtons->addItem(horizontalSpacer_cartLeft);

        refreshCartButton = new QPushButton(cartTab);
        refreshCartButton->setObjectName("refreshCartButton");
        refreshCartButton->setIcon(icon1);

        horizontalLayout_cartButtons->addWidget(refreshCartButton);

        generateOrderButton = new QPushButton(cartTab);
        generateOrderButton->setObjectName("generateOrderButton");
        QIcon icon3;
        icon3.addFile(QString::fromUtf8(":/icons/order.svg"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        generateOrderButton->setIcon(icon3);

        horizontalLayout_cartButtons->addWidget(generateOrderButton);

        removeCartButton = new QPushButton(cartTab);
        removeCartButton->setObjectName("removeCartButton");
        QIcon icon4;
        icon4.addFile(QString::fromUtf8(":/icons/delete.svg"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        removeCartButton->setIcon(icon4);

        horizontalLayout_cartButtons->addWidget(removeCartButton);

        horizontalSpacer_cartRight = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_cartButtons->addItem(horizontalSpacer_cartRight);


        verticalLayout_cart->addLayout(horizontalLayout_cartButtons);

        tabs->addTab(cartTab, QString());
        productTab = new QWidget();
        productTab->setObjectName("productTab");
        verticalLayout_product = new QVBoxLayout(productTab);
        verticalLayout_product->setObjectName("verticalLayout_product");
        productProductsTable = new QTableWidget(productTab);
        productProductsTable->setObjectName("productProductsTable");
        productProductsTable->setSelectionBehavior(QAbstractItemView::SelectRows);

        verticalLayout_product->addWidget(productProductsTable);

        horizontalLayout_productButtons = new QHBoxLayout();
        horizontalLayout_productButtons->setObjectName("horizontalLayout_productButtons");
        horizontalSpacer_productLeft = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_productButtons->addItem(horizontalSpacer_productLeft);

        addProductButton = new QPushButton(productTab);
        addProductButton->setObjectName("addProductButton");
        QIcon icon5;
        icon5.addFile(QString::fromUtf8(":/icons/add.svg"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        addProductButton->setIcon(icon5);

        horizontalLayout_productButtons->addWidget(addProductButton);

        refreshProductButton = new QPushButton(productTab);
        refreshProductButton->setObjectName("refreshProductButton");
        refreshProductButton->setIcon(icon1);

        horizontalLayout_productButtons->addWidget(refreshProductButton);

        updateProductButton = new QPushButton(productTab);
        updateProductButton->setObjectName("updateProductButton");
        QIcon icon6;
        icon6.addFile(QString::fromUtf8(":/icons/edit.svg"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        updateProductButton->setIcon(icon6);

        horizontalLayout_productButtons->addWidget(updateProductButton);

        batchDiscountButton = new QPushButton(productTab);
        batchDiscountButton->setObjectName("batchDiscountButton");
        batchDiscountButton->setIcon(icon6);

        horizontalLayout_productButtons->addWidget(batchDiscountButton);

        horizontalSpacer_productRight = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_productButtons->addItem(horizontalSpacer_productRight);


        verticalLayout_product->addLayout(horizontalLayout_productButtons);

        tabs->addTab(productTab, QString());
        accountTab = new QWidget();
        accountTab->setObjectName("accountTab");
        verticalLayout_account = new QVBoxLayout(accountTab);
        verticalLayout_account->setObjectName("verticalLayout_account");
        avatarLayout = new QHBoxLayout();
        avatarLayout->setObjectName("avatarLayout");
        labelAvatar = new QLabel(accountTab);
        labelAvatar->setObjectName("labelAvatar");
        QFont font;
        font.setPointSize(12);
        font.setBold(true);
        labelAvatar->setFont(font);
        labelAvatar->setAlignment(Qt::AlignVCenter);

        avatarLayout->addWidget(labelAvatar);

        avatarLabel = new QLabel(accountTab);
        avatarLabel->setObjectName("avatarLabel");
        avatarLabel->setMinimumSize(QSize(100, 100));
        avatarLabel->setMaximumSize(QSize(100, 100));
        avatarLabel->setAlignment(Qt::AlignCenter);
        avatarLabel->setScaledContents(true);

        avatarLayout->addWidget(avatarLabel);

        avatarSpacer = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        avatarLayout->addItem(avatarSpacer);


        verticalLayout_account->addLayout(avatarLayout);

        formLayout_account = new QFormLayout();
        formLayout_account->setObjectName("formLayout_account");
        labelUsername = new QLabel(accountTab);
        labelUsername->setObjectName("labelUsername");
        QFont font1;
        font1.setPointSize(11);
        font1.setBold(true);
        labelUsername->setFont(font1);

        formLayout_account->setWidget(0, QFormLayout::LabelRole, labelUsername);

        usernameLabel = new QLabel(accountTab);
        usernameLabel->setObjectName("usernameLabel");
        QFont font2;
        font2.setPointSize(11);
        usernameLabel->setFont(font2);

        formLayout_account->setWidget(0, QFormLayout::FieldRole, usernameLabel);

        labelUserType = new QLabel(accountTab);
        labelUserType->setObjectName("labelUserType");
        labelUserType->setFont(font1);

        formLayout_account->setWidget(1, QFormLayout::LabelRole, labelUserType);

        userTypeLabel = new QLabel(accountTab);
        userTypeLabel->setObjectName("userTypeLabel");
        userTypeLabel->setFont(font2);

        formLayout_account->setWidget(1, QFormLayout::FieldRole, userTypeLabel);

        labelBalance = new QLabel(accountTab);
        labelBalance->setObjectName("labelBalance");
        labelBalance->setFont(font1);

        formLayout_account->setWidget(2, QFormLayout::LabelRole, labelBalance);

        balanceLabel = new QLabel(accountTab);
        balanceLabel->setObjectName("balanceLabel");
        balanceLabel->setFont(font1);

        formLayout_account->setWidget(2, QFormLayout::FieldRole, balanceLabel);

        labelSignature = new QLabel(accountTab);
        labelSignature->setObjectName("labelSignature");
        labelSignature->setFont(font1);

        formLayout_account->setWidget(3, QFormLayout::LabelRole, labelSignature);

        signatureLabel = new QLabel(accountTab);
        signatureLabel->setObjectName("signatureLabel");
        QFont font3;
        font3.setPointSize(11);
        font3.setItalic(true);
        signatureLabel->setFont(font3);

        formLayout_account->setWidget(3, QFormLayout::FieldRole, signatureLabel);


        verticalLayout_account->addLayout(formLayout_account);

        verticalSpacer_accountTop = new QSpacerItem(20, 40, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        verticalLayout_account->addItem(verticalSpacer_accountTop);

        horizontalLayout_accountButtons = new QHBoxLayout();
        horizontalLayout_accountButtons->setSpacing(12);
        horizontalLayout_accountButtons->setObjectName("horizontalLayout_accountButtons");
        horizontalSpacer_accountLeft = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_accountButtons->addItem(horizontalSpacer_accountLeft);

        refreshProfileButton = new QPushButton(accountTab);
        refreshProfileButton->setObjectName("refreshProfileButton");
        refreshProfileButton->setIcon(icon1);

        horizontalLayout_accountButtons->addWidget(refreshProfileButton);

        rechargeButton = new QPushButton(accountTab);
        rechargeButton->setObjectName("rechargeButton");

        horizontalLayout_accountButtons->addWidget(rechargeButton);

        profileButton = new QPushButton(accountTab);
        profileButton->setObjectName("profileButton");

        horizontalLayout_accountButtons->addWidget(profileButton);

        changePwdButton = new QPushButton(accountTab);
        changePwdButton->setObjectName("changePwdButton");

        horizontalLayout_accountButtons->addWidget(changePwdButton);

        logoutButton = new QPushButton(accountTab);
        logoutButton->setObjectName("logoutButton");

        horizontalLayout_accountButtons->addWidget(logoutButton);

        horizontalSpacer_accountRight = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_accountButtons->addItem(horizontalSpacer_accountRight);


        verticalLayout_account->addLayout(horizontalLayout_accountButtons);

        tabs->addTab(accountTab, QString());

        verticalLayout_main->addWidget(tabs);

        MainWindow->setCentralWidget(centralwidget);
        menubar = new QMenuBar(MainWindow);
        menubar->setObjectName("menubar");
        menubar->setGeometry(QRect(0, 0, 1000, 22));
        MainWindow->setMenuBar(menubar);
        statusbar = new QStatusBar(MainWindow);
        statusbar->setObjectName("statusbar");
        MainWindow->setStatusBar(statusbar);

        retranslateUi(MainWindow);

        tabs->setCurrentIndex(0);


        QMetaObject::connectSlotsByName(MainWindow);
    } // setupUi

    void retranslateUi(QMainWindow *MainWindow)
    {
        MainWindow->setWindowTitle(QCoreApplication::translate("MainWindow", "\347\224\265\345\225\206\344\272\244\346\230\223\345\271\263\345\217\260", nullptr));
        centralwidget->setStyleSheet(QCoreApplication::translate("MainWindow", "QWidget#centralwidget {\n"
"    background-image: url(:/images/background.jpg);\n"
"    background-repeat: no-repeat;\n"
"    background-position: center;\n"
"    background-attachment: fixed;\n"
"}", nullptr));
        tabs->setStyleSheet(QCoreApplication::translate("MainWindow", "QTabWidget::pane {\n"
"    border: 2px solid #C0C0C0;\n"
"    border-radius: 8px;\n"
"    background-color: rgba(255, 255, 255, 0.9);\n"
"}\n"
"\n"
"QTabBar::tab {\n"
"    background-color: rgba(240, 240, 240, 0.8);\n"
"    border: 1px solid #C0C0C0;\n"
"    border-bottom: none;\n"
"    border-top-left-radius: 6px;\n"
"    border-top-right-radius: 6px;\n"
"    padding: 8px 16px;\n"
"    margin-right: 2px;\n"
"    font-weight: bold;\n"
"    font-size: 11pt;\n"
"}\n"
"\n"
"QTabBar::tab:selected {\n"
"    background-color: rgba(255, 255, 255, 0.95);\n"
"    border-color: #4A90E2;\n"
"    color: #4A90E2;\n"
"}\n"
"\n"
"QTabBar::tab:hover {\n"
"    background-color: rgba(250, 250, 250, 0.9);\n"
"}", nullptr));
        categoryFilterCombo->setItemText(0, QCoreApplication::translate("MainWindow", "\345\205\250\351\203\250\347\261\273\345\236\213", nullptr));
        categoryFilterCombo->setItemText(1, QCoreApplication::translate("MainWindow", "\345\233\276\344\271\246", nullptr));
        categoryFilterCombo->setItemText(2, QCoreApplication::translate("MainWindow", "\346\234\215\350\243\205", nullptr));
        categoryFilterCombo->setItemText(3, QCoreApplication::translate("MainWindow", "\351\243\237\345\223\201", nullptr));

        homeSearchLineEdit->setPlaceholderText(QCoreApplication::translate("MainWindow", "\346\220\234\347\264\242\345\225\206\345\223\201\345\220\215\347\247\260...", nullptr));
        searchHomeButton->setStyleSheet(QCoreApplication::translate("MainWindow", "QPushButton {\n"
"    background-color: #4A90E2;\n"
"    color: white;\n"
"    border: none;\n"
"    border-radius: 6px;\n"
"    padding: 8px 16px;\n"
"    font-weight: bold;\n"
"    font-size: 10pt;\n"
"    min-width: 80px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: #357ABD;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: #2E5F8A;\n"
"}", nullptr));
        searchHomeButton->setText(QCoreApplication::translate("MainWindow", "\346\220\234\347\264\242", nullptr));
        refreshHomeButton->setStyleSheet(QCoreApplication::translate("MainWindow", "QPushButton {\n"
"    background-color: #5CB85C;\n"
"    color: white;\n"
"    border: none;\n"
"    border-radius: 6px;\n"
"    padding: 8px 16px;\n"
"    font-weight: bold;\n"
"    font-size: 10pt;\n"
"    min-width: 80px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: #449D44;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: #357A35;\n"
"}", nullptr));
        refreshHomeButton->setText(QCoreApplication::translate("MainWindow", "\345\210\267\346\226\260", nullptr));
        addToCartButton->setStyleSheet(QCoreApplication::translate("MainWindow", "QPushButton {\n"
"    background-color: #F0AD4E;\n"
"    color: white;\n"
"    border: none;\n"
"    border-radius: 6px;\n"
"    padding: 8px 16px;\n"
"    font-weight: bold;\n"
"    font-size: 10pt;\n"
"    min-width: 100px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: #EC971F;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: #D58512;\n"
"}", nullptr));
        addToCartButton->setText(QCoreApplication::translate("MainWindow", "\345\212\240\345\205\245\350\264\255\347\211\251\350\275\246", nullptr));
        tabs->setTabText(tabs->indexOf(homeTab), QCoreApplication::translate("MainWindow", "\344\270\273\351\241\265", nullptr));
        refreshCartButton->setStyleSheet(QCoreApplication::translate("MainWindow", "QPushButton {\n"
"    background-color: #5CB85C;\n"
"    color: white;\n"
"    border: none;\n"
"    border-radius: 6px;\n"
"    padding: 8px 16px;\n"
"    font-weight: bold;\n"
"    font-size: 10pt;\n"
"    min-width: 80px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: #449D44;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: #357A35;\n"
"}", nullptr));
        refreshCartButton->setText(QCoreApplication::translate("MainWindow", "\345\210\267\346\226\260", nullptr));
        generateOrderButton->setStyleSheet(QCoreApplication::translate("MainWindow", "QPushButton {\n"
"    background-color: #4A90E2;\n"
"    color: white;\n"
"    border: none;\n"
"    border-radius: 6px;\n"
"    padding: 8px 16px;\n"
"    font-weight: bold;\n"
"    font-size: 10pt;\n"
"    min-width: 100px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: #357ABD;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: #2E5F8A;\n"
"}", nullptr));
        generateOrderButton->setText(QCoreApplication::translate("MainWindow", "\347\224\237\346\210\220\350\256\242\345\215\225", nullptr));
        removeCartButton->setStyleSheet(QCoreApplication::translate("MainWindow", "QPushButton {\n"
"    background-color: #D9534F;\n"
"    color: white;\n"
"    border: none;\n"
"    border-radius: 6px;\n"
"    padding: 8px 16px;\n"
"    font-weight: bold;\n"
"    font-size: 10pt;\n"
"    min-width: 100px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: #C9302C;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: #AC2925;\n"
"}", nullptr));
        removeCartButton->setText(QCoreApplication::translate("MainWindow", "\347\247\273\351\231\244\345\225\206\345\223\201", nullptr));
        tabs->setTabText(tabs->indexOf(cartTab), QCoreApplication::translate("MainWindow", "\350\264\255\347\211\251\350\275\246", nullptr));
        addProductButton->setStyleSheet(QCoreApplication::translate("MainWindow", "QPushButton {\n"
"    background-color: #5CB85C;\n"
"    color: white;\n"
"    border: none;\n"
"    border-radius: 6px;\n"
"    padding: 8px 16px;\n"
"    font-weight: bold;\n"
"    font-size: 10pt;\n"
"    min-width: 100px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: #449D44;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: #357A35;\n"
"}", nullptr));
        addProductButton->setText(QCoreApplication::translate("MainWindow", "\346\267\273\345\212\240\345\225\206\345\223\201", nullptr));
        refreshProductButton->setStyleSheet(QCoreApplication::translate("MainWindow", "QPushButton {\n"
"    background-color: #5CB85C;\n"
"    color: white;\n"
"    border: none;\n"
"    border-radius: 6px;\n"
"    padding: 8px 16px;\n"
"    font-weight: bold;\n"
"    font-size: 10pt;\n"
"    min-width: 80px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: #449D44;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: #357A35;\n"
"}", nullptr));
        refreshProductButton->setText(QCoreApplication::translate("MainWindow", "\345\210\267\346\226\260", nullptr));
        updateProductButton->setStyleSheet(QCoreApplication::translate("MainWindow", "QPushButton {\n"
"    background-color: #F0AD4E;\n"
"    color: white;\n"
"    border: none;\n"
"    border-radius: 6px;\n"
"    padding: 8px 16px;\n"
"    font-weight: bold;\n"
"    font-size: 10pt;\n"
"    min-width: 100px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: #EC971F;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: #D58512;\n"
"}", nullptr));
        updateProductButton->setText(QCoreApplication::translate("MainWindow", "\344\277\256\346\224\271\345\225\206\345\223\201", nullptr));
        batchDiscountButton->setStyleSheet(QCoreApplication::translate("MainWindow", "QPushButton {\n"
"    background-color: #5BC0DE;\n"
"    color: white;\n"
"    border: none;\n"
"    border-radius: 6px;\n"
"    padding: 8px 16px;\n"
"    font-weight: bold;\n"
"    font-size: 10pt;\n"
"    min-width: 100px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: #31B0D5;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: #269ABC;\n"
"}", nullptr));
        batchDiscountButton->setText(QCoreApplication::translate("MainWindow", "\346\211\271\351\207\217\346\212\230\346\211\243", nullptr));
        tabs->setTabText(tabs->indexOf(productTab), QCoreApplication::translate("MainWindow", "\345\225\206\345\223\201\347\256\241\347\220\206", nullptr));
        labelAvatar->setText(QCoreApplication::translate("MainWindow", "\345\244\264\345\203\217\357\274\232", nullptr));
        avatarLabel->setText(QCoreApplication::translate("MainWindow", "\345\244\264\345\203\217", nullptr));
        avatarLabel->setStyleSheet(QCoreApplication::translate("MainWindow", "border: 1px solid gray;", nullptr));
        labelUsername->setText(QCoreApplication::translate("MainWindow", "\347\224\250\346\210\267\345\220\215\357\274\232", nullptr));
        usernameLabel->setText(QCoreApplication::translate("MainWindow", "-", nullptr));
        labelUserType->setText(QCoreApplication::translate("MainWindow", "\347\224\250\346\210\267\347\261\273\345\236\213\357\274\232", nullptr));
        userTypeLabel->setText(QCoreApplication::translate("MainWindow", "-", nullptr));
        labelBalance->setText(QCoreApplication::translate("MainWindow", "\350\264\246\346\210\267\344\275\231\351\242\235\357\274\232", nullptr));
        balanceLabel->setStyleSheet(QCoreApplication::translate("MainWindow", "color: #2E8B57;", nullptr));
        balanceLabel->setText(QCoreApplication::translate("MainWindow", "\357\277\2450.00", nullptr));
        labelSignature->setText(QCoreApplication::translate("MainWindow", "\344\270\252\346\200\247\347\255\276\345\220\215\357\274\232", nullptr));
        signatureLabel->setStyleSheet(QCoreApplication::translate("MainWindow", "color: #696969;", nullptr));
        signatureLabel->setText(QCoreApplication::translate("MainWindow", "-", nullptr));
        refreshProfileButton->setStyleSheet(QCoreApplication::translate("MainWindow", "QPushButton {\n"
"    background-color: #4A90E2;\n"
"    color: white;\n"
"    border: none;\n"
"    border-radius: 6px;\n"
"    padding: 8px 16px;\n"
"    font-weight: bold;\n"
"    font-size: 10pt;\n"
"    min-width: 80px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: #357ABD;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: #2E5F8A;\n"
"}", nullptr));
        refreshProfileButton->setText(QCoreApplication::translate("MainWindow", "\345\210\267\346\226\260", nullptr));
        rechargeButton->setStyleSheet(QCoreApplication::translate("MainWindow", "QPushButton {\n"
"    background-color: #5CB85C;\n"
"    color: white;\n"
"    border: none;\n"
"    border-radius: 6px;\n"
"    padding: 8px 16px;\n"
"    font-weight: bold;\n"
"    font-size: 10pt;\n"
"    min-width: 80px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: #449D44;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: #357A35;\n"
"}", nullptr));
        rechargeButton->setText(QCoreApplication::translate("MainWindow", "\345\205\205\345\200\274", nullptr));
        profileButton->setStyleSheet(QCoreApplication::translate("MainWindow", "QPushButton {\n"
"    background-color: #F0AD4E;\n"
"    color: white;\n"
"    border: none;\n"
"    border-radius: 6px;\n"
"    padding: 8px 16px;\n"
"    font-weight: bold;\n"
"    font-size: 10pt;\n"
"    min-width: 80px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: #EC971F;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: #D58512;\n"
"}", nullptr));
        profileButton->setText(QCoreApplication::translate("MainWindow", "\344\270\252\344\272\272\344\277\241\346\201\257", nullptr));
        changePwdButton->setStyleSheet(QCoreApplication::translate("MainWindow", "QPushButton {\n"
"    background-color: #5BC0DE;\n"
"    color: white;\n"
"    border: none;\n"
"    border-radius: 6px;\n"
"    padding: 8px 16px;\n"
"    font-weight: bold;\n"
"    font-size: 10pt;\n"
"    min-width: 80px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: #31B0D5;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: #269ABC;\n"
"}", nullptr));
        changePwdButton->setText(QCoreApplication::translate("MainWindow", "\344\277\256\346\224\271\345\257\206\347\240\201", nullptr));
        logoutButton->setStyleSheet(QCoreApplication::translate("MainWindow", "QPushButton {\n"
"    background-color: #D9534F;\n"
"    color: white;\n"
"    border: none;\n"
"    border-radius: 6px;\n"
"    padding: 8px 16px;\n"
"    font-weight: bold;\n"
"    font-size: 10pt;\n"
"    min-width: 80px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: #C9302C;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: #AC2925;\n"
"}", nullptr));
        logoutButton->setText(QCoreApplication::translate("MainWindow", "\351\200\200\345\207\272\347\231\273\345\275\225", nullptr));
        tabs->setTabText(tabs->indexOf(accountTab), QCoreApplication::translate("MainWindow", "\344\270\252\344\272\272\344\270\255\345\277\203", nullptr));
    } // retranslateUi

};

namespace Ui {
    class MainWindow: public Ui_MainWindow {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MAINWINDOW_H
