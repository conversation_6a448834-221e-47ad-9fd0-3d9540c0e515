#ifndef ORDER_H
#define ORDER_H

#include <QString>
#include <QVector>

class Order
{
public:
    enum Status { Pending, Completed, Cancelled };

    Order(int orderId, const QString& buyerUsername);

    int getOrderId() const;
    const QString& getBuyerUsername() const;
    Status getStatus() const;
    void setStatus(Status s);

    void addItem(int productId, int quantity);
    const QVector<QPair<int,int>>& getItems() const;
    // Each pair = (productId, quantity)

    QString toCsvString() const; // For file persistence

private:
    int m_orderId;
    QString m_buyerUsername;
    Status m_status;
    QVector<QPair<int,int>> m_items;
};

#endif // ORDER_H
