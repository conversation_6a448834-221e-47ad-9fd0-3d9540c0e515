#ifndef CLOTHING_H
#define CLOTHING_H

#include "Product.h"

class Clothing : public Product
{
public:
    Clothing(int id, const QString& name, double price, int stock,
             const QString& owner, double discount,
             const QString& size, const QString& color, const QString& imagePath = "");

    Category getCategory() const override { return ClothingCategory; }

    const QString& getSize() const { return m_size; }
    const QString& getColor() const { return m_color; }
    void setSize(const QString& size) { m_size = size; }
    void setColor(const QString& color) { m_color = color; }

    double getDiscountedPrice() const override
    {
        return m_price * m_discount;
    }

    QString getRemark() const override
    {
        // “备注”显示尺码 + 颜色
        return QString("尺码：%1  颜色：%2").arg(m_size).arg(m_color);
    }

    QString toCsvString() const override;

private:
    QString m_size;
    QString m_color;
};

#endif // CLOTHING_H
