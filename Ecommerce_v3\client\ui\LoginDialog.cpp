#include "LoginDialog.h"
#include "ui_LoginDialog.h"
#include "RegisterDialog.h"
#include "StyleManager.h"
#include "../network/ClientManager.h"
#include <QMessageBox>

LoginDialog::LoginDialog(ClientManager* clientMgr, QWidget *parent) :
    QDialog(parent),
    ui(new Ui::LoginDialog),
    m_clientMgr(clientMgr)
{
    ui->setupUi(this);
    setWindowTitle("E-Commerce Login");

    // 设置密码输入框为密码模式
    ui->passwordLineEdit->setEchoMode(QLineEdit::Password);

    // 应用样式
    applyStyles();
}

void LoginDialog::applyStyles()
{
    StyleManager& styleMgr = StyleManager::instance();

    // 设置对话框样式
    this->setStyleSheet(R"(
QDialog {
    background-color: #F8F9FA;
    border-radius: 8px;
}

QLabel {
    color: #495057;
    font-size: 11pt;
    font-weight: bold;
}

QLineEdit {
    border: 2px solid #E9ECEF;
    border-radius: 6px;
    padding: 10px;
    font-size: 11pt;
    background-color: white;
}

QLineEdit:focus {
    border-color: #4A90E2;
    outline: none;
}
)");

    // 设置按钮样式
    ui->loginButton->setStyleSheet(styleMgr.getButtonStyle(StyleManager::PRIMARY_COLOR));
    ui->registerButton->setStyleSheet(styleMgr.getButtonStyle(StyleManager::SUCCESS_COLOR));
}

LoginDialog::~LoginDialog()
{
    delete ui;
}

void LoginDialog::on_loginButton_clicked()
{
    QString username = ui->usernameLineEdit->text().trimmed();
    QString password = ui->passwordLineEdit->text();

    if (username.isEmpty() || password.isEmpty()) {
        QMessageBox::warning(this, "输入错误", "用户名和密码不能为空。");
        return;
    }

    QString errorMsg;
    if (!m_clientMgr->login(username, password, errorMsg)) {
        QMessageBox::warning(this, "登录失败", errorMsg);
        return;
    }

    QMessageBox::information(this, "登录成功", 
                           QString("欢迎，%1！").arg(m_clientMgr->getCurrentUsername()));
    accept();
}

void LoginDialog::on_registerButton_clicked()
{
    RegisterDialog dlg(m_clientMgr, this);
    dlg.exec();
}
