#ifndef CLIENTHANDLER_H
#define CLIENTHANDLER_H

#include <QObject>
#include <QTcpSocket>
#include <QTimer>
#include <QByteArray>

class MessageDispatcher;
class User;

class ClientHandler : public QObject
{
    Q_OBJECT

public:
    explicit ClientHandler(qintptr socketDescriptor, MessageDispatcher* dispatcher, QObject* parent = nullptr);
    ~ClientHandler();
    
    // 获取客户端信息
    QString getClientInfo() const;
    QTcpSocket* getSocket() const { return m_socket; }
    bool isConnected() const;
    
    // 断开连接
    void disconnectFromHost();
    
    // 发送响应
    void sendResponse(const QString& response);
    
    // 获取当前登录用户
    User* getCurrentUser() const { return m_currentUser; }
    void setCurrentUser(User* user) { m_currentUser = user; }

signals:
    void disconnected();
    void clientDisconnecting(ClientHandler* client);

private slots:
    void onReadyRead();
    void onDisconnected();
    void onError(QAbstractSocket::SocketError error);
    void onHeartbeatTimeout();

private:
    void processMessage(const QString& message);
    void setupHeartbeat();
    void resetHeartbeat();

private:
    QTcpSocket* m_socket;
    MessageDispatcher* m_dispatcher;
    User* m_currentUser;
    
    QByteArray m_buffer;
    QTimer* m_heartbeatTimer;
    
    QString m_clientAddress;
    quint16 m_clientPort;
    
    static const int HEARTBEAT_TIMEOUT = 60000; // 60秒心跳超时
};

#endif // CLIENTHANDLER_H
