#include "RegisterDialog.h"
#include "ui_RegisterDialog.h"
#include <QMessageBox>

RegisterDialog::RegisterDialog(UserManager* userMgr, QWidget *parent) :
    QDialog(parent),
    ui(new Ui::RegisterDialog),
    m_userMgr(userMgr)
{
    ui->setupUi(this);
    setWindowTitle("Register New Account");

    // User type combo: "Consumer", "Seller"
    ui->userTypeCombo->addItem("Consumer", QVariant::fromValue(User::ConsumerType));
    ui->userTypeCombo->addItem("Seller", QVariant::fromValue(User::SellerType));
}

RegisterDialog::~RegisterDialog()
{
    delete ui;
}

void RegisterDialog::on_registerButton_clicked()
{
    QString username = ui->usernameLineEdit->text().trimmed();
    QString password = ui->passwordLineEdit->text();
    QString confirm = ui->confirmLineEdit->text();
    User::UserType type = static_cast<User::UserType>(ui->userTypeCombo->currentData().toInt());

    if (username.isEmpty() || password.isEmpty() || confirm.isEmpty()) {
        QMessageBox::warning(this, "Input Error", "All fields are required.");
        return;
    }
    if (password != confirm) {
        QMessageBox::warning(this, "Password Mismatch", "Passwords do not match.");
        return;
    }
    // Simple validation: username length >= 2, password length >= 6
    if (username.length() < 2) {
        QMessageBox::warning(this, "Invalid Username", "Username must be at least 2 characters.");
        return;
    }
    if (password.length() < 6) {
        QMessageBox::warning(this, "Invalid Password", "Password must be at least 6 characters.");
        return;
    }

    if (!m_userMgr->registerUser(username, password, type)) {
        QMessageBox::warning(this, "Registration Failed", "Username already exists.");
        return;
    }

    QMessageBox::information(this, "Success", "Registration successful. You may now log in.");
    accept();
}
