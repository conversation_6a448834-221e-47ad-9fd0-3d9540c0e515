#ifndef PRODUCTMANAGER_H
#define PRODUCTMANAGER_H

#include "../models/Product.h"
#include <QVector>
#include <QString>

class ProductManager
{
public:
    static ProductManager* instance();
    static void init(const QString& productFilePath);
    static void cleanup();

    bool loadProducts();
    bool saveProducts();

    // 统一的 addProduct 接口，按类型分发
    bool addProduct(Product::Category cat,
                    int id, const QString& name, double price, int stock,
                    const QString& owner, double discount,
                    const QString& extra1 = "", const QString& extra2 = "", const QString& imagePath = "");

    Product* getProductById(int id);
    QVector<Product*> getAllProducts() const;

    QVector<Product*> searchByName(const QString& name) const;

    bool updateProductStock(int id, int newStock);
    bool updateProductPrice(int id, double newPrice);
    bool updateProductDiscount(int id, double newDiscount);  // 修改折扣

    int  getNextProductId() const;

private:
    ProductManager(const QString& productFilePath);
    ~ProductManager();

    QString m_productFilePath;
    QVector<Product*> m_products;

    bool idExists(int id) const;
};

#endif // PRODUCTMANAGER_H
