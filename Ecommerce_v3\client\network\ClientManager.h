#ifndef CLIENTMANAGER_H
#define CLIENTMANAGER_H

#include <QObject>
#include <QJsonObject>
#include <QJsonArray>
#include <QVector>

class Client;
class User;
class Product;

struct ClientProduct {
    int id;
    QString name;
    double originalPrice;
    double discountedPrice;
    int stock;
    QString owner;
    double discount;
    int category;
    QString remark;
    QString author;      // for books
    QString size;        // for clothing
    QString color;       // for clothing
    QString imagePath;   // 商品图片路径

    ClientProduct() : id(0), originalPrice(0), discountedPrice(0), stock(0), discount(1.0), category(0) {}
};

struct CartItem {
    int productId;
    int quantity;
    QString productName;
    double price;
    double totalPrice;
    
    CartItem() : productId(0), quantity(0), price(0), totalPrice(0) {}
};

class ClientManager : public QObject
{
    Q_OBJECT

public:
    explicit ClientManager(QObject* parent = nullptr);
    ~ClientManager();
    
    // 连接管理
    bool connectToServer(const QString& host = "localhost", quint16 port = 8888);
    void disconnectFromServer();
    bool isConnected() const;
    
    // 用户认证
    bool login(const QString& username, const QString& password, QString& errorMsg);
    bool registerUser(const QString& username, const QString& password, const QString& userType, QString& errorMsg);
    bool logout(QString& errorMsg);
    bool changePassword(const QString& oldPassword, const QString& newPassword, QString& errorMsg);
    bool rechargeBalance(double amount, QString& errorMsg);
    bool updateProfile(const QString& signature, const QString& avatarPath, QString& errorMsg);
    bool refreshUserInfo(QString& errorMsg);
    
    // 商品管理
    QVector<ClientProduct> getAllProducts(QString& errorMsg);
    QVector<ClientProduct> searchProducts(const QString& name, QString& errorMsg);
    QVector<ClientProduct> filterProducts(const QString& keyword, int category, QString& errorMsg);
    bool addProduct(int category, const QString& name, double price, int stock, double discount,
                   const QString& extra1, const QString& extra2, const QString& remark,
                   const QString& imagePath, QString& errorMsg);
    bool updateProductStock(int productId, int newStock, QString& errorMsg);
    bool updateProductPrice(int productId, double newPrice, QString& errorMsg);
    bool updateProductDiscount(int productId, double newDiscount, QString& errorMsg);
    bool batchUpdateDiscount(int category, double newDiscount, QString& errorMsg);
    
    // 购物车管理
    bool addToCart(int productId, int quantity, QString& errorMsg);
    bool removeFromCart(int productId, QString& errorMsg);
    QVector<CartItem> getCart(QString& errorMsg);
    bool clearCart(QString& errorMsg);
    
    // 订单管理
    bool reserveOrder(const QVector<QPair<int, int>>& orderItems, QString& errorMsg, double& totalAmount);  // 预订单
    bool createOrder(QString& errorMsg);                                                                    // 确认支付
    bool cancelOrder(QString& errorMsg);                                                                    // 取消订单
    bool createOrderWithItems(const QVector<QPair<int, int>>& orderItems, QString& errorMsg);
    
    // 获取当前用户信息
    QString getCurrentUsername() const { return m_currentUsername; }
    QString getCurrentUserType() const { return m_currentUserType; }
    double getCurrentBalance() const { return m_currentBalance; }
    QString getCurrentSignature() const { return m_currentSignature; }
    QString getCurrentAvatarPath() const { return m_currentAvatarPath; }
    bool isLoggedIn() const { return !m_currentUsername.isEmpty(); }

signals:
    void connected();
    void disconnected();
    void loginStatusChanged(bool loggedIn);
    void balanceChanged(double newBalance);

private slots:
    void onConnected();
    void onDisconnected();

private:
    void clearUserInfo();
    ClientProduct jsonToProduct(const QJsonObject& json);
    CartItem jsonToCartItem(const QJsonObject& json);

private:
    Client* m_client;
    
    // 当前用户信息
    QString m_currentUsername;
    QString m_currentUserType;
    double m_currentBalance;
    QString m_currentSignature;
    QString m_currentAvatarPath;
};

#endif // CLIENTMANAGER_H
