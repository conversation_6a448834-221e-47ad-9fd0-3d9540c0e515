<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x><y>0</y><width>900</width><height>650</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>电商交易平台</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout_main">
    <item>
     <widget class="QTabWidget" name="tabs">
      <!-- 1. 主页Tab -->
      <widget class="QWidget" name="homeTab">
       <attribute name="title"><string>主页</string></attribute>
       <layout class="QVBoxLayout" name="verticalLayout_home">
        <property name="spacing"><number>10</number></property>
        <property name="margin"><number>10</number></property>

        <!-- 第一行：筛选按钮 -->
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_filter">
          <property name="spacing"><number>8</number></property>
          <item>
           <widget class="QPushButton" name="filterButton">
            <property name="text"><string>筛选商品类型</string></property>
            <property name="minimumSize"><size><width>120</width><height>32</height></size></property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_filterRight">
            <property name="orientation"><enum>Qt::Horizontal</enum></property>
            <property name="sizeType"><enum>QSizePolicy::Expanding</enum></property>
            <property name="sizeHint"><size><width>40</width><height>20</height></size></property>
           </spacer>
          </item>
         </layout>
        </item>

        <!-- 第二行：搜索行 -->
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_searchHome">
          <property name="spacing"><number>8</number></property>
          <item>
           <widget class="QLineEdit" name="homeSearchLineEdit">
            <property name="placeholderText"><string>请输入商品名称关键字</string></property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="searchHomeButton">
            <property name="text"><string>搜索</string></property>
            <property name="minimumSize"><size><width>80</width><height>28</height></size></property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="refreshHomeButton">
            <property name="text"><string>刷新</string></property>
            <property name="minimumSize"><size><width>80</width><height>28</height></size></property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_searchHomeRight">
            <property name="orientation"><enum>Qt::Horizontal</enum></property>
            <property name="sizeType"><enum>QSizePolicy::Expanding</enum></property>
            <property name="sizeHint"><size><width>40</width><height>20</height></size></property>
           </spacer>
          </item>
         </layout>
        </item>

        <!-- 第三行：商品表格 -->
        <item>
         <widget class="QTableWidget" name="homeProductsTable">
          <property name="columnCount"><number>9</number></property>
          <property name="rowCount"><number>0</number></property>
          <!-- 注意：第0列预留“添加”按钮，第1列为ID -->
          <column>
           <property name="text"><string>操作</string></property>
          </column>
          <column>
           <property name="text"><string>ID</string></property>
          </column>
          <column>
           <property name="text"><string>名称</string></property>
          </column>
          <column>
           <property name="text"><string>类别</string></property>
          </column>
          <column>
           <property name="text"><string>原价</string></property>
          </column>
          <column>
           <property name="text"><string>折后价</string></property>
          </column>
          <column>
           <property name="text"><string>库存</string></property>
          </column>
          <column>
           <property name="text"><string>商家</string></property>
          </column>
          <column>
           <property name="text"><string>备注</string></property>
          </column>
         </widget>
        </item>
       </layout>
      </widget>

      <!-- 2. 购物车Tab -->
      <widget class="QWidget" name="cartTab">
       <attribute name="title"><string>购物车</string></attribute>
       <layout class="QVBoxLayout" name="verticalLayout_cart">
        <property name="spacing"><number>10</number></property>
        <property name="margin"><number>10</number></property>

        <!-- 购物车商品表格 -->
        <item>
         <widget class="QTableWidget" name="cartTable">
          <property name="columnCount"><number>9</number></property>
          <property name="rowCount"><number>0</number></property>
          <!-- 第0列：复选框；第1列 ID -->
          <column>
           <property name="text"><string>选中</string></property>
          </column>
          <column>
           <property name="text"><string>ID</string></property>
          </column>
          <column>
           <property name="text"><string>名称</string></property>
          </column>
          <column>
           <property name="text"><string>类别</string></property>
          </column>
          <column>
           <property name="text"><string>原价</string></property>
          </column>
          <column>
           <property name="text"><string>折后价</string></property>
          </column>
          <column>
           <property name="text"><string>购买数量</string></property>
          </column>
          <column>
           <property name="text"><string>商家</string></property>
          </column>
          <column>
           <property name="text"><string>备注</string></property>
          </column>
         </widget>
        </item>

        <!-- 按钮行 -->
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_cartButtons">
         <property name="spacing"><number>12</number></property>
         <item>
          <spacer name="horizontalSpacer_cartLeft">
           <property name="orientation"><enum>Qt::Horizontal</enum></property>
           <property name="sizeType"><enum>QSizePolicy::Expanding</enum></property>
          </spacer>
         </item>
         <item>
          <widget class="QPushButton" name="generateOrderButton">
           <property name="text"><string>生成订单</string></property>
           <property name="minimumSize"><size><width>100</width><height>32</height></size></property>
          </widget>
         </item>
         <!-- 在此处插入新的“删除购物车”按钮 -->
         <item>
          <widget class="QPushButton" name="removeCartButton">
           <property name="text"><string>删除购物车</string></property>
           <property name="minimumSize"><size><width>100</width><height>32</height></size></property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_cartRight">
           <property name="orientation"><enum>Qt::Horizontal</enum></property>
           <property name="sizeType"><enum>QSizePolicy::Expanding</enum></property>
          </spacer>
         </item>
        </layout>
       </item>

       </layout>
      </widget>

      <!-- 3. 商品管理Tab（原“商家管理”改名） -->
      <widget class="QWidget" name="productTab">
       <attribute name="title"><string>商品管理</string></attribute>
       <layout class="QVBoxLayout" name="verticalLayout_product">
        <property name="spacing"><number>10</number></property>
        <property name="margin"><number>10</number></property>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_productButtons">
          <property name="spacing"><number>12</number></property>
          <item>
           <widget class="QPushButton" name="addProductButton">
            <property name="text"><string>添加商品</string></property>
            <property name="minimumSize"><size><width>100</width><height>32</height></size></property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="refreshProductButton">
            <property name="text"><string>刷新列表</string></property>
            <property name="minimumSize"><size><width>100</width><height>32</height></size></property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="updateProductButton">
            <property name="text"><string>更新商品</string></property>
            <property name="minimumSize"><size><width>100</width><height>32</height></size></property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="setDiscountButton">
            <property name="text"><string>设置折扣</string></property>
            <property name="minimumSize"><size><width>100</width><height>32</height></size></property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <widget class="QTableWidget" name="productProductsTable">
          <property name="columnCount"><number>8</number></property>
          <property name="rowCount"><number>0</number></property>
          <column><property name="text"><string>ID</string></property></column>
          <column><property name="text"><string>名称</string></property></column>
          <column><property name="text"><string>类别</string></property></column>
          <column><property name="text"><string>原价</string></property></column>
          <column><property name="text"><string>折后价</string></property></column>
          <column><property name="text"><string>库存</string></property></column>
          <column><property name="text"><string>商家</string></property></column>
          <column><property name="text"><string>备注</string></property></column>
         </widget>
        </item>
       </layout>
      </widget>

      <!-- 4. 我的账户Tab -->
<widget class="QWidget" name="profileTab">
    <attribute name="title"><string>我的账户</string></attribute>
    <layout class="QVBoxLayout" name="verticalLayout_profile">
        <!-- 头像和编辑个人信息按钮 -->
        <item>
            <layout class="QHBoxLayout" name="horizontalLayout_avatarView">
                <item>
                    <widget class="QLabel" name="labelAvatarStatic">
                        <property name="text"><string>头像：</string></property>
                        <property name="minimumSize"><size><width>60</width><height>0</height></size></property>
                    </widget>
                </item>
                <item>
                    <widget class="QLabel" name="avatarLabel">
                        <property name="minimumSize"><size><width>128</width><height>128</height></size></property>
                        <property name="maximumSize"><size><width>128</width><height>128</height></size></property>
                        <property name="scaledContents"><bool>true</bool></property>
                        <property name="pixmap"><pixmap>:/default_avatar.png</pixmap></property>
                    </widget>
                </item>
                <item>
                    <spacer name="horizontalSpacer_avatarRight">
                        <property name="orientation"><enum>Qt::Horizontal</enum></property>
                        <property name="sizeType"><enum>QSizePolicy::Expanding</enum></property>
                    </spacer>
                </item>
            </layout>
        </item>

        <!-- 用户名 -->
        <item>
            <layout class="QHBoxLayout" name="horizontalLayout_username">
                <item>
                    <widget class="QLabel" name="labelUsernameStatic">
                        <property name="text"><string>用户名：</string></property>
                        <property name="minimumSize"><size><width>80</width><height>0</height></size></property>
                    </widget>
                </item>
                <item>
                    <widget class="QLabel" name="usernameLabel">
                        <property name="text"><string>---</string></property>
                        <property name="minimumSize"><size><width>100</width><height>0</height></size></property>
                        <property name="maximumSize"><size><width>100</width><height>50</height></size></property>
                    </widget>
                </item>
                <item>
                    <spacer name="horizontalSpacer_usernameRight">
                        <property name="orientation"><enum>Qt::Horizontal</enum></property>
                        <property name="sizeType"><enum>QSizePolicy::Expanding</enum></property>
                    </spacer>
                </item>
            </layout>
        </item>

        <!-- 余额 -->
        <item>
            <layout class="QHBoxLayout" name="horizontalLayout_balance">
                <item>
                    <widget class="QLabel" name="labelBalanceStatic">
                        <property name="text"><string>余额：</string></property>
                        <property name="minimumSize"><size><width>80</width><height>0</height></size></property>
                    </widget>
                </item>
                <item>
                    <widget class="QLabel" name="balanceLabel">
                        <property name="text"><string>0.00</string></property>
                        <property name="minimumSize"><size><width>100</width><height>0</height></size></property>
                        <property name="maximumSize"><size><width>100</width><height>50</height></size></property>
                    </widget>
                </item>
                <item>
                    <spacer name="horizontalSpacer_balanceRight">
                        <property name="orientation"><enum>Qt::Horizontal</enum></property>
                        <property name="sizeType"><enum>QSizePolicy::Expanding</enum></property>
                    </spacer>
                </item>
            </layout>
        </item>

        <!-- 个性签名 -->
        <item>
            <layout class="QHBoxLayout" name="horizontalLayout_signature">
                <item>
                    <widget class="QLabel" name="labelSignatureStatic">
                        <property name="text"><string>个性签名：</string></property>
                        <property name="minimumSize"><size><width>80</width><height>0</height></size></property>
                    </widget>
                </item>
                <item>
                    <widget class="QLabel" name="signatureLabel">
                        <property name="text"><string>这个人很懒，什么都没写</string></property>
                        <property name="wordWrap"><bool>true</bool></property>
                        <property name="minimumSize"><size><width>300</width><height>100</height></size></property>
                    </widget>
                </item>
                <item>
                    <spacer name="horizontalSpacer_sigRight">
                        <property name="orientation"><enum>Qt::Horizontal</enum></property>
                        <property name="sizeType"><enum>QSizePolicy::Expanding</enum></property>
                    </spacer>
                </item>
            </layout>
        </item>

        <!-- 功能按钮组：充值、修改个人信息、退出登录 -->
        <item>
            <layout class="QHBoxLayout" name="horizontalLayout_profileButtons">
                <item>
                    <spacer name="horizontalSpacer_profileLeft">
                        <property name="orientation"><enum>Qt::Horizontal</enum></property>
                        <property name="sizeType"><enum>QSizePolicy::Expanding</enum></property>
                    </spacer>
                </item>
                <item>
                    <widget class="QPushButton" name="rechargeButton">
                        <property name="text"><string>充值</string></property>
                        <property name="minimumSize"><size><width>120</width><height>35</height></size></property>
                    </widget>
                </item>
                <item>
                    <spacer name="horizontalSpacer_betweenButtons1">
                        <property name="orientation"><enum>Qt::Horizontal</enum></property>
                        <property name="sizeType"><enum>QSizePolicy::Fixed</enum></property>
                        <property name="sizeHint" stdset="0"><size><width>15</width><height>0</height></size></property>
                    </spacer>
                </item>
                <item>
                    <widget class="QPushButton" name="editProfileButton">
                        <property name="text"><string>修改个人信息</string></property>
                        <property name="minimumSize"><size><width>150</width><height>35</height></size></property>
                    </widget>
                </item>
                <item>
                    <spacer name="horizontalSpacer_betweenButtons2">
                        <property name="orientation"><enum>Qt::Horizontal</enum></property>
                        <property name="sizeType"><enum>QSizePolicy::Fixed</enum></property>
                        <property name="sizeHint" stdset="0"><size><width>15</width><height>0</height></size></property>
                    </spacer>
                </item>
                <item>
                    <widget class="QPushButton" name="logoutButton">
                        <property name="text"><string>退出登录</string></property>
                        <property name="minimumSize"><size><width>120</width><height>35</height></size></property>
                    </widget>
                </item>
                <item>
                    <spacer name="horizontalSpacer_profileRight">
                        <property name="orientation"><enum>Qt::Horizontal</enum></property>
                        <property name="sizeType"><enum>QSizePolicy::Expanding</enum></property>
                    </spacer>
                </item>
            </layout>
        </item>
    </layout>
</widget>

     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar"/>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>