# 电商交易平台网络版实现方案

## 项目概述

本项目将原有的单机版电商交易平台改造为网络版，采用传统的C/S架构，使用Qt的QTcpServer和QTcpSocket进行网络通信。

## 架构设计

### 整体架构
```
┌─────────────────┐    Socket通信    ┌─────────────────┐
│   客户端(Client) │ ←──────────────→ │  服务器(Server)  │
│                 │    JSON消息      │                 │
│  - UI界面       │                  │  - 网络监听     │
│  - 网络通信     │                  │  - 消息分发     │
│  - 本地缓存     │                  │  - 业务逻辑     │
│                 │                  │  - 数据持久化   │
└─────────────────┘                  └─────────────────┘
```

### 目录结构
```
Ecommerce_v3/
├── common/                    # 共享模块
│   ├── Protocol.h            # 通信协议常量
│   ├── Message.h/cpp         # 消息格式定义
│   └── NetworkUtils.h/cpp    # 网络工具类
├── server/                   # 服务器端
│   ├── main.cpp             # 服务器启动入口
│   ├── network/             # 网络层
│   │   ├── Server.h/cpp     # TCP服务器
│   │   └── ClientHandler.h/cpp # 客户端连接处理
│   ├── dispatcher/          # 消息分发层
│   │   └── MessageDispatcher.h/cpp
│   ├── src/managers/        # 业务逻辑层（复用原有）
│   │   ├── UserManager.h/cpp
│   │   └── ProductManager.h/cpp
│   ├── src/models/          # 数据模型（复用原有）
│   │   ├── User.h/cpp
│   │   ├── Product.h/cpp
│   │   └── ...
│   └── EcommerceServer.pro  # 服务器项目文件
└── client/                  # 客户端
    ├── main.cpp            # 客户端启动入口
    ├── network/            # 网络通信层
    │   ├── Client.h/cpp    # TCP客户端
    │   └── ClientManager.h/cpp # 客户端管理器
    ├── ui/                 # 用户界面（基于原有UI修改）
    │   ├── LoginDialog.h/cpp/ui
    │   ├── RegisterDialog.h/cpp/ui
    │   └── MainWindow.h/cpp/ui
    └── EcommerceClient.pro # 客户端项目文件
```

## 通信协议

### 消息格式
使用JSON格式进行消息传输，包含消息长度前缀（4字节）+ JSON数据。

**请求格式：**
```json
{
    "action": "login",
    "data": {
        "username": "user1",
        "password": "pass123"
    }
}
```

**响应格式：**
```json
{
    "success": true,
    "message": "登录成功",
    "status": 200,
    "data": {
        "username": "user1",
        "userType": "consumer",
        "balance": 1000.0
    }
}
```

### 支持的操作
- **用户认证**：login, register, logout, change_password
- **用户管理**：recharge_balance, update_profile（包括头像和签名）
- **商品管理**：get_all_products, search_products, add_product, update_product_stock/price/discount
- **购物车**：add_to_cart, remove_from_cart, get_cart, clear_cart
- **订单**：create_order

## 核心组件

### 服务器端

#### 1. Server类
- 监听指定端口（默认8888）
- 接受客户端连接
- 为每个客户端创建ClientHandler
- 管理所有客户端连接

#### 2. ClientHandler类
- 处理单个客户端的连接
- 接收和发送消息
- 维护客户端状态（登录用户信息）
- 心跳检测

#### 3. MessageDispatcher类
- 解析客户端请求
- 调用相应的业务逻辑
- 构造响应消息
- 权限验证

### 客户端

#### 1. Client类
- 连接到服务器
- 发送请求和接收响应
- 同步和异步通信支持
- 心跳维持

#### 2. ClientManager类
- 封装所有业务操作
- 管理用户状态
- 提供简单的API给UI层

#### 3. UI层
- LoginDialog：用户登录
- RegisterDialog：用户注册
- MainWindow：主界面（商品浏览、购物车、商品管理、账户管理）
- AddProductDialog：商品添加对话框（商家专用）
- EditProductDialog：商品修改对话框（商家专用）
- ProfileDialog：个人信息设置对话框（头像、签名）
- OrderDialog：订单生成和支付对话框（完整的订单流程）

## 数据流程

### 登录流程
1. 客户端发送login请求
2. 服务器验证用户名密码
3. 验证成功后返回用户信息
4. 客户端保存用户状态
5. 显示主界面

### 商品浏览流程
1. 客户端发送get_all_products请求
2. 服务器返回所有商品列表
3. 客户端显示在表格中
4. 用户可以搜索、添加到购物车

### 购买流程（与单机版一致）
1. **添加商品到购物车**：
   - 用户选择商品，输入数量（可超过库存）
   - 如果商品已在购物车中，则累加数量
   - 客户端发送add_to_cart请求
   - 服务器在内存中维护购物车

2. **购物车管理**：
   - 显示购物车中的所有商品
   - 用户可以勾选要购买的商品
   - 用户可以在购物车中调整数量（可超过库存）
   - 用户可以移除选中的商品

3. **生成订单**：
   - 用户勾选要购买的商品
   - 点击"生成订单"进行库存验证
   - 检查选中商品的数量是否超过当前库存
   - 库存充足则打开OrderDialog

4. **订单确认和支付**（与单机版一致）：
   - 订单对话框显示商品详情：名称、原价、折后价、数量、小计、商家
   - 显示总金额
   - 点击"支付"完成交易：
     - 服务器验证库存和余额
     - 扣除库存和消费者余额
     - 给商家账户充值相应金额
     - 从购物车中移除已购买商品

## 特性

### 网络特性
- **连接管理**：自动重连、心跳检测
- **消息完整性**：长度前缀确保消息完整接收
- **并发支持**：服务器支持多客户端同时连接
- **错误处理**：网络异常的优雅处理

### 业务特性
- **用户认证**：登录、注册、权限验证
- **商品管理**：商家可以添加、修改商品（支持图书、服装、食品三种类型）
- **购物车**：消费者可以管理购物车
- **订单处理**：完整的下单和支付流程
- **余额管理**：充值和扣费
- **个人信息**：头像上传、个性签名设置
- **界面完整性**：与单机版功能完全一致

### 安全特性
- **密码加密**：使用SHA-256哈希存储密码
- **权限控制**：商家和消费者权限分离
- **会话管理**：服务器端维护用户登录状态

## 编译和运行

### 服务器端
```bash
cd Ecommerce_v3/server
qmake EcommerceServer.pro
make
./EcommerceServer [port]
```

### 客户端
```bash
cd Ecommerce_v3/client
qmake EcommerceClient.pro
make
./EcommerceClient [server_host] [server_port]
```

## 扩展性

该架构设计具有良好的扩展性：
- **协议扩展**：可以轻松添加新的请求类型
- **功能扩展**：可以添加新的业务模块
- **数据库支持**：可以将文件存储替换为数据库
- **负载均衡**：可以部署多个服务器实例
- **安全增强**：可以添加SSL/TLS加密

## 功能完整性对比

### ✅ 已实现的单机版功能
1. **商品管理**：
   - ✅ 商家添加商品（支持图书、服装、食品）
   - ✅ 商家修改商品（价格、库存、折扣）
   - ✅ 商品搜索和浏览

2. **个人信息管理**：
   - ✅ 头像显示和上传
   - ✅ 个性签名设置
   - ✅ 密码修改
   - ✅ 余额充值

3. **购物和订单**：
   - ✅ 购物车管理（添加、移除、数量调整）
   - ✅ 允许添加超过库存的数量到购物车
   - ✅ 购物车商品选择（复选框）
   - ✅ 完整的订单生成流程（选择商品、库存验证、订单确认、支付）
   - ✅ 库存验证和扣减
   - ✅ 余额验证和扣费
   - ✅ 商家收入分配（自动给商家账户充值）
   - ✅ 订单详情显示（与单机版一致的表格结构）

4. **用户界面**：
   - ✅ 登录注册界面
   - ✅ 主界面四个Tab页
   - ✅ 各种功能对话框（包括完整的订单对话框）

### 🎯 网络版新增特性
- **多用户支持**：多个客户端同时连接
- **实时数据同步**：商品信息实时更新
- **权限控制**：服务器端验证用户权限
- **网络通信**：稳定的TCP连接和心跳检测

## 注意事项

1. **数据一致性**：服务器端需要处理并发访问
2. **网络异常**：客户端需要处理网络断开情况
3. **资源管理**：及时清理断开的连接
4. **性能优化**：大量数据传输时的优化
5. **安全考虑**：防止恶意请求和数据泄露
