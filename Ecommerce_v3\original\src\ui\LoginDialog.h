#ifndef LOGINDIALOG_H
#define LOGINDIALOG_H

#include <QDialog>
#include "UserManager.h"

namespace Ui {
class LoginDialog;
}

class LoginDialog : public QDialog
{
    Q_OBJECT

public:
    explicit LoginDialog(UserManager* userMgr, QWidget *parent = nullptr);
    ~LoginDialog();

signals:
    void loginSuccess(User* user);

private slots:
    void on_loginButton_clicked();
    void on_registerButton_clicked();

private:
    Ui::LoginDialog *ui;
    UserManager* m_userMgr;
};

#endif // LOGINDIALOG_H
