#ifndef MESSAGE_H
#define MESSAGE_H

#include <QString>
#include <QJsonObject>
#include <QJsonDocument>
#include <QJsonArray>

class Message
{
public:
    Message();
    Message(const QString& action);
    Message(const QString& action, const QJsonObject& data);
    
    // 从JSON字符串创建消息
    static Message fromJson(const QString& jsonStr);
    
    // 转换为JSON字符串
    QString toJson() const;
    
    // Getters
    QString getAction() const { return m_action; }
    QJsonObject getData() const { return m_data; }
    bool isValid() const { return m_valid; }
    
    // Setters
    void setAction(const QString& action) { m_action = action; }
    void setData(const QJsonObject& data) { m_data = data; }
    void addData(const QString& key, const QJsonValue& value) { m_data[key] = value; }
    
private:
    QString m_action;
    QJsonObject m_data;
    bool m_valid;
};

class Response
{
public:
    Response();
    Response(bool success, const QString& message = "");
    Response(bool success, const QString& message, const QJsonObject& data);
    
    // 从JSON字符串创建响应
    static Response fromJson(const QString& jsonStr);
    
    // 转换为JSON字符串
    QString toJson() const;
    
    // Getters
    bool isSuccess() const { return m_success; }
    QString getMessage() const { return m_message; }
    QJsonObject getData() const { return m_data; }
    int getStatus() const { return m_status; }
    bool isValid() const { return m_valid; }
    
    // Setters
    void setSuccess(bool success) { m_success = success; }
    void setMessage(const QString& message) { m_message = message; }
    void setData(const QJsonObject& data) { m_data = data; }
    void setStatus(int status) { m_status = status; }
    void addData(const QString& key, const QJsonValue& value) { m_data[key] = value; }
    
private:
    bool m_success;
    QString m_message;
    QJsonObject m_data;
    int m_status;
    bool m_valid;
};

#endif // MESSAGE_H
