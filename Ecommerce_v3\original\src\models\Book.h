#ifndef BOOK_H
#define BOOK_H

#include "Product.h"

class Book : public Product
{
public:
    // 构造函数增加 author 字段
    Book(int id, const QString& name, double price, int stock,
         const QString& owner, double discount, const QString& author);

    Category getCategory() const override { return BookCategory; }

    const QString& getAuthor() const { return m_author; }
    void setAuthor(const QString& author) { m_author = author; }

    double getDiscountedPrice() const override
    {
        return m_price * m_discount;
    }

    QString getRemark() const override
    {
        // “备注”显示作者信息
        return QString("作者：%1").arg(m_author);
    }

    QString toCsvString() const override;

private:
    QString m_author;
};

#endif // BOOK_H
