#ifndef PROFILEDIALOG_H
#define PROFILEDIALOG_H

#include <QDialog>

class ClientManager;

namespace Ui {
class ProfileDialog;
}

class ProfileDialog : public QDialog
{
    Q_OBJECT

public:
    explicit ProfileDialog(ClientManager* clientMgr, QWidget *parent = nullptr);
    ~ProfileDialog();

private slots:
    void on_selectAvatarButton_clicked();
    void on_updateButton_clicked();
    void on_cancelButton_clicked();

private:
    void setupUI();
    void loadCurrentInfo();
    void updateAvatarDisplay();

private:
    Ui::ProfileDialog *ui;
    ClientManager* m_clientMgr;
    QString m_selectedAvatarPath;
};

#endif // PROFILEDIALOG_H
